﻿namespace XianYangInterconnect.Service.Interface
{
    public interface IPatientLabservice
    {
        /// <summary>
        /// 同步住院病人信息(MQ调用)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        Task<bool> SyncPatientLab(string hospitalID);


        /// <summary>
        /// 同步患者门诊检验数据
        /// </summary>
        /// <param name="outPatientID"></param>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        Task<bool> SyncPatientTest(string outPatientID, string caseNumber);
    }
}
