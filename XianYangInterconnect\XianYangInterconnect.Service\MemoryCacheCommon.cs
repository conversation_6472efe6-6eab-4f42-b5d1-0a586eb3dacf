﻿using Microsoft.Extensions.Caching.Memory;


namespace XianYangInterconnect.Service
{
    public class MemoryCacheCommon
    {
        private readonly IMemoryCache _memoryCache;

        public MemoryCacheCommon(IMemoryCache memoryCache)
        {
            _memoryCache = memoryCache;
        }
        /// <summary>
        /// 增加Cache
        /// </summary>
        /// <param name="key"></param>
        /// <param name="data"></param>
        /// <param name="slidingExpiration">举例：TimeSpan.FromMinutes(10)</param>
        /// <returns></returns>
        public void SetCacheData(string key, object data, TimeSpan slidingExpiration)
        {
            var cacheEntryOptions = new MemoryCacheEntryOptions()
           .SetSlidingExpiration(slidingExpiration); // 设置滑动过期时间
            _memoryCache.Remove(key);
            _memoryCache.Set(key, data, cacheEntryOptions);
        }
    }
}
