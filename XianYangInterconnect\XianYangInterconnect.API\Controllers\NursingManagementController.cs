﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using XianYangInterconnect.API.Extensions;
using XianYangInterconnect.Common;
using XianYangInterconnect.Service.Interface;

namespace XianYangInterconnect.API.Controllers
{
    /// <summary>
    /// 同步护理管理信息控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/NursingManagement")]
    [EnableCors("any")]
    public class NursingManagementController : ControllerBase
    {
        private readonly INursingManagementService _nursingManagementService;
        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="nursingManagementService"></param>
        public NursingManagementController(
            INursingManagementService nursingManagementService
            )
        {
            _nursingManagementService = nursingManagementService;
        }
        /// <summary>
        /// 获取护理管理排班数据
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetShitData")]
        public async Task<IActionResult> GetShitData(DateTime? startDate, DateTime? endDate)
        {
            var (_, shiftList) = await _nursingManagementService.GetShitData(startDate, endDate);
            var result = new ResponseResult()
            {
                Data = shiftList,
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步护理管理人员信息
        /// </summary>
        /// <param name="employeeIDs">员工编号</param>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncNursingManagementEmployeeBasicData")]
        public async Task<IActionResult> SyncNursingManagementEmployeeBasicData(string[] employeeIDs = null)
        {
            var successFlag = await _nursingManagementService.SyncNursingManagementEmployeeBasicData(employeeIDs);
            var result = new ResponseResult()
            {
                Data = successFlag,
            };
            return result.ToJson();
        }
    }
}
