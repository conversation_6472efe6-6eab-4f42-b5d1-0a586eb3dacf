﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface.Medical;

namespace XianYangInterconnect.Data.Repository.Medical
{
    public class PatientBasicDataRepository : IPatientBasicDataRepository
    {
        private readonly MedicalContext _medicalContext = null;
        public PatientBasicDataRepository(
            MedicalContext medicalContext
            )
        {
            _medicalContext = medicalContext;
        }
        /// <summary>
        /// 获患者姓名
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        public async Task<string> GetPatientNameByChartNo(string chartNo)
        {
            var patient = await _medicalContext.PatientBasicDatas
                .FirstOrDefaultAsync(m => m.ChartNo == chartNo);

            return patient?.PatientName;
        }
    }
}
