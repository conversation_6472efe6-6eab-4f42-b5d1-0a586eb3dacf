﻿using System.Reflection;
using Arch.EntityFrameworkCore.UnitOfWork;
using Hangfire;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using StackExchange.Redis;
using XianYangInterconnect.API.Extensions;
using XianYangInterconnect.API.Filters;
using XianYangInterconnect.Common.SessionCommon;
using XianYangInterconnect.Data;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.API
{
    /// <summary>
    /// Startup
    /// </summary>
    public class Startup
    {
        /// <summary>
        /// Startup
        /// </summary>
        /// <param name="configuration"></param>
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        /// <summary>
        /// Configuration
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        /// ConfigureServices
        /// </summary>
        /// <param name="services"></param>
        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            //连接Redis数据库
            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = Configuration.GetConnectionString("RedisConnection");
                options.InstanceName = "";
            });

            //配置数据库连接 UseMySql使用MySQL  UseSqlServer使用SQL Server DataOutConnection
            services.AddDbContext<DataOutContext>(options => options.UseSqlServer(Configuration.GetConnectionString("DataOutConnection")));
            services.AddScoped<DataOutContext>();
            services.AddUnitOfWork<DataOutContext>();
            services.AddDbContext<HangFireContext>(options => options.UseSqlServer(Configuration.GetConnectionString("HangFireConnection")));
            services.AddScoped<HangFireContext>();
            services.AddUnitOfWork<HangFireContext>();
            services.AddHangfire(x => x.UseSqlServerStorage(Configuration.GetConnectionString("HangFireConnection")));
            services.AddHangfireServer();
            services.AddDbContext<FilesContext>(options => options.UseSqlServer(Configuration.GetConnectionString("FilesConnection")));
            services.AddScoped<FilesContext>();
            services.AddUnitOfWork<FilesContext>();
            services.AddDbContext<MedicalContext>(options => options.UseSqlServer(Configuration.GetConnectionString("MedicalDataInConnection")
                , o => o.UseCompatibilityLevel(100)));
            services.AddScoped<MedicalContext>();
            services.AddUnitOfWork<MedicalContext>();

            services.AddDbContext<HISDbContext>(options =>
        options.UseOracle(Configuration.GetConnectionString("HISConnection"), b => b.UseOracleSQLCompatibility(OracleSQLCompatibility.DatabaseVersion19))
        );
            services.AddUnitOfWork<HISDbContext>();

            services.Configure<SystemConfig>(Configuration.GetSection("Configs"));
            services.Configure<CommonSystemConfig>(Configuration.GetSection("Configs"));

            services.AddHttpContextAccessor();
            services.AddEndpointsApiExplorer();
            services.AddSingleton<ConnectionMultiplexer>(_ => ConnectionMultiplexer.Connect(Configuration.GetConnectionString("RedisConnection")));
            services.AddKeyedSingleton<IDistributedLock, RedisDistributedLock>(LockType.Redis.GetDescription());
            services.AddKeyedSingleton<IDistributedLock, LocalLock>(LockType.Local.GetDescription());
            //增加服务端缓存
            services.AddMemoryCache();

            services.AddMvcCore(options =>
            {
                //添加过滤器
                options.Filters.Add(typeof(AuthorizationAttribute));
                options.Filters.Add<GlobalExceptionsFilter>();
            }).AddNewtonsoftJson(options =>
            {
                //设置时间格式
                options.SerializerSettings.DateFormatHandling = Newtonsoft.Json.DateFormatHandling.MicrosoftDateFormat;
                options.SerializerSettings.DateFormatString = "yyyy-MM-dd HH:mm:ss";
            });

            services.AddCors(options =>
            {
                options.AddPolicy("any", builder =>
                {
                    builder.AllowAnyHeader()
                           .AllowAnyMethod()
                           .AllowCredentials()
                           .SetIsOriginAllowed(origin => true)
                           .SetPreflightMaxAge(TimeSpan.FromSeconds(36000));//指定可以缓存预检请求的响应的时间;
                });
            });
            if (Configuration.GetSection("UseSwagger").Value != "false")
            {
                services.AddSwaggerGen(options =>
                {
                    options.SwaggerDoc("v1", new OpenApiInfo { Title = "XianYangInterconnect.API", Version = "V1.0" });
                    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
                    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                    options.IncludeXmlComments(xmlPath, true);
                });
            }
            services.AddControllers();
        }

        /// <summary>
        /// 设置中间件
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        public void SetupMiddleware(WebApplication app, IWebHostEnvironment env)
        {
            app.UseCors("any");
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.DocumentTitle = "XianYangInterconnect API Doc";
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Interconnect.API V1");
                c.DisplayRequestDuration();
                c.RoutePrefix = string.Empty;
            });
            app.UseHttpsRedirection();
            app.MapControllers();
            app.Run();
        }
    }
}