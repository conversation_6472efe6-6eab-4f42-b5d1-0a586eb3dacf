﻿namespace XianYangInterconnect.Models.Base
{
    public class BaseInfo : ICloneable
    {
        public object Clone()
        {
            return MemberwiseClone();
        }

        /// <summary>
        /// 获取32位GUID
        /// </summary>
        /// <returns></returns>
        public string GetId()
        {
            return Guid.NewGuid().ToString("N");
        }

        /// <summary>
        /// 根据GUID获取16位的唯一字符串
        /// </summary>
        /// <returns></returns>
        public static string Get16Id()
        {
            long i = 1;
            foreach (byte b in Guid.NewGuid().ToByteArray())
                i *= b + 1;
            return string.Format("{0:x}", i - DateTime.Now.Ticks);
        }
    }
}