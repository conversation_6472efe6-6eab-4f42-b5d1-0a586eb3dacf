using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service.Interface
{
    public interface IDictionaryService
    {
        /// <summary>
        /// 获取员工列表
        /// </summary>
        /// <param name="emplCode">员工代码</param>
        /// <param name="emplName">员工姓名</param>
        /// <param name="spellCode">拼音码</param>
        /// <param name="wbCode">五笔</param>
        /// <param name="sexCode">性别</param>
        /// <param name="birthday">出生日期</param>
        /// <param name="posiCode">职务代号</param>
        /// <param name="levlCode">职级代号</param>
        /// <param name="educationCode">学历</param>
        /// <param name="idenno">身份证号</param>
        /// <param name="deptCode">所属科室号</param>
        /// <param name="nurseCellCode">所属护理站</param>
        /// <param name="emplType">人员类型(D医生、N护士、T技师、P药师、C厨师、F收款员、O其他)</param>
        /// <param name="expertFlag">是否专家</param>
        /// <param name="createDateStart">创建开始时间</param>
        /// <param name="createDateEnd">创建结束时间</param>
        /// <returns></returns>
        Task<bool> GetAllEmployeeData(string emplCode, string emplName, string spellCode, string wbCode, string sexCode, string birthday
            , string posiCode, string levlCode, string educationCode, string idenno, string deptCode, string nurseCellCode, string emplType, string expertFlag
            , string createDateStart, string createDateEnd);

        /// <summary>
        /// 根据员工编号获取单个员工
        /// </summary>
        /// <param name="emplId">员工编号</param>
        /// <returns></returns>
        Task<bool> GetOneEmployeeData(string emplId);
        /// <summary>
        /// 获取床位字典
        /// </summary>
        /// <param name="bedCode">病床编码</param>
        /// <param name="bedPrice">床位价格</param>
        /// <param name="bedState">床位状态</param>
        /// <param name="deptCode">病区编码</param>
        /// <param name="roomCode">病室编码</param>
        /// <param name="roomUbedSum">空床数量</param>
        /// <returns></returns>
        Task<bool> GetBedListAsync(string bedCode, string bedPrice, string bedState, string deptCode, string roomCode, string roomUbedSum);
        Task<bool> GetStationAndDepartmentAsync(string branchCode, string deptCode, string deptType, string validState);
        /// <summary>
        /// 获取医嘱字典
        /// </summary>
        /// <returns></returns>
        Task<bool> GetOrderDictAsync();
        /// <summary>
        /// 获取药品字典
        /// </summary>
        /// <returns></returns>
        Task<bool> GetDrugList();

        /// <summary>
        /// 通过插件同步CA
        /// </summary>
        /// <param name="caUserInfo"></param>
        /// <returns></returns>
        Task<bool> SyncCAByPlugin(PluginCAView caUserInfo);
    }
}
