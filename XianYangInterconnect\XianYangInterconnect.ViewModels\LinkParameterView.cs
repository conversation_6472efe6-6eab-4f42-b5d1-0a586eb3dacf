﻿namespace XianYangInterconnect.ViewModels
{
    public class LinkParameterView
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserId { get; set; }
        /// <summary>
        /// 医院编码
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 病区编码
        /// </summary>
        public string StationCode { get; set; }
        /// <summary>
        /// 客户端类型
        /// </summary>
        public string ClientType { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// CareDirect内部逻辑需要参数，默认true即可
        /// </summary>
        public bool IsDialog { get; set; }
        /// <summary>
        /// CareDirect内部逻辑需要参数
        /// </summary>
        public bool ShortCutFlag { get; set; }
        /// <summary>
        /// 要跳转的功能序号
        /// </summary>
        public int FunctionID { get; set; }
        /// <summary>
        /// 患者住院号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime TimeStamp { get; set; }
        /// <summary>
        /// 移动端路由
        /// </summary>
        public string ActionName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string HandoverType { get; set; }

    }
}
