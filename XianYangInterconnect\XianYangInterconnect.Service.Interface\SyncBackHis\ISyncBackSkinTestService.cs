﻿using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service.Interface
{
    public interface ISyncBackSkinTestService
    {
        /// <summary>
        /// 回传患者给药皮试记录
        /// </summary>
        /// <param name="dataView">皮试相关数据view</param>
        /// <returns></returns>
        public Task<bool> SyncBackPatientMedicineScheduleSkinTestData(SyncPatientMedicineScheduleSkinTestView dataView);
    }
}
