﻿using Microsoft.Extensions.Options;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Services.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    public class PatientOperationService : IPatientOperationService
    {
        private readonly IOptions<SystemConfig> _config;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IRequestApiService _requestApiService;
        private readonly ISyncDatasLogRepository _syncDatasLogRepository;
        private readonly ISyncDatasLogServices _syncDatasLogServices;

        #region 常量定义
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 注册手术申请单信息
        /// </summary>
        private const string SHZHY_HLCCC_CREATEOPRETEOPERATIONAPPLY = "SHZHY_HLCCC_createOperationApply";
        /// <summary>
        /// 变更手术申请单信息
        /// </summary>
        private const string SHZHY_HLCCC_UPDATEOPERATIONAPPLY = "SHZHY_HLCCC_updateOperationApply";
        /// <summary>
        /// 注册手术安排信息
        /// </summary>
        private const string SHZHY_HLCCC_CREATEOPERATIONPLAN = "SHZHY_HLCCC_createOperationPlan";
        /// <summary>
        /// 变更手术安排信息
        /// </summary>
        private const string SHZHY_HLCCC_UPDATEOPERATIONPLAN = "SHZHY_HLCCC_updateOperationPlan";
        /// <summary>
        /// 作废手术申请单信息
        /// </summary>
        private const string SHZHY_HLCCC_CANCLEOPERATIONPLAN = "SHZHY_HLCCC_cancleOperationPlan";
        /// <summary>
        /// 取消手术安排信息
        /// </summary>
        private const string SHZHY_HLCCC_DELETEOPETATIONAPPLY = "SHZHY_HLCCC_deleteOperationApply";

        #endregion
        public PatientOperationService(
            IOptions<SystemConfig> options,
            IAppConfigSettingRepository appConfigSettingRepository,
            IRequestApiService requestApiService,
            ISyncDatasLogRepository syncDatasLogRepository,
            ISyncDatasLogServices syncDatasLogServices
            )
        {
            _config = options;
            _appConfigSettingRepository = appConfigSettingRepository;
            _requestApiService = requestApiService;
            _syncDatasLogRepository = syncDatasLogRepository;
            _syncDatasLogServices = syncDatasLogServices;
        }
        public async Task<bool> SyncPatientOperation()
        {
            SyncDataLogInfo logInfo;
            MessageView messageView;
            bool result = false;
            var logIDs = await _syncDatasLogRepository.GetSyncDataByTypeList(_config.Value.HospitalID, ["PatientOperationApply", "PatientOperation"]);
            foreach (var logID in logIDs)
            {
                logInfo = await _syncDatasLogRepository.GetSyncDataByID(logID);
                if (logInfo == null)
                {
                    await _syncDatasLogServices.ModifySyncDataLog(logID, result);
                    continue;
                }
                messageView = ListToJson.ToList<MessageView>(logInfo.SyncData);
                if (messageView == null || messageView.PatientOperationView == null)
                {
                    await _syncDatasLogServices.ModifySyncDataLog(logID, result);
                    continue;
                }
                var operation = messageView.PatientOperationView;
                result = await SyncPatientOperationByApplyNo(operation.CaseNumber, operation.OperationApplyNo, messageView.EventName
                    , operation.ModifyPersonID, operation.ModifyDate);
                await _syncDatasLogServices.ModifySyncDataLog(logID, result);
            }
            return true;
        }

        public async Task<bool> SyncPatientOperationByApplyNo(string caseNumber, string applyNo, string serviceType, string modifyPersonID, string modifyDate)
        {
            OperationParamView operationView = null;
            switch (serviceType)
            {
                case SHZHY_HLCCC_CREATEOPRETEOPERATIONAPPLY:
                case SHZHY_HLCCC_UPDATEOPERATIONAPPLY:
                    operationView = await GetInsertPatientOperationView(applyNo, modifyPersonID);
                    break;
                case SHZHY_HLCCC_CREATEOPERATIONPLAN:
                case SHZHY_HLCCC_UPDATEOPERATIONPLAN:
                    operationView = await GetUpdatePatientOperationView(caseNumber, applyNo, modifyPersonID);
                    break;
                case SHZHY_HLCCC_CANCLEOPERATIONPLAN:
                case SHZHY_HLCCC_DELETEOPETATIONAPPLY:
                    operationView = GetDeletePatientOperationView(caseNumber, applyNo, modifyPersonID, modifyDate);
                    break;
            }
            if (operationView == null) return false;

            var result = await _requestApiService.RequestAPI("SyncPatientOperation", ListToJson.ToJson(operationView), null, 3000);
            return result != null;
        }

        #region 获取手术申请数据
        /// <summary>
        /// 获取手术申请数据
        /// </summary>
        /// <param name="applyNo"></param>
        /// <param name="modifyPersonID"></param>
        /// <returns></returns>
        private async Task<OperationParamView> GetInsertPatientOperationView(string applyNo, string modifyPersonID)
        {
            var hisDataUrl = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetHisShchduleOperation");
            var query = new Dictionary<string, object>
            {
                { "appIds", new List<string>{ applyNo } },
                { "beginTime", "" },
                { "endTime", "" },
                { "notAppIds", new List<string>() }
            };
            var headers = new Dictionary<string, string>
            {
                { "rootId", Guid.NewGuid().ToString("N") },
                {"domain", "SHZHY_HLCCC" },
                { "businessTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                { "operationTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                { "key", "1c09a5c4-d452-4ae1-9f62-7f5a1265fdaa"}
            };
            var hisResult = HttpHelper.SendObjectAsJsonInBody(hisDataUrl, query, headers);
            var response = ListToJson.ToList<HISOperationApplyResponse>(hisResult);
            var hisOperationView = response.data.FirstOrDefault();
            if (hisOperationView == null) return null;

            var patientOperationView = new PatientOperationView()
            {
                ChartNo = hisOperationView.patientNo,
                CaseNumber = hisOperationView.inpatientNo,
                OperationName = string.Join("+", hisOperationView.operTypeList.Select(m => m.operationName)),
                ScheduledDateTime = DateTime.Parse(hisOperationView.operationTime),
                AnesthesiaMethod = hisOperationView.anesMethodName,
                HISOperationNo = hisOperationView.operationApplyNo,
                ModifyPersonID = modifyPersonID,
                PreOP = true
            };
            return new OperationParamView { InsertOperations = [patientOperationView] };
        }
        #endregion

        #region 获取手术更新数据
        /// <summary>
        /// 获取手术更新数据
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="applyNo"></param>
        /// <param name="modifyPersonID"></param>
        /// <returns></returns>
        private async Task<OperationParamView> GetUpdatePatientOperationView(string caseNumber, string applyNo, string modifyPersonID)
        {
            var hisDataUrl = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetHisOperation");
            var query = new OperationDataQuery()
            {
                multiApply = [new DataQuery()
                {
                    applyCode = applyNo,
                    patientId = caseNumber
                }]
            };
            var headers = new Dictionary<string, string>
            {
                { "rootId", Guid.NewGuid().ToString("N") },
                {"domain", "SHZHY_HLCCC" },
                { "businessTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                { "operationTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                { "key", "1c09a5c4-d452-4ae1-9f62-7f5a1265fdaa"}
            };
            var hisResult = HttpHelper.SendObjectAsJsonInBody(hisDataUrl, query, headers);
            var response = ListToJson.ToList<HISOperationDataResponse>(hisResult);
            var hisOperationView = response.data.FirstOrDefault();
            if (hisOperationView == null) return null;
            var patientOperationView = new PatientOperationView()
            {
                CaseNumber = hisOperationView.patientId,
                HISOperationNo = hisOperationView.applyCodeHis,
                OperationDate = DateTime.Parse(hisOperationView.rosteringTime),
                ModifyPersonID = modifyPersonID,
            };
            return new OperationParamView() { UpdateOperations = [patientOperationView] };
        }
        #endregion

        #region 获取手术删除数据
        /// <summary>
        /// 获取手术删除数据
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="applyNo"></param>
        /// <param name="modifyPersonID"></param>
        /// <param name="modifyDate"></param>
        /// <returns></returns>
        private OperationParamView GetDeletePatientOperationView(string caseNumber, string applyNo, string modifyPersonID, string modifyDate)
        {
            var patientOperationView = new PatientOperationView()
            {
                CaseNumber = caseNumber,
                HISOperationNo = applyNo,
                CancelDateTime = DateTime.Parse(modifyDate),
                ModifyPersonID = modifyPersonID,
            };
            return new OperationParamView() { DeleteOperations = [patientOperationView] };
        }
        #endregion
    }
}