﻿using Microsoft.AspNetCore.Http;

namespace XianYangInterconnect.Common.SessionCommon
{
    /// <summary>
    /// SessionExtention
    /// </summary>
    public static class SessionCommonExtention
    {
        private static readonly string token_key = "medical-token";

        /// <summary>
        /// HasToken
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public static bool HasToken(this HttpContext context)
        {
            return context.Request.Headers.ContainsKey(token_key) || context.Request.Cookies.ContainsKey(token_key);
        }

        /// <summary>
        /// 获取Token
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public static string GetCommonToken(this HttpContext context)
        {
            if (context == null)
            {
                return null;
            }
            if (context.Request.Headers.ContainsKey(token_key))
            {
                return context.Request.Headers[token_key].ToString();
            }
            if (context.Request.Cookies.ContainsKey(token_key))
            {
                return context.Request.Cookies[token_key].ToString();
            }
            return null;
        }

        /// <summary>
        /// RemoveToken
        /// </summary>
        /// <param name="context"></param>
        public static void NoAuthorization(this HttpContext context)
        {
            context.Response.StatusCode = 401;
        }

        /// <summary>
        /// SetToken
        /// </summary>
        /// <param name="context"></param>
        /// <param name="token"></param>
        public static void SetToken(this HttpContext context, string token)
        {
            context.Response.Headers.Add(token_key, token);
        }
    }
}