﻿namespace XianYangInterconnect.ViewModels
{

    /// <summary>
    /// 回传生命体征信息view
    /// </summary>
    public class SyncPatientBodySurfaceView
    {
        /// <summary>
        /// 患者主键
        /// </summary>
        public string InpatientID { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 住院流水号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 医院编码
        /// </summary>
        public string HospitalCode { get; set; }

        /// <summary>
        /// 医院名称
        /// </summary>
        public string HospitalName { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>
        public string HpAreaCode { get; set; }

        /// <summary>
        /// 院区名称
        /// </summary>
        public string HpAreaName { get; set; }

        /// <summary>
        /// 患者档案号
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 诊疗号（门诊对应门诊号，住院对应住院号）
        /// </summary>
        public string VisitNo { get; set; }

        /// <summary>
        /// 诊疗流水号
        /// </summary>
        public string VisitSqNo { get; set; }

        /// <summary>
        /// 诊疗类型编码
        /// </summary>
        public string VisitTypeCode { get; set; }

        /// <summary>
        /// 诊疗类型名称
        /// </summary>
        public string VisitTypeName { get; set; }

        /// <summary>
        /// 记录编号（体温单号，作为唯一主键）
        /// </summary>
        public string RecordNo { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// 住院天数
        /// </summary>
        public string InDay { get; set; }

        /// <summary>
        /// 手术（分娩）后天数
        /// </summary>
        public string DaysAfterOperation { get; set; }

        /// <summary>
        /// 床号
        /// </summary>
        public string BedNo { get; set; }

        /// <summary>
        /// 住院次数
        /// </summary>
        public string Times { get; set; }

        /// <summary>
        /// 测量时间（格式：yyyyMMddHHmmss）
        /// </summary>
        public DateTime? TestTime { get; set; }

        /// <summary>
        /// 体温
        /// </summary>
        public string Temperature { get; set; }

        /// <summary>
        /// 体温单位
        /// </summary>
        public string TemperatureUnit { get; set; }

        /// <summary>
        /// 体温测量方式（腋下，肛门，口温，额头）
        /// </summary>
        public string TemperatureMethod { get; set; }

        /// <summary>
        /// 复试（体温）重测体温
        /// </summary>
        public string BodyTempSecond { get; set; }

        /// <summary>
        /// 不升（体温）
        /// </summary>
        public string BodyTempNonRise { get; set; }

        /// <summary>
        /// 降温（体温降温操作后的体温）
        /// </summary>
        public string BodyTempCooling { get; set; }

        /// <summary>
        /// 脉搏
        /// </summary>
        public string Pulse { get; set; }

        /// <summary>
        /// 脉搏单位
        /// </summary>
        public string PulseUnit { get; set; }

        /// <summary>
        /// 心率
        /// </summary>
        public string HeartRate { get; set; }

        /// <summary>
        /// 心率单位
        /// </summary>
        public string HeartRateUnit { get; set; }

        /// <summary>
        /// 心率类别（如窦性心律等）
        /// </summary>
        public string HeartRateType { get; set; }

        /// <summary>
        /// 起搏器心率（次/min）
        /// </summary>
        public string PacemakerHeartRate { get; set; }

        /// <summary>
        /// 疼痛评分（NRS）
        /// </summary>
        public string PainScore { get; set; }

        /// <summary>
        /// 干预后疼痛（NRS）
        /// </summary>
        public string PostInterventionPain { get; set; }

        /// <summary>
        /// 呼吸
        /// </summary>
        public string Breath { get; set; }

        /// <summary>
        /// 呼吸单位
        /// </summary>
        public string BreathUnit { get; set; }

        /// <summary>
        /// 呼吸类型（如:自主呼吸上呼吸机了）
        /// </summary>
        public string BreathingType { get; set; }

        /// <summary>
        /// 是否使用呼吸机（1：是 0：否）
        /// </summary>
        public string IsVentilatorUse { get; set; }

        /// <summary>
        /// 呼吸机监护项目
        /// </summary>
        public string VentilatorMonitorItem { get; set; }

        /// <summary>
        /// 舒张压
        /// </summary>
        public string Dbp { get; set; }

        /// <summary>
        /// 收缩压
        /// </summary>
        public string Sbp { get; set; }

        /// <summary>
        /// 血压单位
        /// </summary>
        public string PresessUnit { get; set; }

        /// <summary>
        /// 血压测量部位
        /// </summary>
        public string BpMeasurementSite { get; set; }

        /// <summary>
        /// 血糖
        /// </summary>
        public string SugarBlood { get; set; }

        /// <summary>
        /// 血糖单位
        /// </summary>
        public string SugarBloodUnit { get; set; }

        /// <summary>
        /// 身高
        /// </summary>
        public string Height { get; set; }

        /// <summary>
        /// 身高单位
        /// </summary>
        public string HeightUnit { get; set; }

        /// <summary>
        /// 身高测量方式（卧床,正常）
        /// </summary>
        public string HeightType { get; set; }

        /// <summary>
        /// 体重
        /// </summary>
        public string Weight { get; set; }

        /// <summary>
        /// 体重单位
        /// </summary>
        public string WeightUnit { get; set; }

        /// <summary>
        /// 体重指数
        /// </summary>
        public string Bmi { get; set; }

        /// <summary>
        /// 体重测量方式（卧床,正常）
        /// </summary>
        public string WeightType { get; set; }

        /// <summary>
        /// 大便次数
        /// </summary>
        public string Defecate { get; set; }

        /// <summary>
        /// 大便方式（如：自主，还是便秘）
        /// </summary>
        public string StoolsPattern { get; set; }

        /// <summary>
        /// 灌肠（泻药）后大便次数
        /// </summary>
        public string LaxativeStoolsNumber { get; set; }

        /// <summary>
        /// 灌肠（泻药）次数
        /// </summary>
        public string LaxativeNumber { get; set; }

        /// <summary>
        /// 小便次数
        /// </summary>
        public string Urinate { get; set; }

        /// <summary>
        /// 小便类型（正常，失禁，导尿管）
        /// </summary>
        public string UrinationPattern { get; set; }

        /// <summary>
        /// 尿量
        /// </summary>
        public string Urine { get; set; }

        /// <summary>
        /// 尿量单位
        /// </summary>
        public string UrineUnit { get; set; }

        /// <summary>
        /// 血氧
        /// </summary>
        public string BloodOxygen { get; set; }

        /// <summary>
        /// 血氧单位
        /// </summary>
        public string BloodOxygenUnit { get; set; }

        /// <summary>
        /// 过敏史
        /// </summary>
        public string AllergyHistory { get; set; }

        /// <summary>
        /// 过敏史标志（1：是 0：否）
        /// </summary>
        public string IsAllergyHistory { get; set; }

        /// <summary>
        /// 皮试结果
        /// </summary>
        public string SkinTest { get; set; }

        /// <summary>
        /// 腹围（cm）
        /// </summary>
        public string AbdominalCircumference { get; set; }

        /// <summary>
        /// 脐带（儿童体温单信息）
        /// </summary>
        public string UmbilicalCord { get; set; }

        /// <summary>
        /// 黄疸（1：是 0：否）
        /// </summary>
        public string Jaundice { get; set; }

        /// <summary>
        /// 是否接种卡介苗（儿童体温单信息）
        /// </summary>
        public string IsBcgVaccine { get; set; }

        /// <summary>
        /// 哺乳方式（儿童体温单信息）
        /// </summary>
        public string LactationMethod { get; set; }

        /// <summary>
        /// 记录时发生的事件（入院、离院、手术、死亡）
        /// </summary>
        public string OccurrenceEvent { get; set; }

        /// <summary>
        /// 发生事件时间（格式：yyyyMMddHHmmss）
        /// </summary>
        public DateTime? OccurrenceEventTime { get; set; }

        /// <summary>
        /// 是否发出手术安全核对表标志（1：是 0：否）
        /// </summary>
        public string IsSurgicalSafetyChecklist { get; set; }

        /// <summary>
        /// 是否发出手术风险评估表标志（1：是 0：否）
        /// </summary>
        public string IsSurgicalRiskAssessment { get; set; }

        /// <summary>
        /// 是否体温单显示（1：是 0：否）
        /// </summary>
        public string IsShowTemp { get; set; }

        /// <summary>
        /// 是否护理记录显示（1：是 0：否）
        /// </summary>
        public string IsShowRecord { get; set; }

        /// <summary>
        /// 是否儿童（1：是 0：否）
        /// </summary>
        public string IsChild { get; set; }

        /// <summary>
        /// 护理记录单单号（外键）
        /// </summary>
        public string RecordNurseNo { get; set; }

        /// <summary>
        /// 备注其他说明
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 测量人编码
        /// </summary>
        public string TestOperCode { get; set; }

        /// <summary>
        /// 测量人名称
        /// </summary>
        public string TestOperName { get; set; }

        /// <summary>
        /// 测量科室编码
        /// </summary>
        public string TestDpetCode { get; set; }

        /// <summary>
        /// 测量人科室名称
        /// </summary>
        public string TestDpetName { get; set; }

        /// <summary>
        /// 是否有效（1：有效 0：作废）
        /// </summary>
        public string ValidFlag { get; set; }

        /// <summary>
        /// 面色
        /// </summary>
        public string FaceColor { get; set; }

        /// <summary>
        /// 患者入量表
        /// </summary>
        public List<NurseInList> NurseInList { get; set; }

        /// <summary>
        /// 患者出量表
        /// </summary>
        public List<NurseOutList> NurseOutList { get; set; }
    }

    /// <summary>
    /// 患者入量表实体类
    /// </summary>
    public class NurseInList
    {
        /// <summary>
        /// 入量项目编码
        /// </summary>
        public string InItemCode { get; set; }

        /// <summary>
        /// 入量项目名称
        /// </summary>
        public string InItemName { get; set; }

        /// <summary>
        /// 入量数量
        /// </summary>
        public string InAmount { get; set; }

        /// <summary>
        /// 入量单位
        /// </summary>
        public string InUnit { get; set; }

        /// <summary>
        /// 入量方式
        /// </summary>
        public string InMode { get; set; }

        /// <summary>
        /// 医嘱号
        /// </summary>
        public string OrderNo { get; set; }

        /// <summary>
        /// 陈述者姓名
        /// </summary>
        public string PresenterName { get; set; }

        /// <summary>
        /// 陈述者与患者的关系代码
        /// </summary>
        public string PresenterPatientRela { get; set; }
    }

    /// <summary>
    /// 患者出量表实体类
    /// </summary>
    public class NurseOutList
    {
        /// <summary>
        /// 出量项目编码
        /// </summary>
        public string OutItemCode { get; set; }

        /// <summary>
        /// 出量项目名称
        /// </summary>
        public string OutItemName { get; set; }

        /// <summary>
        /// 出量数量
        /// </summary>
        public string OutAmount { get; set; }

        /// <summary>
        /// 出量单位
        /// </summary>
        public string OutUnit { get; set; }

        /// <summary>
        /// 出量颜色
        /// </summary>
        public string OutColor { get; set; }

        /// <summary>
        /// 出量性状
        /// </summary>
        public string OutShape { get; set; }

        /// <summary>
        /// 出量方式
        /// </summary>
        public string OutMode { get; set; }

        /// <summary>
        /// 排尿困难标志
        /// </summary>
        public string IsDysuria { get; set; }

        /// <summary>
        /// 陈述者姓名
        /// </summary>
        public string PresenterName { get; set; }

        /// <summary>
        /// 陈述者与患者的关系代码
        /// </summary>
        public string PresenterPatientRela { get; set; }
    }
}
