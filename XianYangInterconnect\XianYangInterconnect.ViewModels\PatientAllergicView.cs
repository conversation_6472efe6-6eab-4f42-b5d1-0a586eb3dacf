﻿namespace XianYangInterconnect.ViewModels
{
    public class PatientAllergicView
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public string AllergyRecordID { get; set; }
        /// <summary>
        /// 过敏类型
        /// </summary>
        public string AllergyType { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? BeginTime { get; set; }

        /// <summary>
        /// 作废时间
        /// </summary>
        public DateTime? CancelDate { get; set; }

        /// <summary>
        /// 作废原因
        /// </summary>
        public string CancelReason { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string AddPerson { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }

        /// <summary>
        /// 作废人
        /// </summary>
        public string DeletePerson { get; set; }

        /// <summary>
        /// 删除原因
        /// </summary>
        public string DeleteReason { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime? DeleteTime { get; set; }

        /// <summary>
        /// 过敏药物名
        /// </summary>
        public string AllergenName { get; set; }

        /// <summary>
        /// 皮试类别编码
        /// </summary>
        public string DrugCode { get; set; }

        /// <summary>
        /// 患者ID
        /// </summary>
        public string ChartNo { get; set; }

        /// <summary>
        /// 就诊患者ID
        /// </summary>
        public string CaseNumber { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 有效性标记
        /// </summary>
        public bool? ValidFlag { get; set; }
    }
}
