﻿namespace XianYangInterconnect.ViewModels
{
    public class OrderItem
    {
        /// <summary>
        /// 非药品术语ID
        /// </summary>
        public string undrugId { get; set; }
        /// <summary>
        /// 术语类型
        /// </summary>
        public string termClassId { get; set; }
        /// <summary>
        /// 术语名称
        /// </summary>
        public string termName { get; set; }
        /// <summary>
        /// 术语英文名称
        /// </summary>
        public string englishName { get; set; }
        /// <summary>
        /// 应用范围
        /// </summary>
        public string visitType { get; set; }
        /// <summary>
        /// 拼音码
        /// </summary>
        public string spellCode { get; set; }
        /// <summary>
        /// 五笔码
        /// </summary>
        public string wbCode { get; set; }
        /// <summary>
        /// 自定义码
        /// </summary>
        public string customCode { get; set; }
        /// <summary>
        /// 规格
        /// </summary>
        public string specs { get; set; }
        /// <summary>
        /// 护理级别标识
        /// </summary>
        public string nurseGrade { get; set; }
        /// <summary>
        /// 病情标识
        /// </summary>
        public string criticalState { get; set; }
        /// <summary>
        /// 性别限制
        /// </summary>
        public string sexLimit { get; set; }
        /// <summary>
        /// 参考价格
        /// </summary>
        public string priceRef { get; set; }
        ///// <summary>
        ///// 可执行可是范围
        ///// </summary>
        //public string execDepts { get; set; }
        ///// <summary>
        ///// 检验可用样品编码
        ///// </summary>
        //public string labSpecimens { get; set; }
        /// <summary>
        /// 对照码
        /// </summary>
        public string mappingCode { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string memo { get; set; }
        /// <summary>
        /// 是否需要收费
        /// </summary>
        public string chargeFlag { get; set; }
        /// <summary>
        /// 用于标记该术语中的物价是否需要到相应科室划价
        /// </summary>
        public string pricingFlag { get; set; }
        /// <summary>
        /// 是否需要终端确认
        /// </summary>
        public string confirmFlag { get; set; }
        /// <summary>
        /// 物价是否审批
        /// </summary>
        public string fincheckFlag { get; set; }
        /// <summary>
        /// 是否提示医生要求患者填写知情同意书
        /// </summary>
        public string consentFlag { get; set; }
        /// <summary>
        /// 申请单类型
        /// </summary>
        public string sheetType { get; set; }
        /// <summary>
        /// 检查大类
        /// </summary>
        public string examClass { get; set; }
        /// <summary>
        /// 检查方法
        /// </summary>
        public string examMethod { get; set; }
        /// <summary>
        /// 检查部位        
        /// </summary>
        public string examPart { get; set; }
    }

    public class OrderDict
    {
        /// <summary>
        /// 数据总数
        /// </summary>
        public int total { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<OrderItem> list { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pageNum { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pageSize { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int size { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int startRow { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int endRow { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pages { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int prePage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int nextPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string isFirstPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string isLastPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string hasPreviousPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string hasNextPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigatePages { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<int> navigatepageNums { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigateFirstPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigateLastPage { get; set; }
    }

    public class HisOrderDictView
    {
        /// <summary>
        /// 
        /// </summary>
        public int code { get; set; }
        /// <summary>
        /// 操作成功！
        /// </summary>
        public string msg { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public OrderDict data { get; set; }
    }

}
