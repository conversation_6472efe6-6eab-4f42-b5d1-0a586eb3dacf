﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface.Medical;
using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Repository.Medical
{
    public class BedListRepository : IBedListRepository
    {
        private readonly MedicalContext _medicalContext = null;
        public BedListRepository(
            MedicalContext medicalContext
            )
        {
            _medicalContext = medicalContext;
        }

        /// <summary>
        /// 最大ID
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<int>> GetBedListMaxID(string hospitalID)
        {
            return await _medicalContext.BedListInfos.Where(m => m.HospitalID == hospitalID).Select(m => m.ID).ToListAsync();
        }

        /// <summary>
        /// 获取床位列表
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public  async Task<List<BedListInfo>> GetBedList(string hospitalID)
        {
            return await _medicalContext.BedListInfos.Where(m => m.HospitalID == hospitalID&&m.DeleteFlag!="*").ToListAsync();
        }
    }
}
