﻿namespace XianYangInterconnect.ViewModels
{
    public class MedicationOrderExecuteView
    {
        /// <summary>
        ///  Log主键ID
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// 医疗院所代码
        /// </summary>
        public string HospitalCode { get; set; }
        /// <summary>
        /// 医院名称
        /// </summary>
        public string HospitalName { get; set; }
        /// <summary>
        /// 患者主键
        /// </summary>
        public string InpatientID { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// 住院流水号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 科室编码
        /// </summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>
        public string DepartmentName { get; set; }
        /// <summary>
        /// 入院次数
        /// </summary>
        public int NumberOfAdmissions { get; set; }
        /// <summary>
        /// 医嘱执行闭环装填吗
        /// </summary>
        public string StatusCode { get; set; }
        /// <summary>
        /// 新增人员工号
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增时间
        /// </summary>
        public DateTime AddDateTime { get; set; }
        /// <summary>
        /// 医嘱码
        /// </summary>
        public string OrderCode { get; set; }
        /// <summary>
        /// 医嘱明细表主键ID
        /// </summary>
        public string PatientOrderDetailID { get; set; }
        /// <summary>
        /// 医嘱主表主键ID
        /// </summary>
        public string PatientOrderMainID { get; set; }
        /// <summary>
        /// 备药日期
        /// </summary>
        public DateTime? PrepareDate { get; set; }
        /// <summary>
        /// 备药时间
        /// </summary>
        public TimeSpan? PrepareTime { get; set; }
        /// <summary>
        /// 备药人ID
        /// </summary>
        public string PrepareEmployeeID { get; set; }
        /// <summary>
        /// 执行日期
        /// </summary>
        public DateTime? PerformDate { get; set; }
        /// <summary>
        /// 执行时间
        /// </summary>
        public TimeSpan? PerformTime { get; set; }
        /// <summary>
        /// 执行人ID
        /// </summary>
        public string PerformEmployeeID { get; set; }
        /// <summary>
        /// 取消原因（可为任意类型，建议根据实际需求指定具体类型）
        /// </summary>
        public string CancelReason { get; set; }
    }
}
