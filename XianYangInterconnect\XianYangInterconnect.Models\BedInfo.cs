﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XianYangInterconnect.Models.Base;

namespace XianYangInterconnect.Models
{
    /// <summary>
    /// 床位信息表
    /// </summary>
    [Serializable]
    [Table("Bed")]
    public class BedInfo : ModifyInfo
    {

        [Key]
        [Column("ID")]
        public int ID { get; set; }
        /// <summary>
        /// 病区代号
        /// </summary>
        [Column("StationCode")]
        public string StationCode { get; set; }
        /// <summary>
        /// 床位标识(床位号码)
        /// </summary>
        [Column("BedNumber")]
        public string BedNumber { get; set; }
        /// <summary>
        /// 床位号(床位代码)
        /// </summary>
        [Column("BedCode")]
        public string BedCode { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int? Sort { get; set; }
        /// <summary>
        /// 是否为虚床 1.虚拟床位 0 真正床位
        /// </summary>
        public int? IsVirtualBed { get; set; }
    }
}