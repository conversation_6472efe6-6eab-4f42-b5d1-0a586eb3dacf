﻿using XianYangInterconnect.Models.HIS;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Data.Interface
{
    public interface IHISPatientOrderRepository
    {
        /// <summary>
        /// 根据caseNumber，minute获取HIS医嘱数据
        /// </summary>
        ///  <param name="caseNumber">caseNumber</param>
        /// <param name="minute">小于当前时间多少分钟</param>
        /// <returns></returns>
        Task<List<HISPatientOrderView>> GetByCasenumber(string caseNumber, int minute);
        /// <summary>
        /// 根据caseNumber，comboNo，获取HIS医嘱
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="comboNo"></param>
        /// <returns></returns>

        Task<List<HISPatientOrderView>> GetByCasenumberAndComboNo(string caseNumber, string comboNo);

        /// <summary>
        /// 获取当天审核的医嘱信息
        /// </summary>
        /// <param name="minute"></param>
        /// <returns></returns>
        Task<List<HISPatientOrderNoView>> GetByConfirmDateTime(int minute);
    }
}
