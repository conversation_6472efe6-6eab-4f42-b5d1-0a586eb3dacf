﻿using NLog;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Services.Interface;

namespace XianYangInterconnect.Services
{
    public class RequestApiService : IRequestApiService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        public readonly IAPISettingService _apiSettingService;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="apiSettingService"></param>
        /// <param name="appConfigSettingRepository"></param>
        public RequestApiService(IAPISettingService apiSettingService
            , IAppConfigSettingRepository appConfigSettingRepository)
        {
            _apiSettingService = apiSettingService;
            _appConfigSettingRepository = appConfigSettingRepository;
        }


        /// <summary>
        /// 根据settingCode调用API并返回数据
        /// </summary>
        /// <param name="settingCode">ApiSetting表中SettingCode字段</param>
        /// <param name="param">参数，post时参数为json格式，get时参数为?a=xxx&b=yyy格式</param>
        /// <param name="token">可不传</param>
        /// <param name="seconds">访问限时</param>
        /// <param name="contentType">默认application/json可不传，短信发送需要传对应的媒体类型</param>
        /// <returns></returns>
        public async Task<object> RequestAPI(string settingCode, string param, string token = null, int seconds = 30, string contentType = "application/json")
        {
            if (string.IsNullOrEmpty(settingCode))
            {
                return null;
            }
            //获取API
            var apiStr = await _apiSettingService.GetAPIAddressByCode(settingCode);
            if (apiStr == null || string.IsNullOrEmpty(apiStr.ApiUrl))
            {
                _logger.Error("获取地址失败！code：" + settingCode);
                return null;
            }
            Dictionary<string, string> httpHeader = null;
            if (!string.IsNullOrEmpty(token))
            {
                httpHeader = new Dictionary<string, string>
                {
                    { "medical-token", token}
                };
            }
            var url = apiStr.ApiUrl.Trim();
            _logger.Info("APiUrl||" + url);
            string result;
            // 根据配置的访问方式访问API-----1 Post,2 Get
            if (apiStr.CallType == 1)
            {
                _logger.Info($"请求参数：{url}数据：{ListToJson.ToJson(param)}httpHeader{httpHeader}");
                // Post方式请求数据
                result = await HttpHelper.HttpPostAsync(url, param, contentType, seconds, httpHeader);
            }
            else
            {
                // Get方式请求数据
                if (!string.IsNullOrEmpty(param))
                {
                    url += param;
                }
                //呼叫api
                result = HttpHelper.HttpGet(url, "application/json", httpHeader);
            }
            return result;
        }

        /// <summary>
        /// 根据settingCode调用AppConfigSettingAPI并返回数据
        /// </summary>
        /// <param name="settingType">AppConfigSetting表中SettingType字段，配置类型</param>
        /// <param name="settingCode">AppConfigSetting表中SettingCode字段</param>
        /// <param name="param">参数，post时参数为json格式，get时参数为?a=xxx&b=yyy格式</param>
        /// <param name="httpType">请求类型，1：Post请求；2：Get请求</param>
        /// <param name="token">可不传</param>
        /// <param name="contentType">默认application/json可不传，短信发送需要传对应的媒体类型</param>
        /// <returns></returns>
        public async Task<string> RequestAPIByAppconfigSetting(string settingType, string settingCode, string param, int httpType, string token = null, string contentType = "application/json", Dictionary<string, string> headers = null)
        {
            var url = await _appConfigSettingRepository.GetConfigSettingValue(settingType, settingCode);
            //var url = "https://localhost:1056/api/8/Employee/GetAllEmployeeData";
            if (string.IsNullOrEmpty(url))
            {
                _logger.Error($"获取AppConfigSetting接口地址失败，SettingType：{settingType}，SettingCode：{settingCode}");
                return null;
            }
            var medicalToken = new Dictionary<string, string> { { "medical-token", token } };
            Dictionary<string, string> httpHeader = !string.IsNullOrEmpty(token) ? medicalToken : null;
            if (headers != null)
            {
                httpHeader = headers;   
            }
            _logger.Info("APiUrl||" + url);
            string result = "";
            try
            {
                // 根据配置的访问方式访问API-----1 Post,2 Get
                if (httpType == 1)
                {
                    result = await HttpHelper.HttpPostAsync(url, param, contentType, 30, httpHeader);
                }
                else
                {
                    if (!string.IsNullOrEmpty(param)) url += param;
                    result = HttpHelper.HttpGet(url, "application/json", httpHeader);
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"请求异常，地址:{url}，异常信息:{ex.ToString()}");
                return null;
            }
            if (string.IsNullOrEmpty(result))
            {
                _logger.Error($"获取接口信息失败，接口返回数据为空，SettingType：{settingType}，SettingCode：{settingCode}，参数Param：{param}");
                return null;
            }
            var response = ListToJson.ToList<ResponseResult>(result);
            if (response == null || response.Data == null)
            {
                _logger.Error($"接口信息转换ResponseResult失败，SettingType：{settingType}，SettingCode：{settingCode}，参数Param：{param}");
                return null;
            }
            return response.Data.ToString();
        }
    }
}
