﻿using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System.Collections.Generic;
using System.Linq;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Data.Interface.Medical;
using XianYangInterconnect.Models.HIS;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Services.Interface;
using XianYangInterconnect.ViewModels;
using XianYangInterconnect.ViewModels.Medical;

namespace XianYangInterconnect.Service
{
    public class BYJDataToPatientMedicineScheduleService : IBYJDataToPatientMedicineScheduleService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IBYJDataRepository _BYJDataRepository;
        private readonly IOptions<SystemConfig> _config;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IStationListRepository _stationListRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IRequestApiService _requestApiService;
        private readonly IMemoryCache _memoryCache;
        private readonly MemoryCacheCommon _memoryCacheCommon;



        public BYJDataToPatientMedicineScheduleService(IBYJDataRepository bYJDataRepository
         , IOptions<SystemConfig> config
            , IInpatientDataRepository inpatientDataRepository
            , IDepartmentListRepository departmentListRepository
            , IStationListRepository stationListRepository
            , IRequestApiService requestApiService
            , IMemoryCache memoryCache
            , MemoryCacheCommon memoryCacheCommon
            )
        {
            _BYJDataRepository = bYJDataRepository;
            _config = config;
            _inpatientDataRepository = inpatientDataRepository;
            _stationListRepository = stationListRepository;
            _departmentListRepository = departmentListRepository;
            _requestApiService = requestApiService;
            _memoryCache = memoryCache;
            _memoryCacheCommon = memoryCacheCommon;
        }


        public async Task<bool> SyncBYJData(int minute)
        {
            (_, var syncBarcode) = await SyncBYJDataByOpertime(minute);
            return await SyncBYJDataByUseTime(syncBarcode, minute);
        }

        /// <summary>
        /// 获取需要同步的BarCode
        /// </summary>
        /// <param name="key"></param>
        /// <param name="barcodes"></param>
        /// <returns></returns>
        private List<string> GetSyncBarCodes(string key, List<string> barcodes)
        {
            //获取缓存中存储的Key
            var datas = _memoryCache.Get(key);
            if (datas == null)
            {
                return barcodes;
            }
            string json = JsonConvert.SerializeObject(datas);
            var cachebarcodes = ListToJson.ToList<List<string>>(json);
            barcodes = barcodes.Where(m => !cachebarcodes.Contains(m)).ToList();
            return barcodes;
        }
        /// <summary>
        ///  根据摆药机摆要时间同步
        /// </summary>
        /// <param name="minute"></param>
        /// <returns>是否成功，同步的barCode</returns>
        private async Task<(bool, List<string>)> SyncBYJDataByOpertime(int minute)
        {
            var syncBarCode = new List<string>();
            var memoryCachKey = "BYJData_Opertime";
            var byjdatas = await _BYJDataRepository.GetAsync(minute);

            if (byjdatas.Count == 0)
            {
                _logger.Info($"SyncBYJDataByOpertime没有获取到摆药机数据");
                return (true, syncBarCode);
            }
            var barCodes = byjdatas.Select(m => m.BARCODE).ToList();
            syncBarCode = GetSyncBarCodes(memoryCachKey, barCodes);
            if (syncBarCode.Count <= 0)
            {
                return (true, syncBarCode);
            }
            ///获取需要同步的数据
            var syncbyjdatas = byjdatas.Select(m => syncBarCode.Contains(m.BARCODE)).ToList();
            var resultFlag = await SyncPatientMedicineSchedule(byjdatas);
            if (!resultFlag)
            {
                return (false, syncBarCode);
            }
            _memoryCacheCommon.SetCacheData(memoryCachKey, barCodes, TimeSpan.FromMinutes(30));
            return (true, syncBarCode);
        }

        /// <summary>
        /// 进行数据同步
        /// </summary>
        /// <param name="byjdatas"></param>
        /// <returns></returns>
        private async Task<bool> SyncPatientMedicineSchedule(List<BYJReturnDataInfo> byjdatas)
        {
            var formatData = await FormantData(byjdatas);
            if (formatData.Count == 0)
            {
                _logger.Error($"清洗摆药机数据失败,hisData={ListToJson.ToJson(byjdatas)}");
                return false;
            }
            _logger.Info($"获取待同步数据{formatData.Count()}条，明细：{ListToJson.ToJson(formatData)}");
            var casenumbers = formatData.Select(m => m.CaseNumber).Distinct().ToList();
            foreach (var item in casenumbers)
            {
                var formatDataTemp = formatData.Where(m => m.CaseNumber == item).ToList();
                // 调用药嘱标准接口同步数据
                await _requestApiService.RequestAPI("SyncPatientMedicineSchedule", ListToJson.ToJson(formatDataTemp), null, 300);
            }
            return true;
        }

        /// <summary>
        /// 根据预计执行时间同步
        /// </summary>
        /// <param name="unbarCodes">需要排除的Barcode</param>
        /// <param name="minute"></param>
        /// <returns></returns>
        private async Task<bool> SyncBYJDataByUseTime(List<string> unbarCodes, int minute)
        {
            var memoryCachKey = "BYJBarcodes_UseTime";
            _logger.Info($"开始获取SyncBYJDataByUseTime数据,从当前时间减{minute}分钟开始获取");
            var viewBarcodes = await _BYJDataRepository.GetBarcodes(minute);
            _logger.Info($"结束获取SyncBYJDataByUseTime数据从当前时间减{minute}分钟开始获取");
            if (viewBarcodes.Count <= 0)
            {
                return true;
            }
            var barcodesTemp = viewBarcodes;
            //获取缓存中存储的Key
            var datas = _memoryCache.Get(memoryCachKey);
            if (datas != null)
            {
                string json = JsonConvert.SerializeObject(datas);
                var cachebarcodes = ListToJson.ToList<List<string>>(json);
                barcodesTemp = viewBarcodes.Where(m => !cachebarcodes.Contains(m)).ToList();
                barcodesTemp = barcodesTemp.Where(m => !unbarCodes.Contains(m)).ToList();
            }
            if (barcodesTemp.Count <= 0)
            {
                return true;
            }
            var byjdatas = await _BYJDataRepository.GetDataByBarCodes(barcodesTemp);
            if (byjdatas.Count == 0)
            {
                _logger.Error($"没有获取到摆药机数据");
                return false;
            }
            var resultFlag = await SyncPatientMedicineSchedule(byjdatas);
            if (!resultFlag)
            {
                return false;
            }
            _memoryCacheCommon.SetCacheData(memoryCachKey, viewBarcodes, TimeSpan.FromMinutes(30));
            return true;
        }

        /// <summary>
        /// 格式化给药数据
        /// </summary>
        /// <param name="byjdatas"></param>
        /// <returns></returns>
        private async Task<List<PatientMedicineScheduleView>> FormantData(List<BYJReturnDataInfo> byjdatas)
        {
            var statinlist = await _stationListRepository.GetStationList("8");
            var departmentList = await _departmentListRepository.GetDepartmentList("8");
            var casenumbers = byjdatas.Select(m => m.INPATIENT_NO).Distinct().ToList();
            var inpatientdatas = await _inpatientDataRepository.GetInpatientDatas(casenumbers);
            var cccData = new List<PatientMedicineScheduleView>();
            foreach (var item in byjdatas)
            {
                var cccMedicine = new PatientMedicineScheduleView();
                var inpatientdata = inpatientdatas.FirstOrDefault(m => m.CaseNumber == item.INPATIENT_NO);
                if (inpatientdata == null)
                {
                    continue;
                }
                var statinTemp = statinlist.FirstOrDefault(m => m.ID == inpatientdata.StationID);
                if (statinTemp == null)
                {
                    continue;
                }
                var departmentTemp = departmentList.FirstOrDefault(m => m.ID == inpatientdata.DepartmentListID);
                if (departmentTemp == null)
                {
                    continue;
                }

                var drugExec = await _BYJDataRepository.GetByBarcode(item.BARCODE);

                cccMedicine.PatientMedicineScheduleID = Guid.NewGuid().ToString("N");
                cccMedicine.SourceType = 2;
                //患者信息
                cccMedicine.CaseNumber = inpatientdata.CaseNumber;
                cccMedicine.ChartNo = inpatientdata.ChartNo;
                cccMedicine.StationCode = statinTemp.StationCode;
                //药品信息
                cccMedicine.PatientOrderMainID = item.EXEC_SQN;
                cccMedicine.PatientOrderDetailID = item.APPLY_NUMBER;

                cccMedicine.OrderCode = item.DRUG_CODE;
                cccMedicine.DrugCode = item.DRUG_CODE;
                cccMedicine.HISOrderGroupNo = item.BARCODE;
                cccMedicine.HISOrderSort = 0;
                cccMedicine.OrderType = "1";
                //三查七对-药品名称及其备注
                cccMedicine.OrderContent = item.DRUG_NAME;
                cccMedicine.OrderDescription = "";
                //三查七对-药品规格剂量
                cccMedicine.AmountText = "";
                cccMedicine.DrugSpec = item.MEDUNIT;
                if (decimal.TryParse(item.DOSE, out var orderDose))
                {
                    cccMedicine.OrderDose = orderDose;
                    cccMedicine.TotalVolume = orderDose;
                }
                cccMedicine.Unit = item.MEDUNIT;
                //三查七对-药品用法给药途径
                cccMedicine.Location = "";
                cccMedicine.OrderRule = "P.O";

                cccMedicine.GroupID = item.BARCODE;
                cccMedicine.ScheduleDate = item.OPERTIME.Date;
                cccMedicine.ScheduleTime = new TimeSpan(item.OPERTIME.TimeOfDay.Hours, 0, 0);
                if (drugExec != null )
                {
                    DateTime scheduleDateTime = drugExec.USE_TIME;
                    cccMedicine.ScheduleDate = scheduleDateTime.Date;
                    cccMedicine.ScheduleTime = new TimeSpan(scheduleDateTime.TimeOfDay.Hours, 0, 0);
                }
                cccMedicine.Frequency = "st";
                //三查七对-注意
                cccMedicine.DrugAttention = "";
                //状态
                cccMedicine.OrderStatus = 2;
                cccMedicine.AddDate = DateTime.Now;
                cccMedicine.AddEmployeeID = drugExec != null ? drugExec.DOC_CODE : item.OPERCODE;
                cccMedicine.HighRiskFlag = "";
                cccMedicine.FirstDayFlag = "";
                cccMedicine.Speed = "";
                cccMedicine.BillingAttribution = "0";
                cccData.Add(cccMedicine);
            }
            return cccData;
        }
    }
}
