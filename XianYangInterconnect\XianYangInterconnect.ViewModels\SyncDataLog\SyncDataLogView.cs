﻿
namespace XianYangInterconnect.ViewModels
{
    public class SyncDataLogView
    {
        /// <summary>
        /// 医院标识
        /// </summary>       
        public string HospitalID { get; set; }
        /// <summary>
        /// 数据同步的名称，如bedList,StationList 等
        /// </summary>
        public string SyncDataType { get; set; }
        /// <summary>
        /// 事件类型（入院、出院、转入、转出）
        /// </summary>
        public string EventName { get; set; }

        /// <summary>
        /// 患者唯一号
        /// </summary>
        public string CaseNumber { get; set; }

        /// <summary>
        /// 操作者
        /// </summary>
        public string OperationEmployeeID { get; set; }
        /// <summary>
        /// 需要同步的数据
        /// </summary>
        public object SyncData { get; set; }

        public SyncDataLogView()
        {
            HospitalID = "";
            SyncDataType = "";
            EventName = "";
            CaseNumber = "";
            OperationEmployeeID = "";
            SyncData = "";
        }

    }
}
