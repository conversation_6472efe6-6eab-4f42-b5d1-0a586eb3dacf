﻿using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service.SyncBackHis
{
    public abstract class SyncBackCommon
    {
        /// <summary>
        /// 就诊类型 01、02、03、04、05、09
        /// 1门诊 2急诊 3住院 4体检 5互联网 9其他
        /// </summary>
        internal const string VISIT_TYPE_CODE = "03";
        internal const string VISIT_TYPE_NAME = "住院";
        /// <summary>
        /// 配置类别
        /// </summary>
        internal const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 同步回传时间的格式
        /// </summary>
        internal const string SYNC_DATETIME_FORMAT = "yyyyMMddHHmmss";
        /// <summary>
        /// 默认命名空间前缀（读取的xml文档的命名空间前缀（xmlNamespaceManager中使用的默认的命名空间前缀））
        /// 随便命名的
        /// </summary>
        internal const string NAMESPACE_PREFIX = "hl7";
        /// <summary>
        /// xml样例模板中的命名空间键值对
        /// </summary>
        internal readonly Dictionary<string, string> namespaceDict = new()
        {
            { NAMESPACE_PREFIX, "urn:hl7-org:v3" },
            { "xsi", "http://www.w3.org/2001/XMLSchema-instance"}
        };
        /// <summary>
        /// 发送者编码
        /// </summary>
        internal const string SENDER = "SHZHY_HLCCC";
        /// <summary>
        /// 接受着编码
        /// </summary>
        internal const string RECEIVER = "SHZHY_HLCCC";

    }
}
