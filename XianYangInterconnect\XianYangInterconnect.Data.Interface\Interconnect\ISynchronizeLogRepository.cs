﻿using XianYangInterconnect.Models.Interconnect;

namespace XianYangInterconnect.Data.Interface.Interconnect
{
    public interface ISynchronizeLogRepository
    {
        /// <summary>
        /// 取得失败日志
        /// </summary>
        /// <returns></returns>
        Task<List<SynchronizeLogInfo>> GetErrorLog();

        /// <summary>
        /// 取得重复次数小于等于参数的失败日志
        /// </summary>
        /// <returns></returns>
        Task<List<SynchronizeLogInfo>> GetErrorLog(byte retryTimes);
        /// <summary>
        /// 取得指定API重复次数小于等于参数的失败日志
        /// </summary>
        /// <param name="retryTimes"></param>
        /// <param name="url"></param>
        /// <returns></returns>
        Task<List<SynchronizeLogInfo>> GetErrorLogByUrl(byte retryTimes, string url);
        /// <summary>
        /// 获取未同步数量
        /// </summary>
        /// <returns></returns>
        Task<int> GetUnSyncCount();
        /// <summary>
        /// 根据病人ID获取同步记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        Task<List<SynchronizeLogInfo>> GetSyncLogByInpatientIDAsync(string inpatientID);

        /// <summary>
        /// 获取一条没有呼叫成功的日志
        /// </summary>
        /// <param name="logID"></param>
        /// <returns></returns>
        Task<SynchronizeLogInfo> GetErrorLogByLogID(string logID);
    }
}
