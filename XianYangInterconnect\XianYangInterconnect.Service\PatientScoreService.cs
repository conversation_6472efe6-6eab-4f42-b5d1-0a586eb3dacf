﻿using NLog;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Services.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    public class PatientScoreService : IPatientScoreService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISyncDatasLogRepository _syncDatasLogRepository;
        private readonly ISyncDatasLogServices _syncDatasLogServices;
        private readonly IRequestApiService _requestApiService;

        public PatientScoreService(ISyncDatasLogRepository syncDatasLogRepository
            , ISyncDatasLogServices syncDatasLogServices
            , IRequestApiService requestApiService)
        {
            _syncDatasLogRepository = syncDatasLogRepository;
            _syncDatasLogServices = syncDatasLogServices;
            _requestApiService = requestApiService;
        }

        /// <summary>
        /// 同步患者VTE数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientVTE(string hospitalID)
        {
            var successFlag = true;
            var patientVTEViews = new List<PatientVTEView>();
            // 获取需同步的数据
            var logIDs = await _syncDatasLogRepository.GetSyncDataByDataType(hospitalID, "VTE");
            foreach (var logID in logIDs)
            {
                var patientVTEView = await GetPatientVTE(logID);
                if (patientVTEView == null)
                {
                    successFlag = false;
                    _logger.Error($"同步患者VTE数据失败，SyncDatasLogID={logID}");
                    await _syncDatasLogServices.ModifySyncDataLog(logID, false);
                    continue;
                }
                patientVTEViews.Add(patientVTEView);
            }
            await _requestApiService.RequestAPI("SyncPatientVTE", ListToJson.ToJson(patientVTEViews), null, 300);
            return successFlag;
        }

        /// <summary>
        /// 获取患者VTE数据
        /// </summary>
        /// <param name="logID"></param>
        /// <returns></returns>
        private async Task<PatientVTEView> GetPatientVTE(int logID)
        {
            PatientVTEView patientVTEView = null;
            var logInfo = await _syncDatasLogRepository.GetSyncDataByID(logID);
            try
            {
                var dataJsonList = ListToJson.ToList<MessageView>(logInfo.SyncData);
                if (dataJsonList == null || dataJsonList.ScoreInFo == null)
                {
                    return patientVTEView;
                }
                var scoreInFo = dataJsonList.ScoreInFo;
                if (string.IsNullOrEmpty(scoreInFo.CaseNumber) || string.IsNullOrEmpty(scoreInFo.RecordId)
                    || string.IsNullOrEmpty(scoreInFo.Point) || !int.TryParse(scoreInFo.Point, out int scorePoint))
                {
                    return patientVTEView;
                }
                patientVTEView = new PatientVTEView
                {
                    CaseNumber = dataJsonList.ScoreInFo.CaseNumber,
                    RecordListID = 45,//暂时默认RecordListID=45，Padua静脉血栓风险评估
                    ScorePoint = scorePoint
                };
            }
            catch (Exception ex)
            {
                _logger.Error("Json转换异常:" + ex.ToString() + "Json字符串:" + logInfo.SyncData);
                return patientVTEView;
            }
            return patientVTEView;
        }
    }
}