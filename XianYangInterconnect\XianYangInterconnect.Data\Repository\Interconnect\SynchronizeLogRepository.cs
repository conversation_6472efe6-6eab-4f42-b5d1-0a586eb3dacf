﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface.Interconnect;
using XianYangInterconnect.Models.Interconnect;

namespace XianYangInterconnect.Data.Repository.Interconnect
{
    public class SynchronizeLogRepository : ISynchronizeLogRepository
    {
        private DataOutContext _dataOutConnection = null;
        public SynchronizeLogRepository(DataOutContext db)
        {
            _dataOutConnection = db;
        }

        public async Task<List<SynchronizeLogInfo>> GetErrorLog()
        {
            return await _dataOutConnection.SynchronizeLogInfos.Where(m => m.SuccessFlag == "").ToListAsync();
        }

        public async Task<List<SynchronizeLogInfo>> GetErrorLog(byte retryTimes)
        {
            return await _dataOutConnection.SynchronizeLogInfos.Where(m => m.SuccessFlag == ""
            && m.RetryTimes <= retryTimes
            && m.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<SynchronizeLogInfo>> GetErrorLogByUrl(byte retryTimes, string url)
        {
            return await _dataOutConnection.SynchronizeLogInfos.Where(m => m.SuccessFlag == ""
            && m.RetryTimes <= retryTimes
            && m.DeleteFlag != "*"
            && m.ApiUrl == url).ToListAsync();
        }

        public async Task<int> GetUnSyncCount()
        {
            return await _dataOutConnection.SynchronizeLogInfos.CountAsync(m => m.SuccessFlag == ""
            && m.DeleteFlag != "*");
        }
        /// <summary>
        /// 获取指定病人的同步记录
        /// </summary>
        /// <param name="inpatientID"></param>
        /// <returns></returns>
        public async Task<List<SynchronizeLogInfo>> GetSyncLogByInpatientIDAsync(string inpatientID)
        {
            return await _dataOutConnection.SynchronizeLogInfos.Where(m => m.InpatientID == inpatientID && m.DeleteFlag != "*").
                Select(m => new SynchronizeLogInfo
                {
                    InpatientID = m.InpatientID,
                    ApiUrl = m.ApiUrl,
                    SuccessFlag = m.SuccessFlag,
                    Arguments = m.Arguments,

                }).Distinct().ToListAsync();
        }
        public async Task<SynchronizeLogInfo> GetErrorLogByLogID(string logID)
        {
            return await _dataOutConnection.SynchronizeLogInfos.Where(m => m.ID == logID
            && m.DeleteFlag != "*" && m.SuccessFlag == "").FirstOrDefaultAsync();
        }
    }
}
