﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Models;
using XianYangInterconnect.Models.Interconnect;
using XianYangInterconnect.Models.SyncDataLog;

namespace XianYangInterconnect.Data.Context
{
    public partial class DataOutContext : DbContext
    {
        public DataOutContext(DbContextOptions<DataOutContext> options)
           : base(options)
        { }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
        }

        public DbSet<BedInfo> BedInfos { get; set; }

        /// <summary>
        /// 数据同步日志记录
        /// </summary>
        public DbSet<SyncDataLogInfo> SyncDataLogInfos { get; set; }

        /// <summary>
        /// 同步日志
        /// </summary>
        public DbSet<SyncLogInfo> SyncLogInfos { get; set; }

        /// <summary>
        /// 病区分组字典
        /// </summary>
        public DbSet<StationGroupListInfo> StationGroupListInfos { get; set; }
        /// <summary>
        /// 调用日志表
        /// </summary>
        public DbSet<SynchronizeLogInfo> SynchronizeLogInfos { get; set; }
        /// <summary>
        /// 同步数据库中的配置信息字典
        /// </summary>
        public DbSet<InterconnectAppConfigSettingInfo> InterconnectAppConfigSettingInfos { get; set; }

    }
}