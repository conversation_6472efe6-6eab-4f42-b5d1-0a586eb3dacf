﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using XianYangInterconnect.API.Extensions;
using XianYangInterconnect.Common;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.API.Controllers
{
    /// <summary>
    /// 加解密
    /// </summary>
    [Produces("application/json")]
    [Route("api/SendData")]
    [EnableCors("any")]
    public class SendDataController : ControllerBase
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOptions<SystemConfig> _config;
        /// <summary>
        /// 
        /// </summary>
        public SendDataController(IOptions<SystemConfig> options)
        {
            _config = options;
        }
        /// <summary>
        /// 数据加密
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("DataEncryption")]
        public IActionResult DataEncryption([FromBody] object datas)
        {
            //返回加密信息
            _logger.Info("对信息进行加密:" + datas.ToString());
            var result = new ResponseResult();
            if (datas == null)
            {
                result.Data = "";
                result.Message = "数据为空";
                return result.ToJson();
            }
            //转Json
            var dataJson = JsonConvert.SerializeObject(datas);

            var linkParameterView = ListToJson.ToList<LinkParameterView>(dataJson);
            if (linkParameterView.FunctionID == 102)
            {
                linkParameterView.FunctionID = 97;
                linkParameterView.HandoverType = "";
                linkParameterView.HospitalID = _config.Value.HospitalID;
            }
            dataJson = ListToJson.ToJson(linkParameterView);

            //加密
            result.Data = EncryptionAndDecryption.Encryption(dataJson);
            //返回加密信息
            _logger.Info("加密信息:" + result.Data);
            return result.ToJson();
        }
        /// <summary>
        /// 信息加密接口
        /// </summary>
        /// <param name="userId">用户工号</param>
        /// <param name="hospitalID">医院序号</param>
        /// <param name="stationCode">病区Code</param>
        /// <param name="clientType">客户端类型</param>
        /// <param name="language">语言</param>
        /// <param name="isDialog"></param>
        /// <param name="shortCutFlag"></param>
        /// <param name="functionID">菜单ID</param>
        /// <param name="caseNumber">住院流水号</param>
        /// <param name="timeStamp">时间戳</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetDataEncryption")]
        public IActionResult GetDataEncryption(string userId, int hospitalID, string stationCode, int clientType, int language, bool isDialog, bool shortCutFlag, int functionID, string caseNumber = null, string timeStamp = null)
        {
            var encryptionDatas = new
            {
                userId,
                hospitalID,
                stationCode,
                clientType,
                language,
                isDialog,
                shortCutFlag,
                functionID,
                caseNumber,
                timeStamp
            };

            _logger.Info("对信息进行加密:" + encryptionDatas.ToString());
            var result = new ResponseResult();

            // 剩余逻辑保持不变...
            var dataJson = JsonConvert.SerializeObject(encryptionDatas);
            var linkParameterView = ListToJson.ToList<LinkParameterView>(dataJson);

            if (linkParameterView.FunctionID == 102)
            {
                linkParameterView.FunctionID = 97;
                linkParameterView.HandoverType = "";
                linkParameterView.HospitalID = _config.Value.HospitalID;
            }

            dataJson = ListToJson.ToJson(linkParameterView);
            result.Data = EncryptionAndDecryption.Encryption(dataJson);

            _logger.Info("加密信息:" + result.Data);
            return result.ToJson();
        }
    }
}
