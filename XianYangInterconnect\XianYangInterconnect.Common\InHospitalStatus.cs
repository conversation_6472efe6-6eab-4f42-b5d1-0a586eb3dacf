﻿namespace XianYangInterconnect.Common
{
    /// <summary>
    /// 患者在院状态常量
    ///
    ///    10	预入院 清单不呈现
    ///    20	在院\入院 清单不呈现
    ///    30	在科\入科 清单呈现
    ///    40	预出院 清单呈现
    ///    50	出科\不在科 清单不呈现
    ///    60	实际出院 清单不呈现
    ///    70	出院未结算 清单不呈现
    ///    80	出院已结算 清单不呈现
    ///    90	出院召回 清单不呈现
    /// </summary>
    public class InHospitalStatus
    {
        /// <summary>
        /// 在院且清单呈现状态
        /// </summary>
        public static readonly List<int> INHOSPITALLIST = [30, 40];

        /// <summary>
        /// 出院患者状态
        /// </summary>
        public static readonly List<int> DISCHARGEDHOSPITALLIST = [60, 70, 80, 90];

        /// <summary>
        /// 可以进行补录的状态
        /// </summary>
        public static readonly List<int> REFILLFLAGSTARTUS = [50, 60, 70, 80, 90];
    }
}