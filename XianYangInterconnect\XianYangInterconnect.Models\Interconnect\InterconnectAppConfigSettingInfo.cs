﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XianYangInterconnect.Models.Base;

namespace XianYangInterconnect.Models
{
    [Serializable]
    [Table("AppConfigSetting")]
    public class InterconnectAppConfigSettingInfo : ModifyInfo
    {
        /// <summary>
        /// App配置表序号
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int AppConfigSettingID { get; set; }

        /// <summary>
        /// 医疗院所代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string SettingType { get; set; }

        /// <summary>
        /// 配置码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SettingCode { get; set; }

        /// <summary>
        /// 配置值
        /// </summary>

        public string SettingValue { get; set; }

        /// <summary>
        /// API属于那个系统。Medical、External
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string SystemType { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string Description { get; set; }

        /// <summary>
        /// 负责人，填写负责人及联系方式
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string PersonInCharge { get; set; }
    }
}
