﻿namespace XianYangInterconnect.Data.Interface
{
    public interface IInterconnectAppConfigSettingRepository : ICacheRepository
    {
        /// <summary>
        /// 根据settingType，settingCode从数据库获取配置信息
        /// </summary>
        /// <param name="settingType"></param>
        /// <param name="settingCode"></param>
        /// <returns></returns>
        Task<string> GetConfigSettingValue(string settingType, string settingCode);
    }
}
