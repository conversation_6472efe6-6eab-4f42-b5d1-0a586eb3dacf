﻿namespace XianYangInterconnect.ViewModels
{
    public class SignView
    {
        /// <summary>
        /// 签名数据id
        /// </summary>
        public string SignDataID { get; set; }

        /// <summary>
        /// 签名结果
        /// </summary>
        public string SignResult { get; set; }

        /// <summary>
        /// 证书
        /// </summary>
        public string SignCert { get; set; }

        /// <summary>
        /// 状态（UNSIGN：待签，FINISH：已签，EXPIRE：过期，EVOKE：签名任务被服务端撤销
        /// </summary>
        public string JobStatus { get; set; }

        /// <summary>
        /// 用户编号 id
        /// </summary>
        public string MsspId { get; set; }

        /// <summary>
        /// 印章图片 base64
        /// </summary>
        public string Image { get; set; }

        /// <summary>
        /// 时间戳 base64
        /// </summary>
        public string TimeStamp { get; set; }

        /// <summary>
        /// 用户id
        /// </summary>
        public string UserId { get; set; }
    }
}