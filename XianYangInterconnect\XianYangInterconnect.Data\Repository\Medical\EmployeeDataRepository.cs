﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;

namespace XianYangInterconnect.Data.Repository
{
    public class EmployeeDataRepository : IEmployeeDataRepository
    {
        private readonly MedicalContext _medicalContext = null;

        public EmployeeDataRepository(MedicalContext medicalContext)
        {
            _medicalContext = medicalContext;
        }

        public async Task<string> GetEmployeeIDByIDCard(string idenno)
        {
            return await _medicalContext.EmployeeDataInfos.Where(m => m.IDCard == idenno).Select(m => m.EmployeeID).FirstOrDefaultAsync();
        }
    }
}