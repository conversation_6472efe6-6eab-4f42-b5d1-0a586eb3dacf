﻿using NLog;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using XianYangInterconnect.ViewModels.SyncBackHis;

namespace XianYangInterconnect.Service.SyncBackHis
{
    public class WebServiceClient
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        /// <summary>
        /// 发送soap请求
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="serviceUrl">请求路径</param>
        /// <param name="action">行为操作</param>
        /// <returns></returns>
        public static async Task<string> SendRequestMessageAsync(string message, string serviceUrl, string action)
        {
            var _serviceUrl = serviceUrl;
            var _httpClient = new HttpClient();
            var soapEnvelope = CreateSoapEnvelopeView(action, message);
            _httpClient.DefaultRequestHeaders.Add("rootId", Guid.NewGuid().ToString("N"));
            _httpClient.DefaultRequestHeaders.Add("domain", "SHZHY_HLCCC");
            _httpClient.DefaultRequestHeaders.Add("businessTime", DateTime.Now.ToString("yyyy - MM - dd HH: mm:ss"));
            _httpClient.DefaultRequestHeaders.Add("operationTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            _httpClient.DefaultRequestHeaders.Add("key", "1c09a5c4-d452-4ae1-9f62-7f5a1265fdaa");
            var namespaces = new XmlSerializerNamespaces();
            namespaces.Add("soap", "http://www.w3.org/2003/05/soap-envelope");
            namespaces.Add("urn", "urn:hl7-org:v3");
            string soapRequest = XmlSerializerMessage(soapEnvelope, namespaces);

            var content = new StringContent(soapRequest, Encoding.UTF8, "application/soap+xml");

            try
            {
                var response = await _httpClient.PostAsync(_serviceUrl, content);
                return await response.Content.ReadAsStringAsync();
            }
            catch (Exception ex)
            {
                _logger.Error($"Error sending SOAP request: {ex}");
            }
            return null;
        }

        /// <summary>
        /// 反序列化返回结果
        /// </summary>
        /// <param name="xml">xml字符串</param>
        /// <returns></returns>
        public static T DeserializationXml<T>(string xml)
        {
            T responseXmlView;
            XmlSerializer serializer = new(typeof(T));

            using (StringReader reader = new(xml))
            {
                responseXmlView = (T)serializer.Deserialize(reader);
            }
            return responseXmlView;
        }
        /// <summary>
        /// 序列化成字符串
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="message"></param>
        /// <param name="namespaces"></param>
        /// <returns></returns>
        public static string XmlSerializerMessage<T>(T message, XmlSerializerNamespaces namespaces)
        {
            var serializer = new XmlSerializer(typeof(T));
            string soapRequest;

            var settings = new XmlWriterSettings
            {
                Encoding = new UTF8Encoding(false), // 不带 BOM 的 UTF-8
                Indent = true,
                OmitXmlDeclaration = true,
            };
            using (var stringWriter = new Utf8StringWriter()) // 自定义 StringWriter
            using (var xmlWriter = XmlWriter.Create(stringWriter, settings))
            {
                serializer.Serialize(xmlWriter, message, namespaces);
                soapRequest = stringWriter.ToString();
            }

            return soapRequest;
        }

        /// <summary>
        /// 创建咸阳回传webservice请求体
        /// </summary>
        /// <param name="action">行为</param>
        /// <param name="message">传递的信息</param>
        /// <returns></returns>
        public static SoapEnvelopeView CreateSoapEnvelopeView(string action, string message)
        {
            var soapEnvelope = new SoapEnvelopeView
            {
                Body = new SoapBody
                {
                    HIPMessageServer = new HIPMessageServer
                    {
                        Action = action,
                        Message = new CDataWrapper { Value = message }
                    }
                }
            };
            return soapEnvelope;
        }
        /// <summary>
        /// 序列化XmlDocument对象
        /// </summary>
        /// <param name="doc">XmlDocument对象</param>
        /// <param name="indent">缩进</param>
        /// <param name="omitXmlDeclaration"></param>
        /// <returns></returns>
        public static string SerializeXmlDocument(XmlDocument doc, bool indent = true, bool omitXmlDeclaration = true)
        {
            var settings = new XmlWriterSettings
            {
                Indent = indent,
                Encoding = Encoding.UTF8,
                OmitXmlDeclaration = omitXmlDeclaration,
                NewLineHandling = NewLineHandling.Entitize
            };
            var sb = new StringBuilder();
            using (var writer = XmlWriter.Create(sb, settings))
            {
                doc.Save(writer);
            }
            return sb.ToString();
        }
        /// <summary>
        /// 设置字符串写入流字符编码
        /// </summary>
        internal class Utf8StringWriter : StringWriter
        {
            // 去掉 BOM
            public override Encoding Encoding => new UTF8Encoding(false);
        }

    }

}