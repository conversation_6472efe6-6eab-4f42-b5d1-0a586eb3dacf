using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Interface
{
    public interface IInpatientDataRepository
    {
        /// <summary>
        /// 获取当前在院患者住院号
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<string>> GetCaseNumbers(string hospitalID);

        /// <summary>
        /// 根据病区ID获取当前在院患者住院号
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<string>> GetCaseNumberListByStationID(int stationID, string hospitalID);
        /// <summary>
        /// 获取当前在院患者
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<InpatientDataInfo>> GetAllInpatientDataInfos(string hospitalID);
        /// <summary>
        /// 获取当前未出院状态患者
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<InpatientDataInfo>> GetAllInpatientDataAsync(string hospitalID);

        /// <summary>
        /// 获取当前在院患者
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        Task<InpatientDataInfo> GetInpatientDataInfo(string caseNumber);
        /// <summary>
        /// 获取患者(包含出院)
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        Task<InpatientDataInfo> GetInpatientDataInfoAsync(string caseNumber);
        /// <summary>
        /// 通过caseNumberList获取患者数据（含出院）
        /// </summary>
        /// <param name="caseNumberList"></param>
        /// <returns></returns>
        Task<List<InpatientDataInfo>> GetInpatientListByCaseNumberListAsync(List<string> caseNumberList);
        /// <summary>
        /// 获取患者(包含出院)
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        Task<List<InpatientDataInfo>> GetInpatientDataInfoByChartNoAsync(string chartNo);
        /// <summary>
        /// 获取同步的患者流水号
        /// </summary>
        /// <param name="caseNumbers">患者流水号集合</param>
        /// <returns></returns>
        Task<List<string>> GetByCaseNumberListAsync(List<string> caseNumbers);

        /// <summary>
        /// 获取患者信息清单
        /// </summary>
        /// <param name="caseNumbers"></param>
        /// <returns></returns>
        Task<List<InpatientDataInfo>> GetInpatientDatas(List<string> caseNumbers);

        /// <summary>
        /// 获取患者病区
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        Task<InpatientDataInfo> GetInpatientDataView(string caseNumber);
        /// <summary>
        /// 根据CaseNumber获取数据(包含已出院)
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        Task<InpatientDataInfo> GetInpatientAllDataView(string caseNumber);
    }
}