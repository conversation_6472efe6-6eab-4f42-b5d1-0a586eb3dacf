﻿namespace InterconnectCore.ViewModels
{
    public class CommonOrderView
    {
        /// <summary>
        /// 住院唯一号
        /// </summary>
        public string CaseNumber { get; set; }

        /// <summary>
        /// 医嘱序号
        /// </summary>
        public string OrderID { get; set; }

        /// <summary>
        /// 医嘱代码
        /// </summary>
        public string OrderCode { get; set; }
        /// <summary>
        /// 医嘱项目类型代码 1.药物医嘱，2.护理 ，5检验类医嘱 6.治疗
        /// </summary>
        public string OrderPatternCode { get; set; }
        /// <summary>
        /// 医嘱类别(治疗、药品、护理、等)
        /// </summary>
        public string OrderPattern { get; set; }
        /// <summary>
        /// 医嘱类别，长期 1，临时 0，
        /// </summary>
        public string OrderType { get; set; }

        /// <summary>
        /// 医嘱内容
        /// </summary>
        public string OrderContent { get; set; }

        /// <summary>
        /// 频次
        /// </summary>
        public string Frequency { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 一次剂量
        /// </summary>
        public string OrderDose { get; set; }

        /// <summary>
        /// 总剂量
        /// </summary>
        public string TotalVolume { get; set; }

        /// <summary>
        /// 服法/途径/姿势
        /// </summary>
        public string OrderRule { get; set; }

        /// <summary>
        /// 部位
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// 检验类别
        /// </summary>
        public string MethodCategory { get; set; }

        /// <summary>
        /// 执行次数
        /// </summary>
        public string NumberOfExecution { get; set; }

        /// <summary>
        /// 医嘱状态
        /// </summary>
        public int OredrStatus { get; set; }
        /// <summary>
        /// 医嘱状态名称
        /// </summary>
        public string OredrStatusName { get; set; }

        /// <summary>
        /// 开始日期时间
        /// </summary>
        public string StartDate { get; set; }

        /// <summary>
        /// 结束日期时间
        /// </summary>
        public string EndDate { get; set; }

        /// <summary>
        /// 开立人员
        /// </summary>
        public string AddEmployeeID { get; set; }

        /// <summary>
        /// 开立时间
        /// </summary>
        public string AddDate { get; set; }

        /// <summary>
        /// 确认人员
        /// </summary>
        public string ConfirmPersonID { get; set; }

        /// <summary>
        /// 确认时间
        /// </summary>
        public string ConfirmDate { get; set; }

        /// <summary>
        /// 取消人员
        /// </summary>
        public string CancalPersonID { get; set; }

        /// <summary>
        /// 取消时间
        /// </summary>
        public string CancalDate { get; set; }

        /// <summary>
        /// ChartNo
        /// </summary>
        public string ChartNo { get; set; }

        /// <summary>
        /// 备注说明，手写医嘱会写这个字段
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// 病区 
        /// </summary>
        public string StationCode { get; set; }
        /// <summary>
        /// 科室
        /// </summary>
        public string DeptCode { get; set; }
        /// <summary>
        /// 医嘱组号
        /// </summary>
        public string OrderGroupID { get; set; }
        /// <summary>
        /// his数据流水号
        /// </summary>
        public string HISOrderID { get; set; }
        /// <summary>
        /// 医嘱明细ID 存放his医嘱的流水号
        /// </summary>
        public string OrderDetailID { get; set; }
    }
}
