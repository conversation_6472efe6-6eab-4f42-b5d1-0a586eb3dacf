﻿namespace XianYangInterconnect.Service.Interface
{
    public interface IPatientOrderService
    {
        /// <summary>
        /// 自动同步患者医嘱数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<bool> AutoSyncPatientOrderSync(string hospitalID);
        /// <summary>
        /// 同步患者医嘱数据
        /// </summary>
        /// <param name="caseNumber">患者流水号</param>
        /// <returns></returns>
        Task<bool> SyncPatientOrdersAsync(string caseNumber);
    }
}
