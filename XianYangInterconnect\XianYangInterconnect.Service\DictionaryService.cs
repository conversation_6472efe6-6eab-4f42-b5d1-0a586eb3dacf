using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System.Drawing.Imaging;
using System.Reflection.Emit;
using System.Text.RegularExpressions;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Data.Interface.Medical;
using XianYangInterconnect.Models;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Services.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    public class DictionaryService : IDictionaryService
    {
        #region 引用
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAPISettingRepository _aPISettingRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IOptions<SystemConfig> _options;
        private readonly IBedListRepository _bedListRepository;
        private readonly IStationListRepository _stationListRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IRequestApiService _requestApiService;
        private readonly IStationToDeptInfoRepository _stationToDeptInfoRepository;
        private readonly IEmployeeDataRepository _employeeDataRepository;
        #endregion

        #region 常量
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 获取员工数据类型
        /// (D医生、N护士、T技师、P药师、C厨师、F收款员、O其他)
        /// </summary>
        private static readonly List<string> INCLUDE_EMPLOYEE_TYPE = ["D", "N", "T", "P"];
        /// <summary>
        /// 员工数据类型：其他
        /// (D医生、N护士、T技师、P药师、C厨师、F收款员、O其他)
        /// </summary>
        private static readonly string INCLUDE_EMPLOYEE_TYPE_O = "O";
        /// <summary>
        /// 床位可用状态
        /// </summary>
        private static readonly List<string> INCLUDE_BED_TYPE = ["O", "U"];
        /// <summary>
        /// 护理部部门code
        /// </summary>
        private static readonly string DEPARTMENT_CODE_6023 = "6023";
        /// <summary>
        /// 药品分类
        /// </summary>
        private static readonly List<string> DRUG_TYPE_LIST = ["143", "144", "145", "172",];
        /// <summary>
        /// 医嘱类型
        /// </summary>
        private static readonly List<string> ORDER_TYPE_LIST = ["158", "152", "146", "157", "140", "150", "147", "161", "149"];
        /// <summary>
        /// 医嘱类型
        /// </summary>
        private static readonly Dictionary<string, string> ORDER_TYPE_DICT = new Dictionary<string, string> { { "140", "治疗处置" }, { "146", "描述医嘱" }, { "147", "分级护理" }, { "149", "饍食" }, { "150", "入出转" }, { "152", "出院" }, { "157", "收费" }, { "158", "护理" }, { "161", "病情诊断" } };

        private static readonly List<string> DEPT_TYPE = ["N", "OP"];
        #endregion

        #region 构造函数
        public DictionaryService(
            IAPISettingRepository aPISettingRepository
            , IOptions<SystemConfig> options
            , IBedListRepository bedListRepository
            , IStationListRepository stationListRepository
            , IDepartmentListRepository departmentListRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            , IRequestApiService requestApiService
            , IStationToDeptInfoRepository stationToDeptInfoRepository
            , IEmployeeDataRepository employeeDataRepository
            )
        {
            _aPISettingRepository = aPISettingRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _options = options;
            _bedListRepository = bedListRepository;
            _stationListRepository = stationListRepository;
            _departmentListRepository = departmentListRepository;
            _requestApiService = requestApiService;
            _stationToDeptInfoRepository = stationToDeptInfoRepository;
            _employeeDataRepository = employeeDataRepository;
        }
        #endregion

        #region 人员同步数据清洗
        /// <summary>
        /// 获取员工列表
        /// </summary>
        /// <param name="emplCode">员工代码</param>
        /// <param name="emplName">员工姓名</param>
        /// <param name="spellCode">拼音码</param>
        /// <param name="wbCode">五笔</param>
        /// <param name="sexCode">性别</param>
        /// <param name="birthday">出生日期</param>
        /// <param name="posiCode">职务代号</param>
        /// <param name="levlCode">职级代号</param>
        /// <param name="educationCode">学历</param>
        /// <param name="idenno">身份证号</param>
        /// <param name="deptCode">所属科室号</param>
        /// <param name="nurseCellCode">所属护理站</param>
        /// <param name="emplType">人员类型(D医生、N护士、T技师、P药师、C厨师、F收款员、O其他)</param>
        /// <param name="expertFlag">是否专家</param>
        /// <param name="createDateStart">创建开始时间</param>
        /// <param name="createDateEnd">创建结束时间</param>
        /// <returns></returns>
        public async Task<bool> GetAllEmployeeData(string emplCode, string emplName, string spellCode, string wbCode, string sexCode, string birthday
            , string posiCode, string levlCode, string educationCode, string idenno, string deptCode, string nurseCellCode, string emplType, string expertFlag
            , string createDateStart, string createDateEnd)
        {
            try
            {
                var requestParam = $"?emplCode={emplCode}&emplName={emplName}&spellCode={spellCode}&wbCode={wbCode}&sexCode={sexCode}" +
                    $"&birthday={birthday}&posiCode={posiCode}&levlCode={levlCode}&educationCode={educationCode}&idenno={idenno}&deptCode={deptCode}" +
                    $"&nurseCellCode={nurseCellCode}&emplType={emplType}&expertFlag={expertFlag}&createDateStart={createDateStart}&createDateEnd={createDateEnd}";
                var headers = new Dictionary<string, string>
                {
                    { "rootId", Guid.NewGuid().ToString("N") },
                    {"domain", "SHZHY_HLCCC" },
                    { "businessTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                    { "operationTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                    { "key", "1c09a5c4-d452-4ae1-9f62-7f5a1265fdaa"}
                };
                var result = await _requestApiService.RequestAPIByAppconfigSetting(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetAllEmployeeData", requestParam, 2, null, "application/json", headers);
                if (result == null)
                {
                    _logger.Warn($"同步人员信息数据失败，访问接口获取数据为空");
                    return false;
                }
                var employeeList = ListToJson.ToList<List<EmployeeListView>>(result);
                employeeList = employeeList.Where(m => INCLUDE_EMPLOYEE_TYPE.Contains(m.EmplType) || (m.EmplType == INCLUDE_EMPLOYEE_TYPE_O && m.DeptId == DEPARTMENT_CODE_6023)).ToList();
                var userList = new List<UserView>();
                foreach (var employee in employeeList)
                {
                    var user = await CreateUserView(employee.EmplId, employee.EmplName, employee.DeptId, employee.PosiName,
                        employee.LevelName, employee.Tel, employee.SpellCode, employee.ValidState, employee.OperDate, employee.EmplType, headers);
                    userList.Add(user);
                }
                if (userList.Any())
                {
                    await _requestApiService.RequestAPI("SyncAllEmployeeData", ListToJson.ToJson(userList));
                }
                //根据身份证号同步CA数据
                //await SyncEmployeeCADataAsync(employeeList);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Warn($"同步人员信息数据失败，异常信息：{ex.ToString()}");
                return false;
            }

        }
        /// <summary>
        /// 根据身份证号同步CA数据
        /// </summary>
        /// <param name="employeeList"></param>
        /// <returns></returns>
        private async Task SyncEmployeeCADataAsync(List<EmployeeListView> employeeList)
        {
            employeeList = employeeList.Where(m => !string.IsNullOrEmpty(m.Idenno)).ToList();
            var employeeCAViews = new List<EmployeeCAView>();
            foreach (var employee in employeeList)
            {
                //通过身份证号获取caUserID
                var caUserID = await GetCAUserIDAsync(employee.Idenno);
                if (string.IsNullOrEmpty(caUserID))
                {
                    _logger.Warn($"获取人员caUserID数据失败，工号:{employee.EmplId},身份证号:{employee.Idenno}");
                    continue;
                }
                //通过caUserID获取签章图片
                var caImage = await GetCAImageAsync(caUserID);
                if (string.IsNullOrEmpty(caImage))
                {
                    _logger.Warn($"获取人员caImage数据失败，工号:{employee.EmplId},身份证号:{employee.Idenno},caUserID:{caUserID}");
                    continue;
                }
                //组装EmployeeCAData数据
                var employeeCAView = CreateEmployeeCAView(employee.EmplId, caUserID, caImage);
                employeeCAViews.Add(employeeCAView);
            }
            //通过接口同步CA数据
            if (employeeCAViews.Count > 0)
            {
                await _requestApiService.RequestAPI("SyncEmployeeCAData", ListToJson.ToJson(employeeCAViews));
            }
        }

        /// <summary>
        /// 根据caUserID获取签章图片
        /// </summary>
        /// <param name="caUserID"></param>
        /// <returns></returns>
        private async Task<string> GetCAImageAsync(string caUserID)
        {
            var caImageBase64 = "";
            var requestParam = $"?caUserId={caUserID}";
            var result = await _requestApiService.RequestAPIByAppconfigSetting(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "QuerySignImageAPI", requestParam, 2, null, "application/json");
            if (!string.IsNullOrEmpty(result))
            {
                try
                {
                    var caUser = ListToJson.ToList<SignView>(result);
                    if (caUser != null)
                    {
                        caImageBase64 = caUser.Image;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"转换CAImage数据失败，caUserID:{caUserID}，异常:{ex}");
                    return caImageBase64;
                }
            }
            return caImageBase64;
        }

        /// <summary>
        /// 根据身份证号获取caUserID
        /// </summary>
        /// <param name="idenno"></param>
        /// <returns></returns>
        private async Task<string> GetCAUserIDAsync(string idenno)
        {
            var caUserID = "";
            var requestParam = $"?idNumber={idenno}";
            var result = await _requestApiService.RequestAPIByAppconfigSetting(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "QueryUserInfoAPI", requestParam, 2, null, "application/json");
            if (!string.IsNullOrEmpty(result))
            {
                try
                {
                    var caUser = ListToJson.ToList<CAUserView>(result);
                    if (caUser != null)
                    {
                        caUserID = caUser.msspId;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"转换caUserID数据失败，身份证号:{idenno}，异常:{ex}");
                    return caUserID;
                }
            }
            return caUserID;
        }
        /// <summary>
        /// 创建用户CA数据
        /// </summary>
        /// <param name="employee"></param>
        /// <param name="caUserID"></param>
        /// <param name="caImage"></param>
        /// <returns></returns>
        private EmployeeCAView CreateEmployeeCAView(string employeeID, string caUserID, string caImage)
        {

            var employeeCAView = new EmployeeCAView()
            {
                HospitalID = _options.Value.HospitalID,
                UserID = employeeID,
                CAUserID = caUserID,
            };
            try
            {
                employeeCAView.StampImage = Convert.FromBase64String(caImage);
            }
            catch (Exception ex)
            {
                _logger.Error($"转换StampImage数据失败，工号:{employeeID},caUserID:{caUserID},caImage:{caImage},异常:{ex}");
                return employeeCAView;
            }
            return employeeCAView;
        }

        /// <summary>
        /// 根据员工编号获取单个员工
        /// </summary>
        /// <param name="emplId">员工编号</param>
        /// <returns></returns>
        public async Task<bool> GetOneEmployeeData(string emplId)
        {
            try
            {
                var requestParam = $"/{emplId}";
                var headers = new Dictionary<string, string>
                {
                    { "rootId", Guid.NewGuid().ToString("N") },
                    {"domain", "SHZHY_HLCCC" },
                    { "businessTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                    { "operationTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                    { "key", "1c09a5c4-d452-4ae1-9f62-7f5a1265fdaa"}
                };
                var result = await _requestApiService.RequestAPIByAppconfigSetting(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetOneEmployeeData", requestParam, 2, null, "application/json", headers);
                var employee = JsonConvert.DeserializeObject<SingleEmployeeView>(result);
                if (employee == null)
                {
                    _logger.Warn("同步单个人员信息数据失败，人员数据转换失败");
                    return false;
                }
                var user = await CreateUserView(emplId, employee.EmplName, employee.DeptCode, "",
                            "", employee.Tel, employee.SpellCode, employee.ValidState, employee.OperDate, employee.EmplType, headers);
                await _requestApiService.RequestAPI("SyncOneEmployeeData", ListToJson.ToJson(user));
                return true;
            }
            catch (Exception ex)
            {
                _logger.Warn($"同步单个人员信息数据失败，异常信息：{ex.ToString()}");
                return false;
            }
        }
        /// <summary>
        /// 创建人员信息视图
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="emplName">员工姓名</param>
        /// <param name="deptId">所属科室号</param>
        /// <param name="posiName">职务代号</param>
        /// <param name="levelName">职级代号</param>
        /// <param name="phoneNumber">电话号码</param>
        /// <param name="spellCode">拼音码</param>
        /// <param name="validState">有效性标志 1 有效 0 停用 2 废弃</param>
        /// <param name="operDate">操作日期</param>
        /// <param name="emplType">人员类型代号</param>
        /// <returns></returns>
        private async Task<UserView> CreateUserView(string employeeID, string emplName, string deptId, string posiName, string levelName, string phoneNumber
            , string spellCode, string validState, DateTime? operDate, string emplType, Dictionary<string, string> headers)
        {
            var (deptCode,departmentCodes) = await GetEmployeeDepartmentCodes(employeeID,headers);
            return new UserView()
            {
                HospitalID = _options.Value.HospitalID,
                UserID = employeeID,
                PhysicianID = employeeID,
                Password = employeeID,
                Name = emplName,
                DepartmentCode =  string.IsNullOrEmpty(deptCode) ? deptId : deptCode,
                DepartmentCodes = departmentCodes,
                Title = posiName,
                Rank = levelName,
                PhoneNumber = phoneNumber,
                JobID = -1,
                PinYinCode = spellCode,
                ExpirationDate = validState == "1" ? null : operDate,
                DeleteFlag = validState == "1" ? "" : "*",
                UserRoles = GetUserRole(emplType, posiName, levelName)
            };
        }
        /// <summary>
        /// 赋予员工权限
        /// </summary>
        /// <param name="emplType">员工类型(D医生、N护士、T技师、P药师、C厨师、F收款员、O其他)</param>
        /// <param name="posiName">职务代号</param>
        /// <param name="levelName">职级代号</param>
        /// <returns></returns>
        private static List<int> GetUserRole(string emplType, string posiName, string levelName)
        {
            var roles = new List<int>();
            switch (emplType)
            {
                case "D":
                    roles.Add(11);
                    break;
                case "T":
                    roles.Add(10);
                    break;
                case "P":
                    roles.Add(12);
                    break;
                case "N":
                    roles.Add(30);
                    if ((!string.IsNullOrEmpty(posiName) && posiName.Contains("科护长")) || (!string.IsNullOrEmpty(levelName) && levelName.Contains("副主任护师")))
                    {
                        roles.Add(40);
                        roles.Add(50);
                    }
                    break;
                case "O":
                    roles.Add(30);
                    if (!string.IsNullOrEmpty(levelName) && levelName.Contains("副主任护师"))
                    {
                        roles.Add(70);
                    }
                    break;
                default:
                    break;
            }
            return roles;
        }
        /// <summary>
        /// 获取人员本科室及授权科室
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <param name="headers">请求头</param>
        /// <returns></returns>
        private async Task<(string,List<string>)> GetEmployeeDepartmentCodes(string employeeID, Dictionary<string, string> headers)
        {
            var requestParam = $"?docCode={employeeID}";
            var result = await _requestApiService.RequestAPIByAppconfigSetting(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetEmployeeDepartments", requestParam, 2, null, "application/json", headers);
            if (result == null)
            {
                _logger.Warn($"同步人员信息数据失败，访问接口获取数据为空");
                return (null, []);
            }
            var employeeList = ListToJson.ToList<List<DepartmentData>>(result);
            if (!employeeList.Any())
            {
                return (null, []);
            }
            var departmentCodes = employeeList.Where(m => !string.IsNullOrEmpty(m.deptCode)).Select(m => m.deptCode).Distinct().ToList();
            var deptCode = employeeList.FirstOrDefault(m => !string.IsNullOrEmpty(m.deptCode) && m.ownedType == "1")?.deptCode;
            return (deptCode, departmentCodes);
        }

        #endregion

        #region 清洗床位数据逻辑
        /// <summary>
        /// 获取床位字典
        /// </summary>
        /// <param name="bedCode">病床编码</param>
        /// <param name="bedPrice">床位价格</param>
        /// <param name="bedState">床位状态</param>
        /// <param name="deptCode">病区编码</param>
        /// <param name="roomCode">病室编码</param>
        /// <param name="roomUbedSum">空床数量</param>
        /// <returns></returns>
        public async Task<bool> GetBedListAsync(string bedCode, string bedPrice, string bedState, string deptCode, string roomCode, string roomUbedSum)
        {
            var url = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetBedList");
            var responseUrlView = await _aPISettingRepository.GetAPIAddress("SyncBedList");
            if (string.IsNullOrEmpty(url) || responseUrlView == null || string.IsNullOrEmpty(responseUrlView.ApiUrl))
            {
                _logger.Error($"获取链接配置失败");
                return false;
            }
            //string url = "http://************:7801/roc/curr-service/api/v1/common/in-patient/ward/bed/query";
            //var responseUrlView = new ApiUrlView{ApiUrl = "https://localhost:62288/api/Dictionary/SyncBedList"};
            var response = new List<BedListInfo>();
            var hisdata = await GetBedListDataAsync(url, bedCode, bedPrice, bedState, deptCode, roomCode, roomUbedSum);
            if (hisdata == null || hisdata.Count == 0)
            {
                _logger.Warn("同步床位信息数据失败，未获取到相关数据");
                return false;
            }
            var stationToDepts = await _stationToDeptInfoRepository.GetAsync();
            var beds = hisdata.GroupBy(m => m.deptCode).ToList();
            foreach (var item in beds)
            {
                var bedSorts = CalculateDeptBedSort(item.Select(m => m.bedCode).ToList());
                var bedInfos = CreateBedListInfo([.. item], stationToDepts, bedSorts);
                if (bedInfos.Count > 0)
                {
                    response.AddRange(bedInfos);
                }
            }
            var bedIDs = await _bedListRepository.GetBedListMaxID(_options.Value.HospitalID);
            int maxID = bedIDs.Count > 0 ? bedIDs.Max() + 1 : 1;
            response.ForEach(m => m.ID = maxID++);
            HttpHelper.SendObjectAsJsonInBody(responseUrlView.ApiUrl, response);
            return true;
        }
        /// <summary>
        /// 获取HIS数据
        /// </summary>
        /// <param name="bedCode">病床编码</param>
        /// <param name="bedPrice">床位价格</param>
        /// <param name="bedState">床位状态</param>
        /// <param name="deptCode">病区编码</param>
        /// <param name="roomCode">病室编码</param>
        /// <param name="roomUbedSum">空床数量</param>
        /// <returns></returns>
        /// <returns></returns>
        private static async Task<List<BedData>> GetBedListDataAsync(string url, string bedCode, string bedPrice, string bedState, string deptCode, string roomCode, string roomUbedSum)
        {
            var parameters = new Dictionary<string, string>
            {
                { "bedCode", bedCode },
                { "bedPrice", bedPrice },
                { "bedState", bedState },
                { "deptCode", deptCode },
                { "roomCode", roomCode },
                { "roomUbedSum", roomUbedSum }
            };
            var urlParams = string.Join("&", parameters.Where(p => !string.IsNullOrEmpty(p.Value)).Select(p => $"{p.Key}={p.Value}"));
            urlParams = string.IsNullOrEmpty(urlParams) ? string.Empty : $"?{urlParams}";
            string hisResult = await HttpHelper.HttpGetAsync(url + urlParams);
            var result = ListToJson.ToList<BedListView>(hisResult);
            return result.data;
        }
        /// <summary>
        /// 创建床位信息实体
        /// </summary>
        /// <param name="bedViews">HIS床位信息</param>
        /// <param name="stationToDepts">病区对科室字典</param>
        /// <param name="bedSorts">床位排序</param>
        /// <returns></returns>
        private List<BedListInfo> CreateBedListInfo(List<BedData> bedViews, List<StationToDepartmentInfo> stationToDepts, Dictionary<string, int> bedSorts)
        {
            var result = new List<BedListInfo>();
            foreach (var data in bedViews)
            {
                var stationToDept = stationToDepts.Find(m => m.StationCode == data.deptCode);
                if (stationToDept == null)
                {
                    _logger.Error($"床位信息同步失败,获取病区对科室关系失败，科室编码：{data.deptCode}");
                    continue;
                }
                var BedInfo = new BedListInfo
                {
                    BedNumber = data.bedCode.Replace(data.deptCode, string.Empty),
                    StationID = stationToDept.StationID,
                    DepartmentListID = stationToDept.DepartmentListID,
                    Sort = (short)bedSorts[data.bedCode],
                    ICUFlag = data.bedCode.Contains("IC") ? "Y" : string.Empty,
                    DisableFlag = INCLUDE_BED_TYPE.Contains(data.bedState) ? string.Empty : "*",
                    NewBornFlag = string.Empty,
                    AdditionFlag = data.bedCode.Contains('+'),
                    BedLocation = string.Empty,
                    RoomCode = data.roomCode,
                    WardBuilding = string.Empty,
                    WardFloor = string.Empty,
                    HospitalID = _options.Value.HospitalID,
                    ModifyPersonID = "TongBu",
                    ModifyDate = DateTime.Now,
                    DeleteFlag = ""
                };
                result.Add(BedInfo);
            }
            return result;
        }
        /// <summary>
        /// 床位排序 特殊床位在最前方,其他数字床位在中间,加床在最后
        /// </summary>
        /// <param name="bedCodes">床位码</param>
        /// <returns></returns>
        public static Dictionary<string, int> CalculateDeptBedSort(List<string> bedCodes)
        {
            // 分三个阶段处理排序逻辑
            var query = bedCodes.Select(bed => new
            {
                Code = bed,
                Group = GetGroupCategory(bed),
                NumericValue = ExtractNumericValue(bed)
            });
            // 按分组优先级和数值排序
            var ordered = query.OrderBy(x => x.Group)
                              .ThenBy(x => x.NumericValue)
                              .ThenBy(x => x.Code)  // 数值相同时保留原始顺序
                              .ToList();
            // 生成带序号的字典
            int counter = 1;
            return ordered.ToDictionary(
                item => item.Code,
                item => counter++
            );
        }
        /// <summary>
        /// 判断床号所属分组（1=特殊字符组, 2=纯数字组, 3=加号组）
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        private static int GetGroupCategory(string code)
        {
            if (int.TryParse(code.Trim(), out int bedNumber))
            {
                return 2;
            }
            return code.Contains('+') ? 3 : 1;
        }
        /// <summary>
        /// 提取字符串中的数字部分并转换为数值
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        private static int ExtractNumericValue(string code)
        {
            var numericString = Regex.Replace(code, @"[^\d]", "");
            return string.IsNullOrEmpty(numericString) ? 0 : int.Parse(numericString);
        }
        #endregion

        #region 清洗病区科室数据逻辑
        /// <summary>
        /// 清洗HIS病区科室数据
        /// </summary>
        /// <param name="branchCode">院区编码</param>
        /// <param name="deptCode">病区或科室编码</param>
        /// <param name="deptType">病区类型</param>
        /// <param name="validState">病区状态</param>
        /// <returns></returns>
        public async Task<bool> GetStationAndDepartmentAsync(string branchCode, string deptCode, string deptType, string validState)
        {
            var url = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetDepartmentList");
            var responseUrlView = await _aPISettingRepository.GetAPIAddress("SyncStationAndDepartment");
            if (string.IsNullOrEmpty(url) || responseUrlView == null || string.IsNullOrEmpty(responseUrlView.ApiUrl))
            {
                _logger.Error($"获取链接配置失败");
                return false;
            }
            var hisData = await GetDepartmentListDataAsync(url, branchCode, deptCode, deptType, validState);
            if (hisData == null && hisData.Count == 0)
            {
                _logger.Error($"HIS获取病区数据失败");
                return false;
            }
            //病区数据
            var requestStations = await CreateRequestStation(hisData);
            //科室数据
            var requestDepartment = await CreateRequestDepartment(hisData);
            var response = new
            {
                StationList = requestStations,
                DepartmentList = requestDepartment,
                StationToDepartmentList = CreateStationToDepartment(hisData, requestStations, requestDepartment)
            };
            HttpHelper.SendObjectAsJsonInBody(responseUrlView.ApiUrl, response);
            return true;
        }
        /// <summary>
        /// 获取HIS病区科室数据
        /// </summary>
        /// <param name="url">请求链接</param>
        /// <param name="branchCode">院区编码</param>
        /// <param name="deptCode">病区或科室编码</param>
        /// <param name="deptType">病区类型</param>
        /// <param name="validState">病区状态</param>
        /// <returns></returns>
        private static async Task<List<DepartmentData>> GetDepartmentListDataAsync(string url, string branchCode, string deptCode, string deptType, string validState)
        {
            var parameters = new Dictionary<string, string>
            {
                { "branchCode", branchCode },
                { "deptCode", deptCode },
                { "deptType", deptType },
                { "validState", validState },
            };
            string urlParams = string.Join("&", parameters.Where(p => !string.IsNullOrEmpty(p.Value)).Select(p => $"{p.Key}={p.Value}"));
            urlParams = string.IsNullOrEmpty(urlParams) ? string.Empty : $"?{urlParams}";

            string hisResult = await HttpHelper.HttpGetAsync(url + urlParams);
            // 读取本地文件
            //var hisResult = ReadFile.ReadTxt(@"C:\Users\<USER>\Desktop\json\xianyangStation.txt");
            var result = ListToJson.ToList<DepartmentListView>(hisResult);
            return result.data;
        }
        /// <summary>
        /// 创建请求病区数据
        /// </summary>
        /// <param name="hisData"></param>
        /// <returns></returns>
        private async Task<List<StationListInfo>> CreateRequestStation(List<DepartmentData> hisData)
        {
            var requestStations = new List<StationListInfo>();
            var stations = hisData.Where(m => DEPT_TYPE.Contains(m.deptType)).ToList();
            if (stations.Count == 0)
            {
                return requestStations;
            }
            foreach (var station in stations)
            {
                requestStations.Add(CreateStationModel(station));
            }
            var stationIDs = await _stationListRepository.GetStationListMaxID(_options.Value.HospitalID);
            int maxID = stationIDs.Count > 0 ? stationIDs.Max() + 1 : 1;
            foreach (var item in requestStations)
            {
                item.ID = maxID++;
                item.Sort = (short)item.ID;
            }
            return requestStations;
        }
        /// <summary>
        /// 创建请求科室数据
        /// </summary>
        /// <param name="hisData">his数据</param>
        /// <returns></returns>
        private async Task<List<DepartmentListInfo>> CreateRequestDepartment(List<DepartmentData> hisData)
        {
            var result = new List<DepartmentListInfo>();
            var departments = hisData.Where(m => m.deptType == "I").ToList();
            if (departments.Count == 0)
            {
                return result;
            }
            foreach (var department in departments)
            {
                result.Add(CreateDepartmentModel(department));
            }
            var deptIDs = await _departmentListRepository.GetDeptListMaxID(_options.Value.HospitalID);
            int maxID = deptIDs.Count > 0 ? deptIDs.Max() + 1 : 1;
            result.ForEach(m => m.ID = maxID++);
            return result;
        }
        /// <summary>
        /// 创建病区科室关系数据
        /// </summary>
        /// <param name="hisData"></param>
        /// <param name="stations"></param>
        /// <param name="departmentListInfos"></param>
        /// <returns></returns>
        private List<StationToDepartmentInfo> CreateStationToDepartment(List<DepartmentData> hisData, List<StationListInfo> stations, List<DepartmentListInfo> departmentListInfos)
        {
            var result = new List<StationToDepartmentInfo>();
            var departments = hisData.Where(m => m.deptType == "I").ToList();
            if (departments.Count == 0)
            {
                return result;
            }
            foreach (var department in departments)
            {
                var station = stations.FirstOrDefault(m => m.StationCode == department.wardareaCode);
                var dept = departmentListInfos.FirstOrDefault(m => m.DepartmentCode == department.deptCode);
                if (station == null || dept == null)
                {
                    continue;
                }
                result.Add(CreateStationToDepartmentModel(dept, station));
            }
            return result;
        }
        /// <summary>
        /// 创建病区字典实体
        /// </summary>
        /// <param name="station">his数据</param>
        /// <returns></returns>
        private StationListInfo CreateStationModel(DepartmentData station)
        {
            return new StationListInfo()
            {
                HospitalID = _options.Value.HospitalID,
                StationName = station.deptName,
                StationCode = station.deptCode,
                ICUFlag = string.Empty,//后续确认处理
                VirtalFlag = false,//后续确认处理
                AttendanceCondition = "a",//后续确认处理
                AssessToProblemPoint = null,//后续确认处理
                HeadNurse = string.Empty,//后续确认处理
                StationPattern = null,//病区类型,0：内科、1：外科、2：专科、3：非临床病区 后续确认处理
                StationShortName = station.simpleName,//后续确认处理
                ModifyPersonID = "TongBu",
                ModifyDate = DateTime.Now,
                DeleteFlag = ""
            };
        }
        /// <summary>
        /// 创建科室字典实体
        /// </summary>
        /// <param name="department"></param>
        /// <returns></returns>
        private DepartmentListInfo CreateDepartmentModel(DepartmentData department)
        {
            return new DepartmentListInfo()
            {
                HospitalID = _options.Value.HospitalID,
                Department = department.deptName,
                DepartmentCode = department.deptCode,
                DepartmentPattern = null,//科别类型，0：内科、1：外科、2：专科、3：非临床科室 //后续确认处理
                SpecialListType = string.Empty,
                ModifyPersonID = "TongBu",
                ModifyDate = DateTime.Now,
                DeleteFlag = string.Empty
            };
        }
        /// <summary>
        /// 创建科室病区关系实体
        /// </summary>
        /// <param name="dept">科室数据</param>
        /// <param name="station">病区数据</param>
        /// <returns></returns>
        private StationToDepartmentInfo CreateStationToDepartmentModel(DepartmentListInfo dept, StationListInfo station)
        {
            return new StationToDepartmentInfo()
            {
                StationID = station.ID,
                StationCode = station.StationCode,
                DepartmentListID = dept.ID,
                DepartmentCode = dept.DepartmentCode,
                HospitalID = _options.Value.HospitalID,
                ModifyPersonID = "TongBu",
                ModifyDate = DateTime.Now,
                DeleteFlag = string.Empty
            };
        }
        #endregion

        #region 医嘱字典数据逻辑
        /// <summary>
        /// 获取医嘱字典数据
        /// </summary>
        public async Task<bool> GetOrderDictAsync()
        {
            var url = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetOrderDict");
            //url = "http://10.10.13.204/order-service/api/v1/order/order-term/undrug/query";
            if (string.IsNullOrEmpty(url))
            {
                _logger.Error($"获取链接配置失败");
                return false;
            }
            var hisOrderDictList = await GetHisOrderDictAsync(url);
            if (hisOrderDictList == null && hisOrderDictList.Count == 0)
            {
                _logger.Error($"HIS医嘱字典数据失败");
                return false;
            }
            //转换医嘱字典数据
            var orderDictList = CreateOrderDict(hisOrderDictList);
            if (orderDictList.Count <= 0)
            {
                _logger.Error($"HIS医嘱字典数据转换失败");
                return false;
            }
            var result = await _requestApiService.RequestAPI("SyncPhysionOrder", ListToJson.ToJson(orderDictList));
            return true;
        }
        /// <summary>
        /// 创建医嘱字典数据
        /// </summary>
        /// <param name="hisOrderDictList"></param>
        /// <returns></returns>
        private List<OrderDictView> CreateOrderDict(List<OrderItem> hisOrderDictList)
        {
            var list = new List<OrderDictView>();

            foreach (var item in hisOrderDictList)
            {
                // 提前解析 undrugId，避免重复计算
                var id = TryParseTermClassId(item.undrugId);
                if (id == 0)
                {
                    continue;
                }

                string orderTypeName = ORDER_TYPE_DICT.TryGetValue(item.termClassId, out string value)
                    ? value : "未知类型"; // 如果字典没有找到，默认值为"未知类型"

                // 创建并填充 OrderDictView 对象
                var orderDict = new OrderDictView()
                {
                    ID = id,                         // 主键ID
                    OrderType = item.termClassId,     // 医嘱类型代码
                    TypeName = orderTypeName,         // 医嘱类型名称
                    OrderName = item.termName,        // 医嘱名称
                    OrderCode = item.undrugId,        // 医嘱代码
                };

                list.Add(orderDict);
            }

            return list;
        }
        /// <summary>
        /// 获取医嘱字典ID
        /// </summary>
        /// <param name="termClassId"></param>
        /// <returns></returns>
        private static int TryParseTermClassId(string termClassId)
        {
            // 尝试直接转换为 int
            if (int.TryParse(termClassId, out int result))
            {
                return result;
            }
            string numericPart = new(termClassId.Where(char.IsDigit).ToArray());
            return int.TryParse(numericPart, out result) ? result : 0;
        }
        /// <summary>
        /// 获取HIS医嘱字典数据
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        private async Task<List<OrderItem>> GetHisOrderDictAsync(string url)
        {
            var orderDictList = new List<OrderItem>();

            foreach (var orderType in ORDER_TYPE_LIST)
            {
                if (string.IsNullOrEmpty(orderType))
                {
                    continue;
                }

                string hisResult = await HttpHelper.HttpGetAsync(url + $"?termClassId={orderType}&pageSize=150000");
                if (string.IsNullOrEmpty(hisResult))
                {
                    continue;
                }

                HisOrderDictView hisOrderDictView;

                try
                {
                    hisOrderDictView = ListToJson.ToList<HisOrderDictView>(hisResult);
                }
                catch (Exception ex)
                {
                    _logger.Warn($"转换医嘱字典数据失败，异常信息：{ex}");
                    return null;
                }

                if (hisOrderDictView?.data?.list == null || hisOrderDictView.data.list.Count == 0)
                {
                    continue;
                }

                orderDictList.AddRange(hisOrderDictView.data.list);
            }

            return orderDictList;
        }

        #endregion

        #region 获取药品字典数据
        /// <summary>
        /// 获取药品字典数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> GetDrugList()
        {
            var hospitalID = _options.Value.HospitalID;
            var url = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetDrugDict");
            //url = "http://************:7801/roc/order-service/api/v1/order/order-term/drug/query";
            if (string.IsNullOrEmpty(url))
            {
                _logger.Error($"获取链接配置失败");
                return false;
            }
            var hisDrugDictList = await GetHisDrugDictAsync(url);
            if (hisDrugDictList == null && hisDrugDictList.Count == 0)
            {
                _logger.Error($"HIS药品字典数据失败");
                return false;
            }
            //转换医嘱字典数据
            var drugDictList = CreateDrugDict(hisDrugDictList, hospitalID);
            if (drugDictList.Count <= 0)
            {
                _logger.Error($"HIS药品字典数据失败");
                return false;
            }
            var result = await _requestApiService.RequestAPI("SyncDrugList", ListToJson.ToJson(drugDictList));
            try
            {
                ResponseResult ResponseResult = ListToJson.ToList<ResponseResult>(result.ToString());
                if (ResponseResult.Code != 200)
                {
                    _logger.Error($"同步HIS药品字典数据失败，失败原因：{ResponseResult.Message}");
                    return false;
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 获取HIS药品字典数据
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        private async Task<List<DrugItem>> GetHisDrugDictAsync(string url)
        {
            var drugDictList = new List<DrugItem>();

            foreach (var drugType in DRUG_TYPE_LIST)
            {
                if (string.IsNullOrEmpty(drugType))
                {
                    continue;
                }

                string hisResult = await HttpHelper.HttpGetAsync(url + $"?termClassId={drugType}&pageFlag=false");
                if (string.IsNullOrEmpty(hisResult))
                {
                    continue;
                }

                HisDrugDictView hisDrugDictView;

                try
                {
                    hisDrugDictView = ListToJson.ToList<HisDrugDictView>(hisResult);
                }
                catch (Exception ex)
                {
                    _logger.Warn($"转换药品字典数据失败，异常信息：{ex.ToString()}");
                    return null;
                }

                if (hisDrugDictView?.data?.list == null || hisDrugDictView.data.list.Count == 0)
                {
                    continue;
                }

                drugDictList.AddRange(hisDrugDictView.data.list);
            }

            return drugDictList;
        }
        /// <summary>
        /// 创建药品字典数据
        /// </summary>
        /// <param name="hisOrderDictList"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private List<DrugDictView> CreateDrugDict(List<DrugItem> hisDrugDictList, string hospitalID)
        {
            var list = new List<DrugDictView>();

            foreach (var item in hisDrugDictList)
            {
                // 创建并填充 OrderDictView 对象
                var drugDict = new DrugDictView()
                {
                    HospitalID = hospitalID,
                    DrugCode = item.drugtermId,        // 药品代码
                    DrugName = item.tradeName,      // 药品名称
                    DrugSpec = item.specs,  // 药品规格
                    Units = item.minUnit,        // 药品单位
                    DrugType = "",//后续确认处理
                    DrugForm = item.doseModelName,//剂型名称
                    DoseUnits = item.doseUnit,  // 剂量单位
                    PinYinCode = item.regularSpellCode,  // 拼音码
                    HighRiskFlag = item.dangerFlag == "1" ? "高危药品" : "",
                };

                decimal DosePreUnit;
                bool success = decimal.TryParse(item.baseDose, out DosePreUnit);
                if (!success)
                {
                    // 处理转换失败的情况，例如赋一个默认值或抛出错误
                    DosePreUnit = 0; // 默认值，或者做其他错误处理
                }
                drugDict.DosePreUnit = DosePreUnit;
                list.Add(drugDict);
            }
            return list;
        }

        #endregion

        /// <summary>
        /// 通过插件同步CA
        /// </summary>
        /// <param name="caUserInfo"></param>
        /// <returns></returns>
        public async Task<bool> SyncCAByPlugin(PluginCAView caUserInfo)
        {
            if (caUserInfo == null || string.IsNullOrEmpty(caUserInfo.CAUserID) || string.IsNullOrEmpty(caUserInfo.StampImageBase64))
            {
                return false;
            }
            try
            {
                _logger.Info($"通过插件同步CA数据，caUserInfo：{ListToJson.ToJson(caUserInfo)}");
                //根据身份证获取用户信息
                var idenno = caUserInfo.CAUserID[2..];
                var employeeCAViews = new List<EmployeeCAView>();
                var employeeID = await _employeeDataRepository.GetEmployeeIDByIDCard(idenno);
                if (string.IsNullOrEmpty(employeeID))
                {
                    _logger.Info($"通过插件同步CA数据，未查询到用户信息，身份证号：{idenno}");
                    return false;
                }
                //组装EmployeeCAData数据
                var employeeCAView = CreateEmployeeCAView(employeeID, caUserInfo.CAUserID, caUserInfo.StampImageBase64);
                employeeCAViews.Add(employeeCAView);
                //通过接口同步CA数据
                if (employeeCAViews.Count > 0)
                {
                    _logger.Info($"通过插件同步CA数据，SyncEmployeeCAData：{ListToJson.ToJson(employeeCAViews)}");
                    await _requestApiService.RequestAPI("SyncEmployeeCAData", ListToJson.ToJson(employeeCAViews));
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"通过插件同步CA数据失败，异常：{ex}");
                return false;
            }
        }
    }
}
