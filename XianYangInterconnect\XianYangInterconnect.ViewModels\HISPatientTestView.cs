﻿namespace XianYangInterconnect.ViewModels
{
    public class PatientTestResultView
    {
        /// <summary>
        /// 住院号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 检验号
        /// </summary>
        public string TestNo { get; set; }
        /// <summary>
        /// 检验码
        /// </summary>
        public string TestCode { get; set; }
        /// <summary>
        /// 检验日期
        /// </summary>
        public DateTime TestDate { get; set; }
        /// <summary>
        /// 检验项目
        /// </summary>
        public string TestItem { get; set; }
        /// <summary>
        /// 检验值
        /// </summary>
        public string TestValue { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// 值范围
        /// </summary>
        public string NormalRange { get; set; }
        /// <summary>
        /// 值高低
        /// </summary>
        public string NormalAbnormal { get; set; }
        /// <summary>
        /// 检验大项
        /// </summary>
        public string TestGroupName { get; set; }
        /// <summary>
        /// 检验大项码
        /// </summary>
        public string TestGroupCode { get; set; }
        /// <summary>
        /// 检体
        /// </summary>
        public string Specimen { get; set; }
        /// <summary>
        /// 检体编码
        /// </summary>
        public string SpecimenCode { get; set; }


    }
    public class HISPatientTestReport
    {
        /// <summary>
        /// 主键testCode
        /// </summary>
        public string testCode { get; set; }

        /// <summary>
        /// 标本核准者编码
        /// </summary>
        public string approverId { get; set; }

        /// <summary>
        /// 标本核准者姓名
        /// </summary>
        public string approverName { get; set; }

        /// <summary>
        /// 标本核准时间
        /// </summary>
        public DateTime? approveTime { get; set; }

        /// <summary>
        /// 条形码
        /// </summary>
        public string barCode { get; set; }

        /// <summary>
        /// 开立科室名称
        /// </summary>
        public string deptName { get; set; }

        /// <summary>
        /// 执行单号
        /// </summary>
        public string execSqn { get; set; }

        /// <summary>
        /// 收费项目编码
        /// </summary>
        public string hisItemIdList { get; set; }

        /// <summary>
        /// 收费项目名称列表
        /// </summary>
        public string hisItemNameList { get; set; }

        /// <summary>
        /// 检验科接收时间
        /// </summary>
        public DateTime? inceptTime { get; set; }

        /// <summary>
        /// 检验时间
        /// </summary>
        public DateTime? inspectionDate { get; set; }

        /// <summary>
        /// 报告名
        /// </summary>
        public string inspectionName { get; set; }

        /// <summary>
        /// 检验结果
        /// </summary>
        public string inspectionResult { get; set; }

        /// <summary>
        /// 检验报告流水号
        /// </summary>
        public string inspectionSn { get; set; }

        /// <summary>
        /// 机构英文名称
        /// </summary>
        public string institutionEname { get; set; }

        /// <summary>
        /// 本级机构
        /// </summary>
        public string institutionId { get; set; }

        /// <summary>
        /// 机构名称
        /// </summary>
        public string institutionName { get; set; }

        /// <summary>
        /// 是否临床药物试验
        /// </summary>
        public string isGcp { get; set; }

        /// <summary>
        /// 检验项目编码
        /// </summary>
        public string itemCode { get; set; }

        /// <summary>
        /// 检验项目名称
        /// </summary>
        public string itemName { get; set; }

        /// <summary>
        /// Lis备注信息
        /// </summary>
        public string lisMemo { get; set; }

        /// <summary>
        /// 机器码
        /// </summary>
        public string machineCode { get; set; }

        /// <summary>
        /// 开立时间
        /// </summary>
        public DateTime? orderTime { get; set; }

        /// <summary>
        /// 医嘱流水号
        /// </summary>
        public string ordSn { get; set; }

        /// <summary>
        /// 患者ID
        /// </summary>
        public string patientId { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string patientName { get; set; }

        /// <summary>
        /// 患者性别
        /// </summary>
        public string patientSex { get; set; }

        /// <summary>
        /// 报告资源文件
        /// </summary>
        public string resourceFile { get; set; }

        /// <summary>
        /// 结果时间
        /// </summary>
        public DateTime resultTime { get; set; }

        /// <summary>
        /// 样本号
        /// </summary>
        public string sampleId { get; set; }

        /// <summary>
        /// 标本状态
        /// </summary>
        public string sampleState { get; set; }

        /// <summary>
        /// 采样时间
        /// </summary>
        public DateTime? sampleTime { get; set; }

        /// <summary>
        /// 样本类型
        /// </summary>
        public string sampleType { get; set; }

        /// <summary>
        /// 标本名称
        /// </summary>
        public string specimenName { get; set; }

        /// <summary>
        /// 就诊ID
        /// </summary>
        public string visitedId { get; set; }

        /// <summary>
        /// 就诊类型
        /// </summary>
        public string visitTypeCode { get; set; }
    }
    public class HISPatientTestResult
    {
        /// <summary>
        /// id
        /// </summary>
        public string id { get; set; }

        /// <summary>
        /// 危急值标识
        /// </summary>
        public bool? alertFlag { get; set; }

        /// <summary>
        /// 标本核准者编码
        /// </summary>
        public string approverId { get; set; }

        /// <summary>
        /// 标本核准者姓名
        /// </summary>
        public string approverName { get; set; }

        /// <summary>
        /// 条码号
        /// </summary>
        public string barcode { get; set; }

        /// <summary>
        /// 大项目编码
        /// </summary>
        public string comItemCode { get; set; }

        /// <summary>
        /// 大项目名称
        /// </summary>
        public string comItemName { get; set; }

        /// <summary>
        /// 科室编码
        /// </summary>
        public string deptCode { get; set; }

        /// <summary>
        /// 科室名称
        /// </summary>
        public string deptName { get; set; }

        /// <summary>
        /// 业务来源
        /// </summary>
        public string encounterClass { get; set; }

        /// <summary>
        /// 收费项目编码
        /// </summary>
        public string hisItemId { get; set; }

        /// <summary>
        /// 检验时间
        /// </summary>
        public string inspectionDate { get; set; }

        /// <summary>
        /// 检验结果
        /// </summary>
        public string inspectionResult { get; set; }

        /// <summary>
        /// 参考范围
        /// </summary>
        public string inspectionResultRange { get; set; }

        /// <summary>
        /// 检验结果单位名称
        /// </summary>
        public string inspectionResultUnit { get; set; }

        /// <summary>
        /// 检验报告流水号
        /// </summary>
        public string inspectionSn { get; set; }

        /// <summary>
        /// 机构英文名称
        /// </summary>
        public string institutionename { get; set; }

        /// <summary>
        /// 本级机构
        /// </summary>
        public string institutionid { get; set; }

        /// <summary>
        /// 机构名称
        /// </summary>
        public string institutionname { get; set; }

        /// <summary>
        /// 检验项目编码
        /// </summary>
        public string itemCode { get; set; }

        /// <summary>
        /// 项目名称英文
        /// </summary>
        public string itemEname { get; set; }

        /// <summary>
        /// 检验方法
        /// </summary>
        public string itemMethod { get; set; }

        /// <summary>
        /// 检验项目名称
        /// </summary>
        public string itemName { get; set; }

        /// <summary>
        /// 仪器ID
        /// </summary>
        public string machineId { get; set; }

        /// <summary>
        /// 医嘱流水号
        /// </summary>
        public string ordSn { get; set; }

        /// <summary>
        /// 患者ID
        /// </summary>
        public string patientId { get; set; }

        /// <summary>
        /// 报告打印时间
        /// </summary>
        public string printTime { get; set; }

        /// <summary>
        /// 危急值范围
        /// </summary>
        public string rangeLimit { get; set; }

        /// <summary>
        /// 报告日期
        /// </summary>
        public DateTime? reportDate { get; set; }

        /// <summary>
        /// 报告科室编码
        /// </summary>
        public string reportDeptCode { get; set; }

        /// <summary>
        /// 报告科室名称
        /// </summary>
        public string reportDeptName { get; set; }

        /// <summary>
        /// 报告医师ID、文档作者编码、检验报告医师
        /// </summary>
        public string reportDoctorCode { get; set; }

        /// <summary>
        /// 报告医师姓名
        /// </summary>
        public string reportDoctorName { get; set; }

        /// <summary>
        /// 检查结果指标说明
        /// </summary>
        public string resultContext { get; set; }

        /// <summary>
        /// 定性定量标识编码 Quan定量 Qual定性 QuanPlusQual 定量+定性
        /// </summary>
        public string resultNatureCode { get; set; }

        /// <summary>
        /// 定性定量标识名称
        /// </summary>
        public string resultNatureName { get; set; }

        /// <summary>
        /// 结果状态符号
        /// </summary>
        public string resultSign { get; set; }

        /// <summary>
        /// 低于参考值下
        /// </summary>
        public string resultStateClass { get; set; }

        /// <summary>
        /// 自定义危急值
        /// </summary>
        public string resultStateClassName { get; set; }

        /// <summary>
        /// 样本号
        /// </summary>
        public string sampleid { get; set; }

        /// <summary>
        /// 样本类型
        /// </summary>
        public string sampleType { get; set; }

        /// <summary>
        /// 检验项目序号
        /// </summary>
        public string sortNo { get; set; }

        /// <summary>
        /// 主表主键
        /// </summary>
        public string testCode { get; set; }

        /// <summary>
        /// 检验医师工号
        /// </summary>
        public string testDocCode { get; set; }

        /// <summary>
        /// 检验医师姓名
        /// </summary>
        public string testDocName { get; set; }

        /// <summary>
        /// 患者流水号
        /// </summary>
        public string visitedId { get; set; }

        /// <summary>
        /// 1：门诊 2：住院
        /// </summary>
        public string visitTypeCode { get; set; }
    }

}
