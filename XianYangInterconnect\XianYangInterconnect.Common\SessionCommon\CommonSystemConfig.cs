﻿using System.ComponentModel;

namespace XianYangInterconnect.Common.SessionCommon
{
    /// <summary>
    /// 配置参数
    /// </summary>
    public class CommonSystemConfig
    {
        /// <summary>
        /// 医院代码
        /// </summary>
        [Description("医院代码")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        [Description("语言")]
        public int Language { get; set; }

        /// <summary>
        /// 默认系统操作人
        /// </summary>
        public string SystemOperator { get; set; }

        /// <summary>
        /// 根据Casenumber获取转科记录
        /// </summary>
        public string GetInPatientLogByCaseNumber { get; set; }

        /// <summary>
        /// 白名单
        /// </summary>
        public string WhiteListIPs { get; set; }

        ///// <summary>
        ///// 默认客户端类型
        ///// </summary>
        public int PCClientType { get; set; }

        /// <summary>
        /// 系统使用缓存类型
        /// </summary>
        public string UseCacheType { get; set; }
    }
}