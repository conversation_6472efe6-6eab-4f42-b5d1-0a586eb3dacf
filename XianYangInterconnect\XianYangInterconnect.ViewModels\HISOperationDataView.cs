﻿namespace XianYangInterconnect.ViewModels
{
    public class HISOperationDataResponse
    {
        public long code { get; set; }
        public List<HISOperationDataView> data { get; set; }
        public string msg { get; set; }
    }
    public class HISOperationDataView
    {
        /// <summary>
        /// 申请编码
        /// </summary>
        public string applyCodeHis { get; set; }

        /// <summary>
        /// 手术室编码
        /// </summary>
        public string theatreCode { get; set; }

        /// <summary>
        /// 手术室名称
        /// </summary>
        public string theatreName { get; set; }

        /// <summary>
        /// 房间编码
        /// </summary>
        public string roomCode { get; set; }

        /// <summary>
        /// 房间名称
        /// </summary>
        public string roomName { get; set; }

        /// <summary>
        /// 患者ID
        /// </summary>
        public string patientId { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string patientName { get; set; }

        /// <summary>
        /// 手术台次
        /// </summary>
        public string operationIndex { get; set; }

        /// <summary>
        /// 是否首次
        /// </summary>
        public string isFrist { get; set; }

        /// <summary>
        /// 排班时间
        /// </summary>
        public string rosteringTime { get; set; }
    }
}