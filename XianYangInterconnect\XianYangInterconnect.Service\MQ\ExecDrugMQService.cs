﻿using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using NLog;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;
using XianYangInterconnect.Service.Interface.MQ;
using XianYangInterconnect.ViewModels;
namespace XianYangInterconnect.Service.MQ
{
    public class ExecDrugMQService : IExecDrugMQService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IExecDrugRepository _execDrugRepository;
        private readonly IMemoryCache _memoryCache;
        private readonly MemoryCacheCommon _memoryCacheCommon;
        private readonly DataOutContext _dataOutContext;
        public ExecDrugMQService(IMemoryCache memoryCache
            , MemoryCacheCommon memoryCacheCommon
            , IExecDrugRepository execDrugRepository
            , DataOutContext dataOutContext)
        {
            _memoryCache = memoryCache;
            _memoryCacheCommon = memoryCacheCommon;
            _execDrugRepository = execDrugRepository;
            _dataOutContext = dataOutContext;
        }

        public async Task<bool> SetExecDrugMQ(int minute)
        {
            var syncExecsqns = new List<string>();
            var memoryCachKey = "Patient_execsqns";
            var patientExecsqnViews = await _execDrugRepository.GetByUseTime(minute);
            var execsqns = patientExecsqnViews.Select(m => m.Execsqn).Distinct().ToList();
            if (execsqns.Count == 0)
            {
                _logger.Info($"SetExecDrugMQ没有获取到给药拆分数据");
                return true;
            }
            syncExecsqns = GetSyncExecsqn(memoryCachKey, execsqns);
            if (syncExecsqns.Count <= 0)
            {
                return true;
            }
            var casenumber = patientExecsqnViews.Where(m => syncExecsqns.Contains(m.Execsqn)).Select(m => m.Casenumber).ToList();
            casenumber = casenumber.Distinct().ToList();
            ///写MQ消息
            var resultFlag = await SetExecDrugsMQ(casenumber);
            if (!resultFlag)
            {
                return false;
            }
            _memoryCacheCommon.SetCacheData(memoryCachKey, execsqns, TimeSpan.FromMinutes(30));
            return true;
        }

        /// <summary>
        /// 发送医嘱执行MQ消息
        /// </summary>
        /// <param name="casenumber"></param>
        /// <returns></returns>
        private async Task<bool> SetExecDrugsMQ(List<string> casenumber)
        {
            foreach (var item in casenumber)
            {
                var syncData = ListToJson.ToJson(GetMessageView(item));
                var syncDataLogInfo = new SyncDataLogInfo
                {                    
                    HospitalID = "8",
                    SyncDataType = "PatientOrderExecute",
                    EventName = "SHZHY_HLCCC_createOrderExecuteForm",
                    SyncData = syncData,
                    CaseNumber = item,
                    AddDate = DateTime.Now,
                    ModifyDate = DateTime.Now,
                    ModifyPersonID = "Interconnect",
                    AddPersonID = "Interconnect",
                    DeleteFlag = "",
                    DataPumpFlag = "",
                    Counts = 0
                };
                await _dataOutContext.AddAsync(syncDataLogInfo);
            }
            try
            {
                await _dataOutContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.Error("新增数据同步记录错误" + ex.ToString());
                return false;
            }
            return true;
        }

        /// <summary>
        /// 创建消息对象
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        private MessageView GetMessageView(string casenumber)
        {
            return new MessageView()
            {
                MessageID = "",
                HospitalId = "8",
                CreationTime = DateTime.Now,
                MessageType = "PatientOrderExecute",
                EventName = "SHZHY_HLCCC_createOrderExecuteForm",
                InpatientView = new InpatientView() { CaseNumber = casenumber },
                PatientOrderExecuteView = new PatientOrderExecuteView() { CaseNumber = casenumber }
            };
        }

        /// <summary>
        /// 获取需要同步的 
        /// </summary>
        /// <param name="key"></param>
        /// <param name="Execsqn">执行单流水号</param>
        /// <returns></returns>
        private List<string> GetSyncExecsqn(string key, List<string> execsqns)
        {
            //获取缓存中存储的Key
            var datas = _memoryCache.Get(key);
            if (datas == null)
            {
                return execsqns;
            }
            string json = JsonConvert.SerializeObject(datas);
            var cacheexecsqns = ListToJson.ToList<List<string>>(json);
            execsqns = execsqns.Where(m => !cacheexecsqns.Contains(m)).ToList();
            return execsqns;
        }
    }
}
