﻿namespace XianYangInterconnect.ViewModels
{
    public class PatientMachineDataView
    {
        /// <summary>
        /// 患者流水号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 测量时间
        /// </summary>
        public DateTime MeasureTime { get; set; }
        /// <summary>
        /// 体温 °C 
        /// </summary>
        public string Temperature { get; set; }
        /// <summary>
        /// 测温方式
        /// </summary>
        public string TemperatureMethod { get; set; }
        /// <summary>
        /// 脉搏(脉率) 次/分
        /// </summary>
        public string Pulse { get; set; }
        /// <summary>
        /// 心率 次/分
        /// </summary>
        public string HeartRate { get; set; }
        /// <summary>
        /// 呼吸 次/分
        /// </summary>
        public string Breathing { get; set; }
        /// <summary>
        /// 收缩压 mmHg
        /// </summary>
        public string SystolicPressure { get; set; }
        /// <summary>
        /// 舒张压 mmHg
        /// </summary>
        public string DiastolicPressure { get; set; }
        /// <summary>
        /// 血氧饱和度 %
        /// </summary>
        public string SPO2 { get; set; }
    }
}
