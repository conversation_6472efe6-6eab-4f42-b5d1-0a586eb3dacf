﻿using Microsoft.Extensions.Options;
using NLog;
using System.Text;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models.HIS;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Services.Interface;
using XianYangInterconnect.ViewModels;
using XianYangInterconnect.ViewModels.Medical;

namespace XianYangInterconnect.Service
{
    public class PatientMedicineScheduleService : IPatientMedicineScheduleService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IStationListRepository _stationListRepository;
        private readonly IOptions<SystemConfig> _config;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IRequestApiService _requestApiService;
        private readonly ISyncDatasLogRepository _syncDatasLogRepository;
        private readonly ISyncDatasLogServices _syncDatasLogServices;
        private readonly IExecDrugRepository _execDrugRepository;
        private readonly ISettingDescriptionRepository _settingDescriptionRepository;

        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        public PatientMedicineScheduleService(IAppConfigSettingRepository appConfigSettingRepository
            , IStationListRepository stationListRepository
            , IOptions<SystemConfig> config
            , IInpatientDataRepository inpatientDataRepository
            , IRequestApiService requestApiService
            , ISyncDatasLogRepository syncDatasLogRepository
            , ISyncDatasLogServices syncDatasLogServices
            , IExecDrugRepository execDrugRepository
            , ISettingDescriptionRepository settingDescriptionRepository)
        {
            _appConfigSettingRepository = appConfigSettingRepository;
            _stationListRepository = stationListRepository;
            _config = config;
            _inpatientDataRepository = inpatientDataRepository;
            _requestApiService = requestApiService;
            _syncDatasLogRepository = syncDatasLogRepository;
            _syncDatasLogServices = syncDatasLogServices;
            _execDrugRepository = execDrugRepository;
            _settingDescriptionRepository = settingDescriptionRepository;
        }

        /// <summary>
        /// 根据病区同步患者用药信息
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="caseNumber"></param>
        /// <param name="syncType"></param>
        /// <param name="timeDiff"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientMedicineScheduleByStationID(int? stationID, DateTime? beginTime, DateTime? endTime, string caseNumber)
        {
            var syncSuccessFlag = true;
            if (!beginTime.HasValue)
            {
                beginTime = DateTime.Now.Date.AddDays(-1);
            }
            if (!endTime.HasValue)
            {
                endTime = DateTime.Now.Date.AddDays(2);
            }
            var api = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetHISPatientMedicineScheduleAPI");
            //api = "https://localhost:1056/api/8/Patient/GetPatientMedicine";
            if (string.IsNullOrWhiteSpace(api))
            {
                _logger.Error("根据病区同步患者用药信息失败，AppConfigSetting未配置获取HIS药嘱的API，GetHISPatientMedicineScheduleAPI");
                return false;
            }
            var stationList = await _stationListRepository.GetStationList(_config.Value.HospitalID);
            if (stationList.Count == 0)
            {
                _logger.Error("根据病区同步患者用药信息失败，未获取到病区字典数据");
                return false;
            }
            if (stationID.HasValue)
            {
                stationList = stationList.Where(m => m.ID == stationID.Value).ToList();
                if (stationList.Count == 0)
                {
                    _logger.Error($"根据病区同步患者用药信息失败，未获取到指定病区字典数据，stationID={stationID.Value}");
                    return false;
                }
            }
            var syncType = "API";
            var stationIDSetting = await _settingDescriptionRepository.GetBySettingTypeCode("SyncStationMedicineByView");
            var timeDiff = 60;
            var timeDiffSetting = await _settingDescriptionRepository.GetTypeValue("SyncStationMedicineByViewTimeDiff");
            if (!string.IsNullOrEmpty(timeDiffSetting))
            {
                int.TryParse(timeDiffSetting, out timeDiff);
            }
            foreach (var station in stationList)
            {
                var caseNumberList = await _inpatientDataRepository.GetCaseNumberListByStationID(station.ID, _config.Value.HospitalID);
                if (caseNumberList != null && caseNumberList.Count == 0)
                {
                    _logger.Error($"根据病区同步患者用药信息失败,未获取到指定病区患者数据，stationID={station.ID}");
                    continue;
                }
                if (!string.IsNullOrEmpty(caseNumber))
                {
                    caseNumberList = caseNumberList.Where(m => m == caseNumber).ToList();
                }
                syncType = stationIDSetting?.FirstOrDefault(m => m.TypeValue == station.ID.ToString()) != null ? "VIEW" : syncType;
                foreach (var item in caseNumberList)
                {
                    await (syncType == "API"
                        ? SyncPatientMedicineScheduleByAPIAsync(api, item, beginTime, endTime)
                        : SyncPatientMedicineScheduleByViewAsync(item, timeDiff, beginTime.Value, endTime.Value));
                }
            }
            return syncSuccessFlag;
        }

        /// <summary>
        /// 根据病区同步患者用药信息View
        /// </summary>
        /// <param name="item"></param>
        /// <param name="timeDiff"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        private async Task SyncPatientMedicineScheduleByViewAsync(string item, int timeDiff, DateTime beginTime, DateTime endTime)
        {
            var lastDateTime = DateTime.Now.AddMinutes(-timeDiff);
            var hisData = await _execDrugRepository.GetByUseTime(item, beginTime, endTime);
            if (hisData.Count == 0)
            {
                _logger.Error($"根据病区同步VIEW患者用药信息失败,未获取到指定病区患者数据,CaseNumber={item}");
                return;
            }
            // HIS患者用药信息转换为CCC标准格式
            var cccData = ConvertHISMedicineScheduleViewToCCC(hisData);
            if (cccData.Count == 0)
            {
                _logger.Error($"根据病区同步API患者用药信息失败,HIS患者用药信息转换为CCC标准格式失败,hisData={ListToJson.ToJson(hisData)}");
                return;
            }
            // 调用药嘱标准接口同步数据
            await _requestApiService.RequestAPI("SyncPatientMedicineSchedule", ListToJson.ToJson(cccData), null, 300);
        }

        /// <summary>
        /// 根据病区同步患者用药信息API
        /// </summary>
        /// <param name="api"></param>
        /// <param name="item"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        private async Task SyncPatientMedicineScheduleByAPIAsync(string api, string item, DateTime? beginTime, DateTime? endTime)
        {
            // 获取HIS患者用药信息
            var hisData = await GetHISPatientMedicineScheduleAsync(api, item, beginTime, endTime);
            if (hisData.Count == 0)
            {
                _logger.Error($"根据病区同步API患者用药信息失败,未获取到指定病区患者数据,CaseNumber={item}");
                return;
            }
            // HIS患者用药信息转换为CCC标准格式
            var cccData = ConvertHISPatientMedicineScheduleToCCC(hisData);
            if (cccData.Count == 0)
            {
                _logger.Error($"根据病区同步API患者用药信息失败,HIS患者用药信息转换为CCC标准格式失败,hisData={ListToJson.ToJson(hisData)}");
                return;
            }
            // 调用药嘱标准接口同步数据
            await _requestApiService.RequestAPI("SyncPatientMedicineSchedule", ListToJson.ToJson(cccData), null, 300);
        }

        /// <summary>
        /// 转化HIS患者用药信息为CCC标准格式
        /// </summary>
        /// <param name="hisData"></param>
        /// <returns></returns>
        private static List<PatientMedicineScheduleView> ConvertHISMedicineScheduleViewToCCC(List<ExecDrug> hisData)
        {
            return hisData.Select(hisMedicine =>
            {
                var typeCode = GetOrderType(hisMedicine.TypeCode);
                if (string.IsNullOrWhiteSpace(typeCode))
                {
                    _logger.Error($"长期临时药嘱类型为空,ExecSqn:{hisMedicine.ExecSqn},InpatientNo:{hisMedicine.InpatientNo}");
                    return null;
                }

                var isCanceled = hisMedicine.ValidFlag == "0";
                int.TryParse(hisMedicine.ExecSqn, out int sort);
                var cccMedicine = new PatientMedicineScheduleView
                {
                    // 患者信息
                    PatientMedicineScheduleID = Guid.NewGuid().ToString("N"),
                    CaseNumber = hisMedicine.InpatientNo,
                    ChartNo = hisMedicine.PatientNo,
                    StationCode = hisMedicine.NurseCellCode,

                    // 药品信息
                    PatientOrderMainID = hisMedicine.CombNo,
                    PatientOrderDetailID = hisMedicine.ExecSqn,
                    OrderCode = hisMedicine.MoOrder,
                    DrugCode = hisMedicine.DrugCode,
                    HISOrderGroupNo = hisMedicine.CombNo,
                    HISOrderSort = sort,
                    OrderType = typeCode,

                    // 三查七对信息
                    OrderContent = hisMedicine.DrugName,
                    OrderDescription = hisMedicine.MoNote1 + hisMedicine.MoNote2,
                    AmountText = hisMedicine.QtyTot + hisMedicine.PriceUnit,
                    DrugSpec = hisMedicine.Specs,
                    OrderDose = hisMedicine.BaseDose,
                    Unit = hisMedicine.DoseUnit,
                    TotalVolume = hisMedicine.DoseOnce,
                    OrderRule = hisMedicine.UseName,

                    // 给药时间
                    GroupID = $"{hisMedicine.CombNo}||{hisMedicine.UseTime:yyyyMMddHHmmss}",
                    ScheduleDate = hisMedicine.UseTime.Date,
                    ScheduleTime = hisMedicine.UseTime.TimeOfDay,
                    Frequency = hisMedicine.FrequencyName,

                    // 状态信息
                    OrderStatus = isCanceled ? 3 : 2,
                    AddDate = DateTime.Now,
                    AddEmployeeID = hisMedicine.DocCode ?? "",
                    StartDateTime = hisMedicine.OrderStartDateTime,
                    StopDate = isCanceled && hisMedicine.ValidDate.HasValue ? hisMedicine.ValidDate?.Date : null,
                    StopTime = isCanceled && hisMedicine.ValidDate.HasValue ? hisMedicine.ValidDate?.TimeOfDay : null,
                    StopEmployeeID = isCanceled && !string.IsNullOrEmpty(hisMedicine.ValidUserCd) ? hisMedicine.ValidUserCd : "",

                    // 其他信息
                    DrugAttention = hisMedicine.DrugAttention ?? "",
                    Location = "",
                    HighRiskFlag = hisMedicine.HighRiskFlag ?? "",
                    FirstDayFlag = hisMedicine.FirstDayFlag ?? "",
                    Speed = hisMedicine.Speed ?? "",
                    BillingAttribution = "0", // 咸阳不允许自带药
                    Package = hisMedicine.QtyTot
                };

                return cccMedicine;
            }).Where(medicine => medicine != null).ToList();
        }

        /// <summary>
        /// 获取医嘱类型
        /// </summary>
        /// <param name="typeCode"></param>
        /// <returns></returns>
        private static string GetOrderType(string typeCode)
        {
            var hisMedicineTypeCode = "";
            switch (typeCode)
            {
                case "LZ":
                case "ZL":
                    hisMedicineTypeCode = "0";
                    break;

                case "CZ":
                case "ZC":
                    hisMedicineTypeCode = "1";
                    break;

                default:
                    hisMedicineTypeCode = "0";
                    break;
            }
            return hisMedicineTypeCode;
        }

        /// <summary>
        /// HIS患者用药信息转换为CCC标准格式
        /// </summary>
        /// <param name="hisData"></param>
        /// <returns></returns>
        private static List<PatientMedicineScheduleView> ConvertHISPatientMedicineScheduleToCCC(List<HISPatientMedicineSchedule> hisData)
        {
            var cccData = new List<PatientMedicineScheduleView>();
            foreach (var hisMedicine in hisData)
            {
                var cccMedicine = new PatientMedicineScheduleView();
                cccMedicine.PatientMedicineScheduleID = Guid.NewGuid().ToString("N");
                //患者信息
                cccMedicine.CaseNumber = hisMedicine.InpatientNo;
                cccMedicine.ChartNo = hisMedicine.PatientNo;
                cccMedicine.StationCode = hisMedicine.NurseCellCode;
                //药品信息
                cccMedicine.PatientOrderMainID = hisMedicine.CombNo;
                cccMedicine.PatientOrderDetailID = hisMedicine.ExecSqn;
                cccMedicine.OrderCode = hisMedicine.MoOrder;
                cccMedicine.DrugCode = hisMedicine.DrugCode;
                cccMedicine.HISOrderGroupNo = hisMedicine.CombNo;
                int.TryParse(hisMedicine.ExecSqn, out int sort);
                cccMedicine.HISOrderSort = sort;
                var typeCode = GetOrderType(hisMedicine.TypeCode);
                if (string.IsNullOrWhiteSpace(typeCode))
                {
                    _logger.Error($"长期临时药嘱类型为空,ExecSqn:{hisMedicine.ExecSqn},InpatientNo:{hisMedicine.InpatientNo}");
                    continue;
                }
                cccMedicine.OrderType = typeCode;
                //三查七对-药品名称及其备注
                cccMedicine.OrderContent = hisMedicine.DrugName;
                cccMedicine.OrderDescription = hisMedicine.MoNote1 + hisMedicine.MoNote2;
                //三查七对-药品规格剂量
                cccMedicine.AmountText = hisMedicine.QtyTot + hisMedicine.PriceUnit;
                cccMedicine.DrugSpec = hisMedicine.Specs;
                cccMedicine.OrderDose = hisMedicine.BaseDose;
                cccMedicine.Unit = hisMedicine.DoseUnit;
                cccMedicine.TotalVolume = hisMedicine.DoseOnce;
                //三查七对-药品用法给药途径
                cccMedicine.Location = "";
                cccMedicine.OrderRule = hisMedicine.UseName;
                //三查七对-给药时间频次
                if (string.IsNullOrEmpty(hisMedicine.UseTime) || !DateTime.TryParse(hisMedicine.UseTime, out DateTime scheduleDateTime))
                {
                    _logger.Error($"用药时间为空,UseTime:{hisMedicine.UseTime},InpatientNo:{hisMedicine.InpatientNo}");
                    continue;
                }
                cccMedicine.GroupID = hisMedicine.CombNo + "||" + scheduleDateTime.ToString("yyyyMMddHHmmss");
                cccMedicine.ScheduleDate = scheduleDateTime.Date;
                cccMedicine.ScheduleTime = scheduleDateTime.TimeOfDay;
                cccMedicine.Frequency = hisMedicine.FrequencyName;
                //三查七对-注意
                cccMedicine.DrugAttention = "";
                //状态
                cccMedicine.AddDate = DateTime.Now;
                cccMedicine.AddEmployeeID = string.IsNullOrEmpty(hisMedicine.DocCode) ? "" : hisMedicine.DocCode;
                //开立
                if (!string.IsNullOrEmpty(hisMedicine.MoDate) && DateTime.TryParse(hisMedicine.MoDate, out DateTime startDateTime))
                {
                    cccMedicine.StartDateTime = startDateTime;
                }
                // 优化HIS药品状态判断逻辑
                bool isInvalid = hisMedicine.ValidFlag == "0";
                cccMedicine.OrderStatus = isInvalid ? 3 : 2;
                if (isInvalid && DateTime.TryParse(hisMedicine.ValidDate, out DateTime validDate))
                {
                    cccMedicine.StopDate = validDate.Date;
                    cccMedicine.StopTime = validDate.TimeOfDay;
                    cccMedicine.StopEmployeeID = hisMedicine.ValidUsercd ?? "";
                }
                cccMedicine.HighRiskFlag = "";
                cccMedicine.FirstDayFlag = "";
                cccMedicine.Speed = "";
                cccMedicine.BillingAttribution = "0";//咸阳不允许自带药
                cccMedicine.Package = hisMedicine.QtyTot;
                cccData.Add(cccMedicine);
            }
            return cccData;
        }

        /// <summary>
        /// 获取HIS患者用药信息
        /// </summary>
        /// <param name="api"></param>
        /// <param name="caseNumber"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="execSqn"></param>
        /// <returns></returns>
        private static async Task<List<HISPatientMedicineSchedule>> GetHISPatientMedicineScheduleAsync(string api, string caseNumber, DateTime? beginTime, DateTime? endTime, string execSqn = "")
        {
            var hisPatientMedicineSchedules = new List<HISPatientMedicineSchedule>();
            var apiParams = new StringBuilder($"{api}?inpatientNo={caseNumber}");
            if (beginTime.HasValue)
            {
                apiParams.Append($"&beginTime={beginTime.Value}");
            }
            if (endTime.HasValue)
            {
                apiParams.Append($"&endTime={endTime.Value}");
            }
            if (!string.IsNullOrEmpty(execSqn))
            {
                apiParams.Append($"&execSqn={execSqn}");
            }
            try
            {
                string hisResult = await HttpHelper.HttpGetAsync(apiParams.ToString());
                var hisPatientMedicineScheduleView = ListToJson.ToList<HISPatientMedicineScheduleView>(hisResult);
                if (hisPatientMedicineScheduleView != null && hisPatientMedicineScheduleView.Data.Count > 0)
                {
                    hisPatientMedicineSchedules = hisPatientMedicineScheduleView.Data;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString());
                return hisPatientMedicineSchedules;
            }
            return hisPatientMedicineSchedules;
        }

        /// <summary>
        /// 根据LogID同步HIS患者用药信息
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientMedicine(string hospitalID)
        {
            var successFlag = true;
            var beginTime = DateTime.Now.Date.AddDays(-1);
            var endTime = DateTime.Now.Date.AddDays(2);
            var syncType = "API";
            var timeDiff = 60;
            var api = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetHISPatientMedicineScheduleAPI");
            //api = "https://localhost:1056/api/8/Patient/GetPatientMedicine";
            if (string.IsNullOrWhiteSpace(api))
            {
                _logger.Error("根据病区同步患者用药信息失败，AppConfigSetting未配置获取HIS药嘱的API，GetHISPatientMedicineScheduleAPI");
                return false;
            }
            var stationIDSetting = await _settingDescriptionRepository.GetBySettingTypeCode("SyncStationMedicineByView");
            var timeDiffSetting = await _settingDescriptionRepository.GetTypeValue("SyncStationMedicineByViewTimeDiff");
            if (!string.IsNullOrEmpty(timeDiffSetting))
            {
                int.TryParse(timeDiffSetting, out timeDiff);
            }
            // 获取需同步的数据
            var logIDs = await _syncDatasLogRepository.GetIDsByDataType(hospitalID, "PatientOrderExecute", 3);
            foreach (var logID in logIDs)
            {
                var (patientMedicineView, inpatientView) = await GetPatientMedicineByLogID(logID);
                if (patientMedicineView == null || inpatientView == null)
                {
                    successFlag = false;
                    _logger.Error($"同步患者用药信息失败，根据logID获取HIS患者用药信息为空，SyncDatasLogID={logID}");
                    await _syncDatasLogServices.ModifySyncDataLog(logID, false);
                    continue;
                }
                var inpatientDataInfo = await _inpatientDataRepository.GetInpatientAllDataView(inpatientView.CaseNumber);
                if (inpatientDataInfo == null)
                {
                    successFlag = false;
                    _logger.Error($"同步患者用药信息失败，根据CaseNumber{inpatientView.CaseNumber}获取当前在院患者为空，SyncDatasLogID={logID}");
                    await _syncDatasLogServices.ModifySyncDataLog(logID, false);
                    continue;
                }
                syncType = stationIDSetting?.FirstOrDefault(m => m.TypeValue == inpatientDataInfo.StationID.ToString()) != null ? "VIEW" : syncType;
                await (syncType == "API"
                    ? SyncPatientMedicineScheduleByAPIAsync(api, inpatientView.CaseNumber, beginTime, endTime)
                    : SyncPatientMedicineScheduleByViewAsync(inpatientView.CaseNumber, timeDiff, beginTime, endTime));
                await _syncDatasLogServices.ModifySyncDataLog(logID, true);
            }
            return successFlag;
        }

        /// <summary>
        /// 根据logID获取HIS患者用药信息
        /// </summary>
        /// <param name="logID"></param>
        /// <returns></returns>
        private async Task<(PatientOrderExecuteView, InpatientView)> GetPatientMedicineByLogID(int logID)
        {
            MessageView messageView = null;
            var logInfo = await _syncDatasLogRepository.GetSyncDataByID(logID);
            try
            {
                messageView = ListToJson.ToList<MessageView>(logInfo.SyncData);
                if (messageView == null || messageView.InpatientView == null || messageView.PatientOrderExecuteView == null)
                {
                    return (null, null);
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Json转换异常:" + ex.ToString() + "Json字符串:" + logInfo.SyncData);
                return (messageView.PatientOrderExecuteView, messageView.InpatientView);
            }
            return (messageView.PatientOrderExecuteView, messageView.InpatientView);
        }
    }
}