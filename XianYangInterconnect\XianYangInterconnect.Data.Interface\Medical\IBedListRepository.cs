﻿using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Interface.Medical
{
    public interface IBedListRepository
    {
        /// <summary>
        /// 最大ID
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<int>> GetBedListMaxID(string hospitalID);

        /// <summary>
        /// 获取字典列表
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<BedListInfo>> GetBedList(string hospitalID);
    }
}
