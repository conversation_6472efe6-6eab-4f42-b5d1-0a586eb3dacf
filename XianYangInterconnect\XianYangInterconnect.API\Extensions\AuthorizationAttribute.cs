﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using NLog;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.API.Extensions
{
    /// <summary>
    /// 需要验证
    /// </summary>
    public class AuthorizationAttribute : ActionFilterAttribute
    {
        private readonly Logger _logger;
        private IMemoryCache _cache;
        private readonly IOptions<SystemConfig> _config;

        /// <summary>
        /// 构造器注入
        /// </summary>
        /// <param name="cache"></param>
        /// <param name="config"></param>
        public AuthorizationAttribute(
            IMemoryCache cache
            , IOptions<SystemConfig> config)
        {
            _logger = LogManager.GetCurrentClassLogger();
            _cache = cache;
            _config = config;
        }

        /// <summary>
        /// 接口执行时调用
        /// </summary>
        /// <param name="context"></param>
        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var isDefined = false;
            var controllerActionDescriptor = context.ActionDescriptor as ControllerActionDescriptor;
            if (controllerActionDescriptor != null)
            {
                isDefined = controllerActionDescriptor.MethodInfo.GetCustomAttributes(inherit: true)
                   .Any(a => a.GetType().Equals(typeof(NoAuthorizationAttribute)));
            }
            if (isDefined)
            {
                base.OnActionExecuting(context);
                return;
            }
            //白名单
            var clientIP = context.HttpContext.Connection.RemoteIpAddress.ToString();
            string token = context.HttpContext.GetToken();
            if (token == null)
            {
                context.HttpContext.NoAuthorization();
                context.Result = new JsonResult("No Authorization");
                _logger.Info("No Authorization，请求未发现token，IP：" + clientIP);
                return;
            }
            if (_cache.Get(token) == null)
            {
                context.HttpContext.NoAuthorization();
                context.Result = new JsonResult("No Authorization");
                _logger.Info("No Authorization，服务器token不存在，IP：" + clientIP);
                return;
            }
            base.OnActionExecuting(context);
        }
    }
}