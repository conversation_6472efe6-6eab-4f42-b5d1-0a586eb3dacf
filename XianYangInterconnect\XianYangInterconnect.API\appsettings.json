{
  "ConnectionStrings": {
    //咸阳测试环境
    //"MedicalDataInConnection": "server=************;database=medical;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"HangFireConnection": "server=************;database=hangfire;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"FilesConnection": "server=************;database=Interconnect;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"DataOutConnection": "server=************;database=Interconnect;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"RedisConnection": "127.0.0.1:6379,password=ZhongYun`1q20230716,syncTimeout =20000,connectTimeout=3000,connectRetry=3,DefaultDatabase=6",
    //"HISConnection": "Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = *********)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = hisdb)));User ID=hisquery;Password=hisquery;",
    //"Cache": "127.0.0.1:6379"


    //"MedicalDataInConnection": "server=************;database=medical;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"HangFireConnection": "server=************;database=hangfire;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"FilesConnection": "server=************;database=Interconnect;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"DataOutConnection": "server=************;database=Interconnect;uid=sa;pwd=*****************;TrustServerCertificate=true",
    //"RedisConnection": "127.0.0.1:6379,password=ZhongYun`1q20230716,syncTimeout =20000,connectTimeout=3000,connectRetry=3,DefaultDatabase=6",
    //"HISConnection": "Data Source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = *********)(PORT = 1521)))(CONNECT_DATA =(SERVICE_NAME = hisdb)));User ID=hisquery;Password=hisquery;",
    //"Cache": "127.0.0.1:6379",

    "MedicalDataInconnection": "server=www.honlivit.com;database=medical_Dev;uid=zhongYunCCC;pwd=**`1q;trustservercertificate=true",
    "hangfireconnection": "server=www.honlivit.com;database=hangfire;uid=zhongYunCCC;pwd=**`1q;trustservercertificate=true",
    "filesconnection": "server=www.honlivit.com;database=interconnect;uid=zhongYunCCC;pwd=**`1q;trustservercertificate=true",
    "dataoutconnection": "server=www.honlivit.com;database=interconnect;uid=zhongYunCCC;pwd=**`1q;trustservercertificate=true",
    "redisconnection": "127.0.0.1:6379,password=zhongyun`1q20230716,synctimeout =20000,connecttimeout=3000,connectretry=3,defaultdatabase=6",
    "hisconnection": "data source=(description =(address_list =(address = (protocol = tcp)(host = *********)(port = 1521)))(connect_data =(service_name = hisdb)));user id=hisquery;password=hisquery;",
    "cache": "127.0.0.1:6379"
  },
  "Cache": {
    "Dictionary": 3000
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "Configs": {
    "HospitalID": "8",
    "Language": 1,
    "ServerType": 1,
    "UseCacheType": "Memory", //使用缓存类型：Redis、Memory
    "MaxSyncConcurrency": 100
  },
  "AllowedHosts": "*"
}