﻿namespace XianYangInterconnect.Common
{
    public static class TimeStamp
    {
        /// <summary>
        /// 获取当前毫秒级时间戳
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string GetTimeStampByMillisecond()
        {
            DateTimeOffset dto = new DateTimeOffset(DateTime.Now);
            return dto.ToUnixTimeMilliseconds().ToString();
        }
    }
}