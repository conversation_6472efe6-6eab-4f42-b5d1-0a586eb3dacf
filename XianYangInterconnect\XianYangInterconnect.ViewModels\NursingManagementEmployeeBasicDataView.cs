﻿using System.ComponentModel.DataAnnotations;

namespace XianYangInterconnect.ViewModels
{
    public class NursingManagementEmployeeBasicDataView
    {
        /// <summary>
        /// 员工ID
        /// </summary>
        [Required(ErrorMessage = "员工ID不能为空")]
        public string EmployeeID { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 籍贯
        /// </summary>
        public string NativePlace { get; set; }
        /// <summary>
        /// 身份证号
        /// </summary>
        public string IDCard { get; set; }
        /// <summary>
        /// 出生日期（时间戳）
        /// </summary>
        public long? BirthDay { get; set; }
        /// <summary>
        /// 民族
        /// </summary>
        public string Race { get; set; }
        /// <summary>
        /// 婚姻状况
        /// </summary>
        public string Marriage { get; set; }
        /// <summary>
        /// 职工性质
        /// </summary>
        public string HireCategory { get; set; }
        /// <summary>
        /// 职位
        /// </summary>
        public string Position { get; set; }
        /// <summary>
        /// 职称
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// 护理单元
        /// </summary>
        public string StationCode { get; set; }
        /// <summary>
        /// 人员类别
        /// </summary>
        public string EmployeeType { get; set; }
        /// <summary>
        /// 能力等级ID
        /// </summary>
        public string CapabilityLevelID { get; set; }
        /// <summary>
        /// 最高学历
        /// </summary>
        public string EducationDegree { get; set; }
        /// <summary>
        /// 最高学历毕业学校
        /// </summary>
        public string GraduatedSchool { get; set; }
        /// <summary>
        /// 入职日期（时间戳）
        /// </summary>
        public long? EntryDate { get; set; }
        /// <summary>
        /// 参加工作时间（时间戳）
        /// </summary>
        public long? JoinDate { get; set; }
        /// <summary>
        /// 是否离职
        /// </summary>
        public bool LeaveFlag { get; set; }
        /// <summary>
        /// 离职日期（时间戳）
        /// </summary>
        public long? LeaveDateTime { get; set; }
    }
}
