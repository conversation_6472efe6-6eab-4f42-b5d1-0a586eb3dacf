<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.10" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="StackExchange.Redis" Version="2.7.27" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\XianYangInterconnect.Data.Interface\XianYangInterconnect.Data.Interface.csproj" />
    <ProjectReference Include="..\XianYangInterconnect.Models\XianYangInterconnect.Models.csproj" />
    <ProjectReference Include="..\XianYangInterconnect.Service.Interface\XianYangInterconnect.Service.Interface.csproj" />
  </ItemGroup>
</Project>