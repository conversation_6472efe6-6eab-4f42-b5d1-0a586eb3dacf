﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XianYangInterconnect.Models.Base;

namespace XianYangInterconnect.Models
{
    [Serializable]
    [Table("APISetting")]
    public class APISettingInfo : ModifyInfo
    {
        /// <summary>
        ///主键
        ///</summary>
        [Key]
        public int APISettingID { get; set; }
        /// <summary>
        ///配置类别 1:Server(线上环境) 2、LocalHost（本地环境）、9:API 。3-8预留，用来配置不同的环境类别
        ///</summary>
        public int SettingType { get; set; }
        /// <summary>
        ///类型码,填写API的呼叫名称。这个会在程序里写死，不要改变
        ///</summary>
        [Column(TypeName = "varchar(50)")]
        public string SettingCode { get; set; }
        /// <summary>
        ///类型值(呼叫的具体API)
        ///</summary>
        [Column(TypeName = "varchar(100)")]
        public string SettingValue { get; set; }
        /// <summary>
        ///呼叫类别（1:Post,2:Get）
        ///</summary>
        public short CallType { get; set; }
        /// <summary>
        ///API属于那个系统。Medical、External
        ///</summary>
        [Column(TypeName = "varchar(20)")]
        public string SystemType { get; set; }
        /// <summary>
        ///API的服务地址，确定使用那个服务地址
        ///</summary>
        [Column(TypeName = "varchar(50)")]
        public string ServerCode { get; set; }
        /// <summary>
        ///说明
        ///</summary>
        [Column(TypeName = "varchar(200)")]
        public string Description { get; set; }
        /// <summary>
        ///负责人，填写负责人及联系方式
        ///</summary>
        [Column(TypeName = "varchar(200)")]
        public string PersonInCharge { get; set; }

    }
}

