﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using System.Xml;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Service.SyncBackHis;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    /// <summary>
    /// 回传注册患者体征数据
    /// </summary>
    public class SyncBackBodySurfaceService : SyncBackCommon, ISyncBackBodySurfaceService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork<DataOutContext> _unitOfWorkOut;
        private readonly IInterconnectAppConfigSettingRepository _interconnectAppConfigSettingRepository;
        private readonly ISynchronizeLogService _synchronizeLogService;

        public SyncBackBodySurfaceService(
            IUnitOfWork<DataOutContext> unitOfWorkOut,
            IInterconnectAppConfigSettingRepository interconnectAppConfigSettingRepository,
            ISynchronizeLogService synchronizeLogService)
        {
            _unitOfWorkOut = unitOfWorkOut;
            _interconnectAppConfigSettingRepository = interconnectAppConfigSettingRepository;
            _synchronizeLogService = synchronizeLogService;
        }

        /// <summary>
        /// 回传路由配置SettingCode
        /// </summary>
        private const string REQUEST_SETTING_CODE = "SyncBackBodySurface";
        /// <summary>
        /// 回传xml示例SettingCode
        /// </summary>
        private const string XML_DOCUMENT_SETTING_CODE = "SyncBackBodySurfaceXML";
        /// <summary>
        /// 回传生命体征数据
        /// </summary>
        /// <param name="vitalSignsView">生命体征数据</param>
        /// <returns></returns>
        public async Task<bool> SyncBackData(VitalSignsView vitalSignsView)
        {
            // 转换InterconnectCore中的数据转换为回传数据model类
            var bodySurfaceView = ConvertDataView(vitalSignsView);
            // 根据回传model类信息 构造xml格式的请求信息
            string message = await CreateRequestMessageAsync(bodySurfaceView);
            // 发送请求
            return await SendRequest(message, bodySurfaceView, bodySurfaceView.InpatientID, bodySurfaceView.CaseNumber);

        }
        /// <summary>
        /// 将InterconnectCore中的数据转换为回传数据model类
        /// </summary>
        /// <param name="vitalSignsView"></param>
        /// <returns></returns>
        private SyncPatientBodySurfaceView ConvertDataView(VitalSignsView vitalSignsView)
        {
            return new SyncPatientBodySurfaceView
            {
                InpatientID = vitalSignsView.InpatientID,
                ChartNo = vitalSignsView.ChartNo,
                CaseNumber = vitalSignsView.CaseNumber,
                HospitalCode = vitalSignsView.HospitalCode,
                HospitalName = vitalSignsView.HospitalName,
                VisitNo = vitalSignsView.ChartNo,
                VisitSqNo = vitalSignsView.CaseNumber,
                VisitTypeCode = VISIT_TYPE_CODE,
                VisitTypeName = VISIT_TYPE_NAME,
                //RecordNo = vitalSignsView.RecordNo,
                PatientName = vitalSignsView.PatientName,
                InDay = vitalSignsView.InDay,
                //DaysAfterOperation = vitalSignsView.DaysAfterOperation,
                BedNo = vitalSignsView.BedNumber,
                Times = vitalSignsView.NumberOfAdmissions.ToString(),
                TestTime = vitalSignsView.AssessDate.Add(vitalSignsView.AssessTime),
                Temperature = vitalSignsView.Temperature,
                TemperatureUnit = vitalSignsView.TemperatureUnit,
                //TemperatureMethod = vitalSignsView.TemperatureMethod,
                //BodyTempSecond = vitalSignsView.BodyTempSecond,
                //BodyTempNonRise = vitalSignsView.BodyTempNonRise,
                //BodyTempCooling = vitalSignsView.BodyTempCooling,
                Pulse = vitalSignsView.Pulse,
                PulseUnit = vitalSignsView.PulseUnit,
                HeartRate = vitalSignsView.HeartRate,
                HeartRateUnit = vitalSignsView.HeartRateUnit,
                HeartRateType = vitalSignsView.HeartRateType,
                PacemakerHeartRate = vitalSignsView.PacemakerHeartRate,
                PainScore = vitalSignsView.PainScore,
                //PostInterventionPain = vitalSignsView.PostInterventionPain,
                Breath = vitalSignsView.Breath,
                BreathUnit = vitalSignsView.BreathUnit,
                //BreathingType = vitalSignsView.BreathingType,
                //IsVentilatorUse = vitalSignsView.IsVentilatorUse,
                //VentilatorMonitorItem = vitalSignsView.VentilatorMonitorItem,
                Dbp = vitalSignsView.Dbp,
                Sbp = vitalSignsView.Sbp,
                PresessUnit = vitalSignsView.PresessUnit,
                //BpMeasurementSite = vitalSignsView.BpMeasurementSite,
                //SugarBlood = vitalSignsView.SugarBlood,
                //SugarBloodUnit = vitalSignsView.SugarBloodUnit,
                Height = vitalSignsView.Height,
                HeightUnit = vitalSignsView.HeightUnit,
                HeightType = vitalSignsView.HeightType,
                Weight = vitalSignsView.Weight,
                WeightUnit = vitalSignsView.WeightUnit,
                Bmi = vitalSignsView.Bmi,
                //WeightType = vitalSignsView.WeightType,
                //Defecate = vitalSignsView.Defecate,
                //StoolsPattern = vitalSignsView.StoolsPattern,
                //LaxativeStoolsNumber = vitalSignsView.LaxativeStoolsNumber,
                //LaxativeNumber = vitalSignsView.LaxativeNumber,
                //Urinate = vitalSignsView.Urinate,
                //UrinationPattern = vitalSignsView.UrinationPattern,
                //Urine = vitalSignsView.Urine,
                //UrineUnit = vitalSignsView.UrineUnit,
                BloodOxygen = vitalSignsView.BloodOxygen,
                BloodOxygenUnit = vitalSignsView.BloodOxygenUnit,
                AllergyHistory = vitalSignsView.AllergyHistory,
                //IsAllergyHistory = vitalSignsView.IsAllergyHistory,
                //SkinTest = vitalSignsView.SkinTest,
                //AbdominalCircumference = vitalSignsView.AbdominalCircumference,
                //UmbilicalCord = vitalSignsView.UmbilicalCord,
                //Jaundice = vitalSignsView.Jaundice,
                //IsBcgVaccine = vitalSignsView.IsBcgVaccine,
                //LactationMethod = vitalSignsView.LactationMethod,
                //OccurrenceEvent = vitalSignsView.OccurrenceEvent,
                //OccurrenceEventTime = vitalSignsView.OccurrenceEventTime,
                //IsSurgicalSafetyChecklist = vitalSignsView.IsSurgicalSafetyChecklist,
                //IsSurgicalRiskAssessment = vitalSignsView.IsSurgicalRiskAssessment,
                //IsShowTemp = vitalSignsView.IsShowTemp,
                //IsShowRecord = vitalSignsView.IsShowRecord,
                IsChild = vitalSignsView.IsChild ? "1" : "0",
                //RecordNurseNo = vitalSignsView.RecordNurseNo,
                //Remark = vitalSignsView.Remark,
                TestOperCode = vitalSignsView.ModifyEmployeeID,
                TestOperName = vitalSignsView.ModifyEmployeeName,
                TestDpetCode = vitalSignsView.DepartmentCode,
                TestDpetName = vitalSignsView.DepartmentName,
                ValidFlag = vitalSignsView.DeleteFlag != "*" ? "1" : "0",
                FaceColor = vitalSignsView.FaceColor
            };
        }

        /// <summary>
        /// 创建发送的请求信息
        /// </summary>
        /// <param name="bodySurfaceView"></param>
        /// <returns></returns>
        private async Task<string> CreateRequestMessageAsync(SyncPatientBodySurfaceView bodySurfaceView)
        {
            var xmlMessage = await _interconnectAppConfigSettingRepository.
                GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, XML_DOCUMENT_SETTING_CODE);
            if (string.IsNullOrEmpty(xmlMessage))
            {
                _logger.Error($"回传患者生命体征数据的时候，发现xml示例为空，SettingCode=【{XML_DOCUMENT_SETTING_CODE}】");
                return null;
            }
            var (xmlDoc, xmlNamespaceManager) = XmlUtiles.LoadXml(xmlMessage, namespaceDict);
            xmlDoc = InitXmlHeader(xmlDoc, xmlNamespaceManager);
            var fieldValues = GetFieldValueDict(bodySurfaceView);
            // 查找业务节点 并替换值
            ReplaceBusinessNodes(xmlDoc, fieldValues, xmlNamespaceManager);
            var message = WebServiceClient.SerializeXmlDocument(xmlDoc);
            return message;
        }
        /// <summary>
        /// 初始化头信息
        /// </summary>
        /// <param name="xmlDoc">xml文档对象</param>
        /// <param name="xmlNamespaceManager">xml命名空间管理对象</param>
        /// <returns></returns>
        private XmlDocument InitXmlHeader(XmlDocument xmlDoc, XmlNamespaceManager xmlNamespaceManager)
        {
            string xpath = XmlUtiles.AddNamespacePrefixToXPath("REPC_IN004014UV/creationTime/@value", NAMESPACE_PREFIX);
            bool success = XmlUtiles.ReplaceSingleNodeValue(xmlDoc, xpath, DateTime.Now.ToString(SYNC_DATETIME_FORMAT), xmlNamespaceManager);
            if (!success)
            {
                _logger.Error("查找xml中creationTime节点失败，无法保存消息创建时间");
            }
            xpath = XmlUtiles.AddNamespacePrefixToXPath("REPC_IN004014UV/sender/device/id/item/@extension", NAMESPACE_PREFIX);
            success = XmlUtiles.ReplaceSingleNodeValue(xmlDoc, xpath, SENDER, xmlNamespaceManager);
            if (!success)
            {
                _logger.Error("查找xml中发送人信息节点失败，无法设置消息发送人");
            }
            xpath = XmlUtiles.AddNamespacePrefixToXPath("REPC_IN004014UV/receiver/device/id/item/@extension", NAMESPACE_PREFIX);
            success = XmlUtiles.ReplaceSingleNodeValue(xmlDoc, xpath, RECEIVER, xmlNamespaceManager);
            if (!success)
            {
                _logger.Error("查找xml中接受人信息节点失败，无法设置消息接受人");
            }
            return xmlDoc;
        }

        /// <summary>
        /// 发送请求，回传数据给his
        /// </summary>
        /// <param name="message">发送的消息</param>
        /// <param name="syncBackView">core调用的参数</param>
        /// <param name="inpatientID">病人ID</param>
        /// <param name="caseNumber">住院流水号</param>
        /// <returns></returns>
        private async Task<bool> SendRequest(string message, SyncPatientBodySurfaceView syncBackView, string inpatientID, string caseNumber)
        {
            var url = await _interconnectAppConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, REQUEST_SETTING_CODE);
            if (url == null)
            {
                _logger.Error($"回传his数据失败，请求地址没有配置，SettingCode ={REQUEST_SETTING_CODE}");
                return false;
            }
            //新增调用日志
            var syncLog = await _synchronizeLogService.CreateSynchronizeLogInfo(url, ListToJson.ToJson(syncBackView), inpatientID, caseNumber, true);
            _unitOfWorkOut.SaveChanges();
            var result = await WebServiceClient.SendRequestMessageAsync(message, url, "createBodySurfaceMsg");
            if (result == null)
            {
                return false;
            }
            try
            {
                var responseXmlView = WebServiceClient.DeserializationXml<ResponseXmlView>(result);
                if (responseXmlView.Body.Result?.ReturnCode == "AA"
                    || responseXmlView.Body.Result?.ReturnMessage == "SUCCESS" || result.Contains("SUCCESS"))
                {
                    syncLog.SuccessFlag = "*";
                    syncLog.ModifyDateTime = DateTime.Now;
                    syncLog.ModifyEmployeeID = "Interconnect";
                    syncLog.ResponseResult = result;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"反序列化返回结果失败,返回结果{result}，异常为：{ex}");
                return false;
            }
            return _unitOfWorkOut.SaveChanges() > 0;
        }

        /// <summary>
        /// 根据字段名和值批量替换对应的XML节点
        /// </summary>
        /// <param name="xmlDoc">XML文档对象</param>
        /// <param name="fieldValues">字段名 => 替换值</param>
        /// <param name="nsmgr">xml命名空间管理对象</param>
        /// <returns>替换失败结果字典：字段名 => 值</returns>
        public Dictionary<string, string> ReplaceBusinessNodes(XmlDocument xmlDoc, Dictionary<string, string> fieldValues, XmlNamespaceManager nsmgr)
        {
            var results = new Dictionary<string, string>();
            var fieldToXmlNode = GetFieldToXmlDict();
            foreach (var kvp in fieldValues)
            {
                string field = kvp.Key;
                string newValue = kvp.Value;
                if (fieldToXmlNode.TryGetValue(field, out string rawXPath))
                {
                    // 给节点路径中补充命名空间
                    string xpath = XmlUtiles.AddNamespacePrefixToXPath(rawXPath, NAMESPACE_PREFIX);
                    if (XmlUtiles.ReplaceSingleNodeValue(xmlDoc, xpath, newValue, nsmgr))
                    {
                        continue;
                    }
                }
                results[field] = newValue;
            }
            if (results.Count > 0)
            {
                _logger.Error($"XML 文件字段值替换失败变量：{ListToJson.ToJson(results)}");
            }
            return results;
        }
        #region 字段节点对照表
        /// <summary>
        /// 字段和xml节点对应字典
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, string> GetFieldToXmlDict()
        {
            return new Dictionary<string, string>
            {
                { "hospitalCode", "REPC_IN004014UV/controlActProcess/authorOrPerformer/assignedDevice/representedOrganization/id/item/@extension" },
                { "hospitalName", "REPC_IN004014UV/controlActProcess/authorOrPerformer/assignedDevice/representedOrganization/name/item/part/@value" },
                { "hpAreaCode", "REPC_IN004014UV/controlActProcess/authorOrPerformer/assignedDevice/assignedDevice/asLocatedEntity/id/item/@extension" },
                { "hpAreaName", "REPC_IN004014UV/controlActProcess/authorOrPerformer/assignedDevice/assignedDevice/asLocatedEntity/location/name/item/part/@value" },
                { "cardNo", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/subject/patient1/id/item[@root='2.16.156.10011.2.5.1.4']/@extension" },
                { "visitNo", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/recordTarget/patient/id/item[@root='2.16.156.10011.0.9.1.55']/@extension" },
                { "visitSqNo", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/id/item[@root='2.16.156.10011.2.5.1.9']/@extension" },
                { "visitTypeCode", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/code/@code" },
                { "visitTypeName", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/code/displayName/@value" },
                { "recordNo", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/id/item/@extension" },
                { "patientName", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/recordTarget/patient/patientPerson/name/item/part/@value" },
                { "inDay", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/outboundRelationship2/observation[code[@code='CDE01.93.020.04']]/value/@value" },
                { "daysAfterOperation", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/outboundRelationship2/observation[code[@code='CDE01.93.020.05']]/value/@value" },
                { "bedNo", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/location/healthCareFacility/id/@extension" },
                { "times", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/lengthOfStayQuantity/@value" },
                { "testTime", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/author/time/@value" },
                { "temperature", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.186.00']]/value/@value" },
                { "temperatureUnit", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.186.00']]/value/@unit" },
                { "temperatureMethod", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/methodCode/item/originalText/@value" },
                { "bodyTempSecond", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.06']]/value/@value" },
                { "bodyTempNonRise", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.07']]/value/@value" },
                { "bodyTempCooling", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.08']]/value/@value" },
                { "pluse", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.118.00']]/value/@value" },
                { "pluseunit", "REPC_IN004014UV/controlAct4014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.118.00']]/value/@unit" },
                { "heartRate", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.206.00']]/value/@value" },
                { "heartRateUnit", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.206.00']]/value/@unit" },
                { "heartRateType", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.09']]/value/@value" },
                { "pacemakerHeartRate", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.10']]/value/@value" },
                { "painScore", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.11']]/value/@value" },
                { "postInterventionPain", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.12']]/value/@value" },
                { "breath", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.082.00']]/value/@value" },
                { "breathUnit", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.082.00']]/value/@unit" },
                { "breathingType", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.13']]/value/@value" },
                { "isVentilatorUse", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.14']]/value/@value" },
                { "ventilatorMonitorItem", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation/outboundRelationship2/observation/value" },
                { "dbp", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.176.00']]/value/@value" },
                { "sbp", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.174.00']]/value/@value" },
                { "presessUnit", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.174.00']]/value/@unit" },
                { "bpMeasurementSite", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/targetSiteCode/item/displayName/@value" },
                { "sugarBlood", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.50.102.00']]/value/@value" },
                { "sugarBloodUnit", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.50.102.00']]/value/@unit" },
                { "height", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.167.00']]/value/@value" },
                { "heightUnit", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.167.00']]/value/@unit" },
                { "heightType", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.15']]/value/@value" },
                { "weight", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.188.00']]/value/@value" },
                { "weightUnit", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.10.188.00']]/value/@unit" },
                { "bmi", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE05.10.075.00']]/value/@value" },
                { "weightType", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.16']]/value/@value" },
                { "defecate", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.00']]/value" },
                { "stoolsPattern", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.17']]/value/@value" },
                { "laxativeStoolsNumber", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.19']]/value/@value" },
                { "laxativeNumber", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.20']]/value/@value" },
                { "urinate", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.01']]/value/@value" },
                { "urinationPattern", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='CDE01.93.020.18']]/value/@value" },
                { "urine", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.02']]/value/@value" },
                { "urineUnit", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.02']]/value/@unit" },
                { "bloodOxygen", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.50.149.00']]/value/@value" },
                { "bloodOxygenUnit", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE04.50.149.00']]/value/@unit" },
                { "allergyHistory", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/outboundRelationship2/observation[code[@code='DE02.10.022.00']]/value/@value" },
                { "isAllergyHistory", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='DE02.10.023.00']]/value/@value" },
                { "skinTest", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.22']]/value/@value" },
                { "abdominalCircumference", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.23']]/value/@value" },
                { "umbilicalCord", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.24']]/value/@value" },
                { "jaundice", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.25']]/value/@value" },
                { "isBcgVaccine", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.26']]/value/@value" },
                { "lactationMethod", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation/value/displayName/@value" },
                { "occurrenceEvent", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/outboundRelationship2/act/code/displayName/@value" },
                { "occurrenceEventTime", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/pertinentInformation2/encounter/outboundRelationship2/act/effectiveTime/any/@value" },
                { "isSurgicalSafetyChecklist", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.30']]/value/@value" },
                { "isSurgicalRiskAssessment", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.31']]/value/@value" },
                { "isShowTemp", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.32']]/value/@value" },
                { "isShowRecord", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.020.33']]/value/@value" },
                { "isChild", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.030.16']]/value/@value" },
                { "recordNurseNo", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/reference/careProvisionEvent/id/item/@extension" },
                { "remark", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/author/noteText/@value" },
                { "testOperCode", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/author/assignedParty/id/item/@extension" },
                { "testOperName", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/author/assignedParty/assignedPerson/name/item/part/@value" },
                { "testDpetCode", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/author/assignedParty/representedOrganization/id/item/@extension" },
                { "testDpetName", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/author/assignedParty/representedOrganization/name/item/part/@value" },
                { "validFlag", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/statusCode/@code" },
                { "faceColor", "REPC_IN004014UV/controlActProcess/subject/registrationEvent/subject2/careProvisionEvent/component3/statementCollectorActList/component/organizer/component/observation[code[@code='CDE01.93.030.27']]/value/@value" }
            };
        }

        /// <summary>
        /// 根据传递的view获取需要修改的字段值
        /// </summary>
        /// <param name="bodySurfaceView">生命体征参数</param>
        /// <returns></returns>
        private Dictionary<string, string> GetFieldValueDict(SyncPatientBodySurfaceView bodySurfaceView)
        {
            return new Dictionary<string, string>
            {
                { "hospitalCode", bodySurfaceView.HospitalCode },
                { "hospitalName", bodySurfaceView.HospitalName },
                { "hpAreaCode", bodySurfaceView.HpAreaCode },
                { "hpAreaName", bodySurfaceView.HpAreaName },
                { "cardNo", bodySurfaceView.CardNo },
                { "visitNo", bodySurfaceView.VisitNo },
                { "visitSqNo", bodySurfaceView.VisitSqNo },
                { "visitTypeCode", bodySurfaceView.VisitTypeCode },
                { "visitTypeName", bodySurfaceView.VisitTypeName },
                { "recordNo", bodySurfaceView.RecordNo },
                { "patientName", bodySurfaceView.PatientName },
                { "inDay", bodySurfaceView.InDay },
                { "daysAfterOperation", bodySurfaceView.DaysAfterOperation },
                { "bedNo", bodySurfaceView.BedNo },
                { "times", bodySurfaceView.Times },
                { "testTime", bodySurfaceView.TestTime?.ToString("yyyyMMddHHmmss") },
                { "temperature", bodySurfaceView.Temperature },
                { "temperatureUnit", bodySurfaceView.TemperatureUnit },
                { "temperatureMethod", bodySurfaceView.TemperatureMethod },
                { "bodyTempSecond", bodySurfaceView.BodyTempSecond },
                { "bodyTempNonRise", bodySurfaceView.BodyTempNonRise },
                { "bodyTempCooling", bodySurfaceView.BodyTempCooling },
                { "pluse", bodySurfaceView.Pulse },
                { "pluseunit", bodySurfaceView.PulseUnit },
                { "heartRate", bodySurfaceView.HeartRate },
                { "heartRateUnit", bodySurfaceView.HeartRateUnit },
                { "heartRateType", bodySurfaceView.HeartRateType },
                { "pacemakerHeartRate", bodySurfaceView.PacemakerHeartRate },
                { "painScore", bodySurfaceView.PainScore },
                { "postInterventionPain", bodySurfaceView.PostInterventionPain },
                { "breath", bodySurfaceView.Breath },
                { "breathUnit", bodySurfaceView.BreathUnit },
                { "breathingType", bodySurfaceView.BreathingType },
                { "isVentilatorUse", bodySurfaceView.IsVentilatorUse },
                { "ventilatorMonitorItem", bodySurfaceView.VentilatorMonitorItem },
                { "dbp", bodySurfaceView.Dbp },
                { "sbp", bodySurfaceView.Sbp },
                { "presessUnit", bodySurfaceView.PresessUnit },
                { "bpMeasurementSite", bodySurfaceView.BpMeasurementSite },
                { "sugarBlood", bodySurfaceView.SugarBlood },
                { "sugarBloodUnit", bodySurfaceView.SugarBloodUnit },
                { "height", bodySurfaceView.Height },
                { "heightUnit", bodySurfaceView.HeightUnit },
                { "heightType", bodySurfaceView.HeightType },
                { "weight", bodySurfaceView.Weight },
                { "weightUnit", bodySurfaceView.WeightUnit },
                { "bmi", bodySurfaceView.Bmi },
                { "weightType", bodySurfaceView.WeightType },
                { "defecate", bodySurfaceView.Defecate },
                { "stoolsPattern", bodySurfaceView.StoolsPattern },
                { "laxativeStoolsNumber", bodySurfaceView.LaxativeStoolsNumber },
                { "laxativeNumber", bodySurfaceView.LaxativeNumber },
                { "urinate", bodySurfaceView.Urinate },
                { "urinationPattern", bodySurfaceView.UrinationPattern },
                { "urine", bodySurfaceView.Urine },
                { "urineUnit", bodySurfaceView.UrineUnit },
                { "bloodOxygen", bodySurfaceView.BloodOxygen },
                { "bloodOxygenUnit", bodySurfaceView.BloodOxygenUnit },
                { "allergyHistory", bodySurfaceView.AllergyHistory },
                { "isAllergyHistory", bodySurfaceView.IsAllergyHistory },
                { "skinTest", bodySurfaceView.SkinTest },
                { "abdominalCircumference", bodySurfaceView.AbdominalCircumference },
                { "umbilicalCord", bodySurfaceView.UmbilicalCord },
                { "jaundice", bodySurfaceView.Jaundice },
                { "isBcgVaccine", bodySurfaceView.IsBcgVaccine },
                { "lactationMethod", bodySurfaceView.LactationMethod },
                { "occurrenceEvent", bodySurfaceView.OccurrenceEvent },
                { "occurrenceEventTime", bodySurfaceView.OccurrenceEventTime?.ToString("yyyyMMddHHmmss") },
                { "isSurgicalSafetyChecklist", bodySurfaceView.IsSurgicalSafetyChecklist },
                { "isSurgicalRiskAssessment", bodySurfaceView.IsSurgicalRiskAssessment },
                { "isShowTemp", bodySurfaceView.IsShowTemp },
                { "isShowRecord", bodySurfaceView.IsShowRecord },
                { "isChild", bodySurfaceView.IsChild },
                { "recordNurseNo", bodySurfaceView.RecordNurseNo },
                { "remark", bodySurfaceView.Remark },
                { "testOperCode", bodySurfaceView.TestOperCode },
                { "testOperName", bodySurfaceView.TestOperName },
                { "testDpetCode", bodySurfaceView.TestDpetCode },
                { "testDpetName", bodySurfaceView.TestDpetName },
                { "validFlag", bodySurfaceView.ValidFlag },
                { "faceColor", bodySurfaceView.FaceColor }
            };
        }

        #endregion
    }
}
