﻿using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service.Interface
{
    public interface IInpatientService
    {
        /// <summary>
        /// 同步病人信息
        /// </summary>
        /// <param name="stationCode"></param>
        /// <param name="chartNo"></param>
        /// <param name="caseNumber"></param>
        /// <param name="patientState"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<bool> SyncInPatientByStationCode(string stationCode, string patientState, DateTime? startDate = null, DateTime? endDate = null);

        /// <summary>
        /// 同步单病人信息
        /// </summary>
        /// <param name="stationCode"></param>
        /// <param name="chartNo"></param>
        /// <param name="caseNumber"></param>
        /// <param name="patientState"></param>
        /// <returns></returns>
        Task<bool> SyncInPatientByChartNo(string chartNo, string caseNumber, string patientState);


        /// <summary>
        /// 获取单个患者主诉信息
        /// </summary>
        /// <param name="visitNo">用户流水号</param>
        /// <param name="visitType">门诊住院标识</param>
        /// <returns></returns>
        Task<bool> GetSingleInpatientChiefComplaintAsync(string visitNo, string visitType);

        /// <summary>
        /// 同步患者过敏数据
        /// </summary>
        /// <param name="patientId"></param>
        /// <param name="visitedId"></param>
        /// <returns></returns>
        Task<bool> SyncPatientAllergic(string patientId);
        /// <summary>
        /// //根据住院号同步病人诊断信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        Task<bool> SyncPatientDiagnosis(string caseNumber);
        /// <summary>
        /// 根据CaseNumber同步出院病人信息
        /// </summary>
        /// <param name="localCaseNumber"></param>
        /// <param name="chartNo"></param>
        /// <param name="caseNumber"></param>
        /// <param name="patientState"></param>
        /// <returns></returns>
        Task<bool> SyncDischargeInPatientByCaseNumber(string localCaseNumber, string chartNo, string caseNumber, string patientState);

        /// <summary>
        /// 同步患者入出转信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="InpatientTransferView">转出科室</param>
        /// <param name="eventType">事件类型(转入,转出)</param>
        /// <param name="hospitalID"></param>
        /// <param name="isTransfer">是否转科(ture转科,false 撤销转科)</param>
        /// <returns></returns>
        Task<bool> SyncTransferDept(string caseNumber, InpatientTransferView inpatientTransferView, string eventType, string hospitalID, bool isTransfer);
        /// <summary>
        /// 同步住院病人信息(MQ调用)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        Task<bool> SyncInpatientData(string hospitalID);
        /// <summary>
        /// 同步住院病人信息(MQ调用)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        Task<bool> SyncPatientDiagnosisData(string hospitalID);
        /// <summary>
        /// 同步住院病人信息(MQ调用)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        Task<bool> SyncPatientAllergy(string hospitalID);

        /// <summary>
        /// 同步主诉信息(MQ调用)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<bool> SyncPatientMedicalHistory(string hospitalID);
        /// <summary>
        /// 根据病区分组同步在院患者
        /// </summary>
        /// <param name="stationGroupID"></param>
        /// <returns></returns>
        Task<bool> SyncInPatientByStationGroup(int stationGroupID);
        /// <summary>
        /// 同步新生儿信息
        /// </summary>
        /// <param name="parentCaseNumber">母亲住院流水号</param>
        /// <param name="userID">操作人</param>
        /// <returns></returns>
        Task<bool> GetNewBornData(string parentCaseNumber,string userID);
    }
}