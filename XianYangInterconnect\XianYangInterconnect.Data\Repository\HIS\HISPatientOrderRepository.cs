﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models.HIS;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Data.Repository
{
    public class HISPatientOrderRepository: IHISPatientOrderRepository
    {
        private HISDbContext _dbContext = null;

        public HISPatientOrderRepository(HISDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<HISPatientOrderView>> GetByCasenumber(string caseNumber,int minute)
        {
            minute = -minute;
            var timeStamp = DateTime.Now.AddMinutes(minute);
            return await _dbContext.HISPatientOrderViews.Where(m => m.InpatientNo ==caseNumber && m.TimeStamp>= timeStamp).ToListAsync();

        }

        public async Task<List<HISPatientOrderView>> GetByCasenumberAndComboNo(string caseNumber, string comboNo)
        {    
            return await _dbContext.HISPatientOrderViews.Where(m => m.InpatientNo == caseNumber && m.ComboNo== comboNo).ToListAsync();

        }
        public async Task<List<HISPatientOrderNoView>> GetByConfirmDateTime(int minute)
        {
            var datetime = DateTime.Now.AddMinutes(minute * -1);
            var endDatetime = DateTime.Now.AddDays(1);
            endDatetime = new DateTime(endDatetime.Year, endDatetime.Month, endDatetime.Day, 23, 59, 59);

            return await _dbContext.HISPatientOrderViews.Where(m => m.ConfirmDate >= datetime && m.ConfirmDate <= endDatetime).Select(
                m => new HISPatientOrderNoView
                {
                    Casenumber = m.InpatientNo,
                    ComboNo= m.ComboNo
                }
             ).Distinct().ToListAsync();
        }
    }
}
