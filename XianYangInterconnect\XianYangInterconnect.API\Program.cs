using XianYangInterconnect.API;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using XianYangInterconnect.API.Extensions;
var builder = WebApplication.CreateBuilder(args);
var startup = new Startup(builder.Configuration);
ThreadPool.SetMinThreads(200, 200); //设置全局线程池
//注册依赖关系
startup.ConfigureServices(builder.Services);
#region Autofac注册、依赖注入
builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory());
builder.Host.ConfigureContainer<ContainerBuilder>((context, containerBuilder) =>
{
    containerBuilder.RegisterModule(new AutofacModuleRegister());
});
#endregion
var app = builder.Build();
//设置中间件
startup.SetupMiddleware(app, builder.Environment);
