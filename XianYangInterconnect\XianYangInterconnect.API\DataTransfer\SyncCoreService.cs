﻿using System.ComponentModel.Design;
using System.Diagnostics;
using Hangfire;
using NLog;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Service.Interface.SyncDataLog;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.API.DataTransfer
{
    /// <summary>
    /// 同步核心服务
    /// </summary>
    public class SyncCoreService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IJobLogService _jobLogService;
        private readonly IPatientScoreService _patientScoreService;
        private readonly ISyncLogService _syncLogService;
        private readonly IInpatientService _inpatientService;
        private readonly IPatientOrderService _patientOrderService;
        private readonly IPatientMedicineScheduleService _patientMedicineScheduleService;
        private readonly IPatientOperationService _patientOperationService;
        private readonly IPatientMachineDataService _patientMachineDataService;
        private readonly IPatientLabservice _IPatientLabservice;
        private readonly ISyncBloodInspectService _syncBloodInspectService;
        /// <summary>
        /// 同步启动延迟间隔多少秒
        /// </summary>
        private const int SyncDelaySecond =60;
        /// <summary>
        /// 上一次同步时间 
        /// </summary>
        private static DateTime OrderLastDateTime= new DateTime(2025,05,16);

        /// <summary>
        /// 上一次同步时间 
        /// </summary>
        private static DateTime MedicineLastDateTime = new DateTime(2025, 05, 16);

        /// <summary>
        /// 上一次同步时间 
        /// </summary>
        private static DateTime LabLastDateTime = new DateTime(2025, 05, 16);

        /// <summary>
        /// 仪器上一次同步时间 
        /// </summary>
        private static DateTime MachineLastDateTime = new DateTime(2025, 05, 16);



        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="jobLogService"></param>
        /// <param name="patientScoreService"></param>
        /// <param name="syncLogService"></param>
        /// <param name="inpatientService"></param>
        /// <param name="patientOrderService"></param>
        /// <param name="patientMedicineScheduleService"></param>
        /// <param name="patientOperationService"></param>
        /// <param name="patientMachineDataService"></param>
        /// <param name="patientLabservice"></param>
        /// <param name="syncBloodInspectService"></param>
        public SyncCoreService(IJobLogService jobLogService
            , IPatientScoreService patientScoreService
            , ISyncLogService syncLogService
            , IInpatientService inpatientService
            , IPatientOrderService patientOrderService
            , IPatientMedicineScheduleService patientMedicineScheduleService
            , IPatientOperationService patientOperationService
            , IPatientMachineDataService patientMachineDataService
            , IPatientLabservice patientLabservice
            , ISyncBloodInspectService syncBloodInspectService

            )
        {
            _jobLogService = jobLogService;
            _patientScoreService = patientScoreService;
            _syncLogService = syncLogService;
            _inpatientService = inpatientService;
            _patientOrderService = patientOrderService;
            _patientMedicineScheduleService = patientMedicineScheduleService;
            _patientOperationService = patientOperationService;
            _patientMachineDataService = patientMachineDataService;
            _IPatientLabservice = patientLabservice;
            _syncBloodInspectService = syncBloodInspectService;
        }

        /// <summary>
        /// 同步患者床位数据
        /// </summary>
        /// <param name="hospitalID"></param>     
        /// <returns></returns>
        public bool SyncBedList(string hospitalID)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            var guid = Guid.NewGuid().ToString("N");
            var successFlag = false;
            var jobId = ((int)JobType.BedJob).ToString();
            var jobName = "床位数据";
            var logMsg = "床位数据,作业编号:" + guid + "启动同步任务:";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            try
            {
                successFlag = true;
                //var result = _syncListService.SyncBedList(hospitalID);
                // if (result.Code == 0)
                // {
                //     successFlag = false;
                // }
                // if (!successFlag)
                // {
                //     _syncLogService.InsertSyncLog(1, guid, "SyncPatientOperation", "", "同步床位数据失败", "Sys", true);
                // }
            }
            catch (Exception ex)
            {
                _logger.Error("同步患者检验信息失败" + ex.ToString());
                successFlag = false;
                //  _syncLogService.InsertSyncLog(1, guid, "SyncPatientOperation", "", "同步床位数据失败", "Sys", true);
            }

            stopwatch.Stop();
            _jobLogService.RemoveJob(jobId, jobName, "");
            _logger.Info("同步床位数据用时{0}s", stopwatch.Elapsed.TotalSeconds);
            return successFlag;
        }
        ///// <summary>
        ///// 启动定时作业
        ///// </summary>
        ///// <param name="syncDataLogView"></param>
        ///// <returns></returns>
        //public async Task SyncStartUp(SyncDataLogView syncDataLogView)
        //{
        //    switch (syncDataLogView.SyncDataType)
        //    {
        //        case "test":
        //            BackgroundJob.Enqueue(() => SyncBedList(syncDataLogView.HospitalID));
        //            break;
        //        case "VTE":
        //            await SyncPatientVTE(syncDataLogView.HospitalID);
        //            break;
        //        case "Inpatient":
        //            await SyncInpatientData(syncDataLogView.HospitalID);
        //            break;
        //        case "PatientAllergy":
        //            await SyncPatientAllergy(syncDataLogView.HospitalID);
        //            break;
        //        case "PatientDiagnosis":
        //            await SyncPatientDiagnosis(syncDataLogView.HospitalID);
        //            break;
        //        case "PatientLab":
        //            await SyncPatientLab(syncDataLogView.HospitalID);
        //            break;
        //        case "PatientMedicalHistory":
        //            await SyncPatientMedicalHistory(syncDataLogView.HospitalID);
        //            break;
        //        case "PatientOrader":
        //            await SyncPatientOrader(syncDataLogView.HospitalID);
        //            break;
        //        case "PatientOrderExecute":
        //            await SyncPatientMedicine(syncDataLogView.HospitalID);
        //            break;
        //        case "PatientOperation":
        //        case "PatientOperationApply":
        //            await _patientOperationService.SyncPatientOperation();
        //            break;
        //            case "PatientMachineData":
        //                await SyncPatientMachine(syncDataLogView.HospitalID);
        //            break;
        //        default:
        //            break;
        //    }
        //}

        /// <summary>
        /// 同步患者VTE数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientVTE(string hospitalID)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();

            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.PatientScore).ToString();
            var jobName = "同步患者VTE数据";
            var logMsg = $"作业编号:{guid}启动同步任务:{jobName}";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            var successFlag = true;
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            try
            {
                successFlag = await _patientScoreService.SyncPatientVTE(hospitalID);
            }
            catch (Exception ex)
            {
                successFlag = false;
                _logger.Error("同步患者VTE数据失败" + ex.ToString());
            }
            finally
            {
                _jobLogService.RemoveJob(jobId, jobName, "");
            }

            if (!successFlag)
            {            
                _syncLogService.InsertSyncLog(1, guid, "SyncPatientVTE", "SyncPatientVTE", "同步患者VTE数据,失败", "TongBu", true);
            }

            stopwatch.Stop();
            _logger.Info("同步患者VTE数据{0}s", stopwatch.Elapsed.TotalSeconds);
            return successFlag;
        }

        /// <summary>
        /// 同步住院患者数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncInpatientData(string hospitalID)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.InpatientData).ToString();
            var jobName = "同步患者数据";
            var logMsg = $"作业编号:{guid}启动同步任务:{jobName}";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            var successFlag = true;
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            try
            {
                successFlag = await _inpatientService.SyncInpatientData(hospitalID);
            }
            catch (Exception ex)
            {
                successFlag = false;
                _logger.Error("同步住院患者数据" + ex.ToString());
            }
            finally
            {
                _jobLogService.RemoveJob(jobId, jobName, "");
            }
            if (!successFlag)
            {             
                _syncLogService.InsertSyncLog(1, guid, "SyncInpatientData", "SyncInpatientData", "同步患者数据,失败", "TongBu", true);
            }

            stopwatch.Stop();
            _logger.Info("同步患者数据{0}s", stopwatch.Elapsed.TotalSeconds);
            return successFlag;
        }
        /// <summary>
        /// 同步住院患者数据按病区分组
        /// </summary>
        /// <param name="stationGroupID"></param>
        /// <returns></returns>
        public async Task<bool> SyncInpatientDataByStationGroup(int stationGroupID)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            var guid = Guid.NewGuid().ToString("N");
            var jobId = $"{(int)JobType.InpatientData}_{stationGroupID}";
            var jobName = "按病区分组同步患者数据";
            var logMsg = $"作业编号:{guid}启动同步任务:{jobName}";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            var successFlag = true;
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            try
            {
                successFlag = await _inpatientService.SyncInPatientByStationGroup(stationGroupID);
            }
            catch (Exception ex)
            {
                successFlag = false;
                _logger.Error("按病区分组同步住院患者数据" + ex.ToString());
            }
            finally
            {
                _jobLogService.RemoveJob(jobId, jobName, "");
            }
            if (!successFlag)
            {               
                _syncLogService.InsertSyncLog(1, guid, "SyncInpatientData", "SyncInpatientData", "按病区分组同步患者数据,失败", "TongBu", true);
            }

            stopwatch.Stop();
            _logger.Info("按病区分组同步患者数据{0}s", stopwatch.Elapsed.TotalSeconds);
            return successFlag;
        }
        /// <summary>
        /// 同步患者过敏药物
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientAllergy(string hospitalID)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.InpatientData).ToString();
            var jobName = "同步患者过敏药物";
            var logMsg = $"作业编号:{guid}启动同步任务:{jobName}";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            var successFlag = true;
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            try
            {
                successFlag = await _inpatientService.SyncPatientAllergy(hospitalID);
            }
            catch (Exception ex)
            {
                successFlag = false;
                _logger.Error("同步患者过敏药物" + ex.ToString());
            }
            finally
            {
                _jobLogService.RemoveJob(jobId, jobName, "");
            }
            if (!successFlag)
            {                
                _syncLogService.InsertSyncLog(1, guid, "SyncInpatientData", "SyncInpatientData", "同步患者过敏药物,失败", "TongBu", true);
            }

            stopwatch.Stop();
            _logger.Info("同步患者过敏药物{0}s", stopwatch.Elapsed.TotalSeconds);
            return successFlag;
        }
        /// <summary>
        /// 同步患者诊断
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientDiagnosis(string hospitalID)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.InpatientData).ToString();
            var jobName = "同步患者诊断";
            var logMsg = $"作业编号:{guid}启动同步任务:{jobName}";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            var successFlag = true;
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            try
            {
                successFlag = await _inpatientService.SyncPatientDiagnosisData(hospitalID);
            }
            catch (Exception ex)
            {
                successFlag = false;
                _logger.Error("同步患者诊断" + ex.ToString());
            }
            finally
            {
                _jobLogService.RemoveJob(jobId, jobName, "");
            }
            if (!successFlag)
            {          
                _syncLogService.InsertSyncLog(1, guid, "SyncInpatientData", "SyncInpatientData", "同步患者诊断,失败", "TongBu", true);
            }

            stopwatch.Stop();
            _logger.Info("同步患者数据{0}s", stopwatch.Elapsed.TotalSeconds);
            return successFlag;
        }
        /// <summary>
        /// 同步患者检验
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientLab(string hospitalID)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.InpatientData).ToString();
            var jobName = "同步患者检验";
            var logMsg = $"作业编号:{guid}启动同步任务:{jobName}";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            var successFlag = true;
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            try
            {
                successFlag = await _IPatientLabservice.SyncPatientLab(hospitalID);
            }
            catch (Exception ex)
            {
                successFlag = false;
                _logger.Error("同步患者检验" + ex.ToString());
            }
            finally
            {
                _jobLogService.RemoveJob(jobId, jobName, "");
            }
            if (!successFlag)
            { 
                _syncLogService.InsertSyncLog(1, guid, "SyncInpatientData", "SyncInpatientData", "同步患者检验,失败", "TongBu", true);
            }

            stopwatch.Stop();
            _logger.Info("同步患者数据{0}s", stopwatch.Elapsed.TotalSeconds);
            return successFlag;
        }
        /// <summary>
        /// 同步住院患者医嘱数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientOrder(string hospitalID)
        {
            TimeSpan timeDifference = DateTime.Now - OrderLastDateTime;
            if (timeDifference.TotalSeconds< SyncDelaySecond)
            {
                return false;
            }
            OrderLastDateTime= DateTime.Now;
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.PatientOrder).ToString();
            var jobName = "同步患者医嘱数据";
            var logMsg = $"作业编号:{guid}启动同步任务:{jobName}";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            var successFlag = true;
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            try
            {
                successFlag = await _patientOrderService.AutoSyncPatientOrderSync(hospitalID);
            }
            catch (Exception ex)
            {
                successFlag = false;
                _logger.Error("同步患者数据医嘱失败" + ex.ToString());
            }
            finally
            {
                _jobLogService.RemoveJob(jobId, jobName, "");
            }
            if (!successFlag)
            {                
                _syncLogService.InsertSyncLog(1, guid, "SyncInpatientData", "SyncInpatientData", "同步患者数据,失败", "TongBu", true);
            }

            stopwatch.Stop();
            _logger.Info($"同步患者医嘱数据时间{stopwatch.Elapsed.TotalSeconds}s");
            return successFlag;
        }
        /// <summary>
        /// 同步主诉作业
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientMedicalHistory(string hospitalID)
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.InpatientData).ToString();
            var jobName = "同步患者数据";
            var logMsg = $"作业编号:{guid}启动同步任务:{jobName}";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            var successFlag = true;
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            try
            {
                successFlag = await _inpatientService.SyncPatientMedicalHistory(hospitalID);
            }
            catch (Exception ex)
            {
                successFlag = false;
                _logger.Error("同步患者数据失败" + ex.ToString());
            }
            finally
            {
                _jobLogService.RemoveJob(jobId, jobName, "");
            }
            if (!successFlag)
            {                
                _syncLogService.InsertSyncLog(1, guid, "SyncInpatientData", "SyncInpatientData", "同步患者数据,失败", "TongBu", true);
            }

            stopwatch.Stop();
            _logger.Info("同步患者数据{0}s", stopwatch.Elapsed.TotalSeconds);
            return successFlag;
        }

        /// <summary>
        /// 同步患者给药数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientMedicine(string hospitalID)
        {
            var successFlag = true;         
            try
            {
                successFlag = await _patientMedicineScheduleService.SyncPatientMedicine(hospitalID);
            }
            catch (Exception ex)
            {
                successFlag = false;
                _logger.Error("同步同步患者用药数据失败" + ex.ToString());
            }
            
            if (!successFlag)
            {
                _syncLogService.InsertSyncLog(1, "", "SyncPatientMedicine", "SyncPatientMedicine", "同步患者用药数据失败", "TongBu", true);
            }   
            return successFlag;
        }
        /// <summary>
        /// 同步住院患者仪器数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientMachine(string hospitalID)
        {
            TimeSpan timeDifference = DateTime.Now - MachineLastDateTime;
            if (timeDifference.TotalSeconds < SyncDelaySecond)
            {
                return false;
            }
            MachineLastDateTime = DateTime.Now;
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.PatientMachine).ToString();
            var jobName = "同步患者仪器数据";
            var logMsg = $"作业编号:{guid}启动同步任务:{jobName}";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            var successFlag = true;
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            try
            {
                successFlag = await _patientMachineDataService.AutoSyncPatientMachineData(hospitalID);

            }
            catch (Exception ex)
            {
                successFlag = false;
                _logger.Error("同步患者仪器数据失败" + ex.ToString());
            }
            finally
            {
                _jobLogService.RemoveJob(jobId, jobName, "");
            }
            if (!successFlag)
            {             
                _syncLogService.InsertSyncLog(1, guid, "SyncInpatientData", "SyncInpatientData", "同步患者仪器数据,失败", "TongBu", true);
            }

            stopwatch.Stop();
            _logger.Info($"同步患者仪器数据时间{stopwatch.Elapsed.TotalSeconds}s");
            return successFlag;
        }
        /// <summary>
        /// 同步输血监测数据
        /// </summary>
        /// <returns></returns>
        public async Task<object> SyncBloodInspectRecordDetail()
        {
            var stopwatch = new Stopwatch();
            stopwatch.Start();
            var guid = Guid.NewGuid().ToString("N");
            var jobId = ((int)JobType.PatientBloodInspect).ToString();
            var jobName = "同步输血监测数据";
            var logMsg = $"作业编号:{guid}启动同步任务:{jobName}";
            var jobStatus = _jobLogService.GetJobStatus(jobId, jobName, "");
            var successFlag = true;
            if (!jobStatus)
            {
                _logger.Info("作业正在启动" + logMsg);
                return false;
            }
            try
            {
                successFlag = await _syncBloodInspectService.SyncBloodInspectRecordDetail();
            }
            catch (Exception ex)
            {
                successFlag = false;
                _logger.Error("同步输血监测数据数据失败" + ex.ToString());
            }
            finally
            {
                _jobLogService.RemoveJob(jobId, jobName, "");
            }
            if (!successFlag)
            {
                _syncLogService.InsertSyncLog(1, guid, "SyncBloodInspectRecordDetail", "SyncBloodInspectRecordDetail", "同步输血监测数据,失败", "TongBu", true);
            }

            stopwatch.Stop();
            _logger.Info("同步输血监测数据{0}s", stopwatch.Elapsed.TotalSeconds);
            return successFlag;
            
        }
    }
}