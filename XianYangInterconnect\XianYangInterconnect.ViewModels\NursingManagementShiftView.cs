﻿namespace XianYangInterconnect.ViewModels
{
    /// <summary>
    /// 护理管理排班接口数据
    /// </summary>
    public class NursingManagementShiftView
    {
        /// <summary>
        /// 所属班别
        /// </summary>
        public string Shift { get; set; }
        /// <summary>
        /// 操作人工号
        /// </summary>
        public string OperationID { get; set; }
        /// <summary>
        /// 班别日期
        /// </summary>
        public long ShiftDate { get; set; }
        /// <summary>
        /// 工号
        /// </summary>
        public string EmployeeID { get; set; }
        /// <summary>
        /// 姓名
        /// </summary>
        public string EmployeeName { get; set; }
        /// <summary>
        /// 排班岗位ID
        /// </summary>
        public int PostID { get; set; }
        /// <summary>
        /// 排班岗位名称
        /// </summary>
        public string PostName { get; set; }
    }
}
