﻿using XianYangInterconnect.Service.Interface;

namespace XianYangInterconnect.Service.BloodInspect
{
    public class SyncBloodInspectService : ISyncBloodInspectService
    {
        /// <summary>
        /// 同步输血监测数据
        /// </summary>
        /// <param name="startDateTime"></param>
        /// <param name="endDateTime"></param>
        /// <returns></returns>
        public async Task<bool> SyncBloodInspectRecordDetail(DateTime? startDateTime, DateTime? endDateTime)
        {
            throw new NotImplementedException();
        }
    }
}
