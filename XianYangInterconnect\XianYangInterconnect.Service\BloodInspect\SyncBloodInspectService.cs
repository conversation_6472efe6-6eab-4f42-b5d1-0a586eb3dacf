﻿using NLog;
using XianYangInterconnect.Common;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Services.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service.BloodInspect
{
    public class SyncBloodInspectService : ISyncBloodInspectService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IRequestApiService _requestApiService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="requestApiService"></param>
        public SyncBloodInspectService(IRequestApiService requestApiService)
        {
            _requestApiService = requestApiService;
        }

        /// <summary>
        /// 同步输血监测数据
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns></returns>
        public async Task<bool> SyncBloodInspectRecordDetail(DateTime? startDateTime, DateTime? endDateTime)
        {
            try
            {
                _logger.Info($"开始同步输血监测数据，时间范围：{startDateTime} - {endDateTime}");

                // 获取HIS血液检查数据
                var hisBloodInspectData = await GetHisBloodInspectData(startDateTime, endDateTime);
                if (hisBloodInspectData == null || !hisBloodInspectData.Any())
                {
                    _logger.Info("未获取到HIS血液检查数据");
                    return true; // 没有数据也算成功
                }

                _logger.Info($"获取到 {hisBloodInspectData.Count} 条HIS血液检查数据");

                // 转换数据为保存明细的请求参数
                var inputDataList = ProcessHisDataToInputData(hisBloodInspectData);
                if (inputDataList == null || !inputDataList.Any())
                {
                    _logger.Warn("转换后的数据为空");
                    return false;
                }

                _logger.Info($"转换后得到 {inputDataList.Count} 条输入数据");

                // 保存数据到InterconnectCore
                var saveResult = await SaveBloodInspectData(inputDataList);
                if (saveResult)
                {
                    _logger.Info("输血监测数据同步成功");
                }
                else
                {
                    _logger.Error("输血监测数据保存失败");
                }

                return saveResult;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步输血监测数据时发生异常: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取HIS血液检查数据
        /// </summary>
        /// <param name="startDateTime">开始时间</param>
        /// <param name="endDateTime">结束时间</param>
        /// <returns></returns>
        private async Task<List<HisBloodInspectView>> GetHisBloodInspectData(DateTime? startDateTime, DateTime? endDateTime)
        {
            try
            {
                // 构建查询参数
                var queryParam = new
                {
                    StartDateTime = startDateTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    EndDateTime = endDateTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    PageIndex = 1,
                    PageSize = 1000 // 可以根据需要调整
                };

                var paramJson = ListToJson.ToJson(queryParam);
                _logger.Info($"请求HIS血液检查数据参数: {paramJson}");

                // 调用HIS接口获取数据，这里需要配置对应的SettingCode
                var result = await _requestApiService.RequestAPI("GetHisBloodInspectData", paramJson);
                if (result == null)
                {
                    _logger.Error("调用HIS血液检查数据接口返回null");
                    return null;
                }

                var resultString = result.ToString();
                _logger.Info($"HIS接口返回数据: {resultString}");

                // 解析响应数据
                var response = ListToJson.ToList<HisBloodInspectResponse>(resultString);
                if (response == null || !response.Success)
                {
                    _logger.Error($"HIS血液检查数据接口返回失败，响应码: {response?.Code}, 消息: {response?.Message}");
                    return null;
                }

                return response.Data?.Data ?? new List<HisBloodInspectView>();
            }
            catch (Exception ex)
            {
                _logger.Error($"获取HIS血液检查数据时发生异常: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 处理HIS数据转换为输入数据
        /// </summary>
        /// <param name="hisDataList">HIS血液检查数据列表</param>
        /// <returns>转换后的输入数据列表</returns>
        private List<InterconnectCoreBloodInspectInputView> ProcessHisDataToInputData(List<HisBloodInspectView> hisDataList)
        {
            try
            {
                if (hisDataList == null || !hisDataList.Any())
                {
                    return new List<InterconnectCoreBloodInspectInputView>();
                }

                // 使用转换方法批量转换数据
                var inputDataList = InterconnectCoreBloodInspectInputView.FromHisDataList(hisDataList);

                _logger.Info($"成功转换 {inputDataList.Count} 条数据");

                // 可以在这里添加额外的数据处理逻辑
                foreach (var inputData in inputDataList)
                {
                    // 验证必要字段
                    if (string.IsNullOrEmpty(inputData.CaseNumber))
                    {
                        _logger.Warn($"数据缺少病案号，SourceUniqueID: {inputData.SourceUniqueID}");
                        continue;
                    }

                    if (string.IsNullOrEmpty(inputData.PerformEmployeeID))
                    {
                        _logger.Warn($"数据缺少执行人ID，CaseNumber: {inputData.CaseNumber}");
                        continue;
                    }

                    // 可以添加更多的数据验证和处理逻辑
                }

                return inputDataList.Where(x => !string.IsNullOrEmpty(x.CaseNumber) && !string.IsNullOrEmpty(x.PerformEmployeeID)).ToList();
            }
            catch (Exception ex)
            {
                _logger.Error($"处理HIS数据转换时发生异常: {ex.Message}", ex);
                return new List<InterconnectCoreBloodInspectInputView>();
            }
        }

        /// <summary>
        /// 保存血液检查数据到InterconnectCore
        /// </summary>
        /// <param name="inputDataList">输入数据列表</param>
        /// <returns>保存结果</returns>
        private async Task<bool> SaveBloodInspectData(List<InterconnectCoreBloodInspectInputView> inputDataList)
        {
            try
            {
                if (inputDataList == null || !inputDataList.Any())
                {
                    _logger.Warn("没有数据需要保存");
                    return true;
                }

                var paramJson = ListToJson.ToJson(inputDataList);
                _logger.Info($"保存血液检查数据参数: {paramJson}");

                // 调用InterconnectCore接口保存数据，这里需要配置对应的SettingCode
                var result = await _requestApiService.RequestAPI("SaveBloodInspectData", paramJson);
                if (result == null)
                {
                    _logger.Error("调用保存血液检查数据接口返回null");
                    return false;
                }

                var resultString = result.ToString();
                _logger.Info($"保存接口返回数据: {resultString}");

                // 这里可以根据实际的返回格式来判断是否成功
                // 假设返回的是标准的响应格式
                return !string.IsNullOrEmpty(resultString) && !resultString.Contains("error", StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.Error($"保存血液检查数据时发生异常: {ex.Message}", ex);
                return false;
            }
        }
    }
}
