﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XianYangInterconnect.Models.Base;

namespace XianYangInterconnect.Models
{
    [Serializable]
    [Table("PatientBasicData")]
    public class PatientBasicDataInfo
    {
        /// <summary>
        /// 病人序号 
        /// </summary>       
        [Column(TypeName = "char(32)")]
        [Key]
        public string PatientID { get; set; }
        /// <summary>
        /// 医疗院所代码 
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 病历号 
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string ChartNo { get; set; }
        /// <summary>
        /// 在院号 
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string InpatientNo { get; set; }
        /// <summary>
        /// 身份证号
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string IdentityID { get; set; }
        /// <summary>
        /// 病人姓名 
        /// </summary>
        [Column(TypeName = "Nvarchar(100)")]
        public string PatientName { get; set; }
        /// <summary>
        /// 性别 S:共通 1:男 2:女 X:特殊表单专用 
        /// </summary>
        [Column(TypeName = "char(1)")]
        public string Gender { get; set; }
        /// <summary>
        /// 生日 
        /// </summary>
        public DateTime? DateOfBirth { get; set; }
        /// <summary>
        /// 出生时间 
        /// </summary>
        public TimeSpan? TimeOfBirth { get; set; }
        /// <summary>
        /// 血型 
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string BloodType { get; set; }
        /// <summary>
        /// 藉贯 
        /// </summary>
        [Column(TypeName = "varchar(150)")]
        public string NativePlace { get; set; }
        /// <summary>
        /// RH血型
        /// </summary>
        [Column(TypeName = "char(1)")]
        public string RH { get; set; }

        /// <summary>
        /// 患者ID， 标识患者全院唯一码，除Chartno外的唯一标识,有的医院可能没有
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string LocalChartNO { get; set; }
    }
}
