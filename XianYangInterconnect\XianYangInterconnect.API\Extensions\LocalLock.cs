﻿using System.Collections.Concurrent;
using System.Threading.Tasks;
using System;

namespace XianYangInterconnect.API.Extensions
{
    /// <summary>
    /// 本地锁
    /// </summary>
    public class LocalLock : IDistributedLock
    {
        private readonly ConcurrentDictionary<string, byte> lockCounts = new ConcurrentDictionary<string, byte>();
        /// <summary>
        /// 获取锁
        /// </summary>
        /// <param name="resourceKey"></param>
        /// <param name="lockDuration"></param>
        /// <returns></returns>
        public Task<bool> TryAcquireLockAsync(string resourceKey, TimeSpan? lockDuration = null)
        {
            byte lockCount = 0;
            if (lockCounts.TryAdd(resourceKey, lockCount))
            {
                lockCounts[resourceKey] = 1;
                return Task.FromResult(true);
            }
            return Task.FromResult(false);
        }
        /// <summary>
        /// 释放锁
        /// </summary>
        /// <param name="resourceKey"></param>
        /// <returns></returns>
        public Task ReleaseLockAsync(string resourceKey)
        {
            lockCounts.TryRemove(resourceKey, out _);
            return Task.CompletedTask;
        }
    }
}
