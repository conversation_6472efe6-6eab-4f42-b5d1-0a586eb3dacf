﻿using XianYangInterconnect.Common.SessionCommon;

namespace XianYangInterconnect.Common
{
    /// <summary>
    /// 缓存类型
    /// </summary>
    public enum CacheType
    {
        /// <summary>
        /// 远程调用API配置字典
        /// </summary>
        BedInfo = 1,
        /// <summary>
        /// HIS.Common使用的API配置
        /// </summary>
        APISettingInfo = 2,
        /// <summary>
        /// AppConfigSetting配置表
        /// </summary>
        AppConfigSetting = 3,
        /// <summary>
        /// StationToDepartment配置表
        /// </summary>
        StationToDepartment = 4,
        /// <summary>
        /// SettingDescription配置表
        /// </summary>
        SettingDescription = 5,
        /// <summary>
        /// 中介库AppConfigSetting配置表
        /// </summary>
        InterconnectAppConfigSetting = 6,
    }

    public static class CacheTypeExtensions
    {
        public static string GetKey(this CacheType cacheType, SessionCommonServer _sessionCommonServer)
        {
            var session = _sessionCommonServer.GetSessionByCache();
            return cacheType.ToString() + "_" + session.HospitalID + "_" + session.Language.ToString();
        }

        /// <summary>
        /// 医院ID及语言使用参数  前端模板配置页面使用 其它页面不允许使用
        /// </summary>
        /// <param name="cacheType"></param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <returns></returns>
        public static string GetKeyNotBySession(this CacheType cacheType, string hospitalID, int language)
        {
            return cacheType.ToString() + "_" + hospitalID + "_" + language.ToString();
        }
    }
}