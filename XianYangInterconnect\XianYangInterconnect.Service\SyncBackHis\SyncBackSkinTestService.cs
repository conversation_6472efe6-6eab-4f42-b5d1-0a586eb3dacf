﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Service.SyncBackHis;
using XianYangInterconnect.Services.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    public class SyncBackSkinTestService : ISyncBackSkinTestService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork<DataOutContext> _unitOfWorkOut;
        private readonly ISynchronizeLogService _synchronizeLogService;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IRequestApiService _requestApiService;

        public SyncBackSkinTestService(IUnitOfWork<DataOutContext> unitOfWorkOut
            , ISynchronizeLogService synchronizeLogService
            , IAppConfigSettingRepository appConfigSettingRepository
            , IRequestApiService requestApiService)
        {
            _unitOfWorkOut = unitOfWorkOut;
            _synchronizeLogService = synchronizeLogService;
            _appConfigSettingRepository = appConfigSettingRepository;
            _requestApiService = requestApiService;
        }
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        /// <summary>
        /// 回传患者给药皮试记录
        /// </summary>
        /// <param name="dataView">皮试相关数据view</param>
        /// <returns></returns>
        public async Task<bool> SyncBackPatientMedicineScheduleSkinTestData(SyncPatientMedicineScheduleSkinTestView dataView)
        {
            if(dataView == null)
            {
                _logger.Error("回传给药皮试记录失败，传递参数为空");
                return false;
            }
            var syncBackView = new MedicineSkinTestBackRequestParam
            {
                comboNo = dataView.ComboNo,
                execSqnDrug = dataView.ExecSqnDrug,
                execTimes = dataView.ExecTimes,
                inpatientNo = dataView.CaseNumber,
                itemType = dataView.ItemType,
                operExecTime = dataView.PerformDateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                operNurCode = dataView.ModifyEmployeeID,
                operNurName = dataView.ModifyEmployeeName,
                operType = string.IsNullOrEmpty(dataView.DeleteFlag) ? "1" : "2",
                psResult = dataView.AllergyResultCode == "0" ? "5" : "6",
            };
            await SendRequest(ListToJson.ToJson(syncBackView), dataView.InpatientID, dataView.CaseNumber);
            return true;
        }
        /// <summary>
        /// 发送请求，回传数据给his
        /// </summary>
        /// <param name="requestParam"></param>
        /// <param name="inpatientID"></param>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        private async Task SendRequest(string requestParam, string inpatientID, string caseNumber)
        {
            var settingCode = "SyncBackMedicineSkinTestBack";
            var url = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, settingCode);
            if (string.IsNullOrEmpty(url))
            {
                _logger.Error($"回传his数据失败，请求地址没有配置，SettingCode ={settingCode}");
                return;
            }
            //新增调用日志
            var syncLog = await _synchronizeLogService.CreateSynchronizeLogInfo(url, requestParam, inpatientID, caseNumber, true);
            _unitOfWorkOut.SaveChanges();
            var result = await _requestApiService.RequestAPIByAppconfigSetting(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, settingCode, requestParam, 1, null, "application/json");
            if (result == null)
            {
                return;
            }
            syncLog.SuccessFlag = "*";
            syncLog.ModifyDateTime = DateTime.Now;
            syncLog.ModifyEmployeeID = "Interconnect";
            syncLog.ResponseResult = result;
            _unitOfWorkOut.SaveChanges();
            return;
        }
    }

}
