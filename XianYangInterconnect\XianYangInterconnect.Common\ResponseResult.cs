﻿namespace XianYangInterconnect.Common
{
    /// <summary>
    /// ResponseResult
    /// </summary>
    public class ResponseResult
    {
        /// <summary>
        /// Code
        /// </summary>
        public int Code { get; set; }
        private object data;

        /// <summary>
        /// Data
        /// </summary>
        public object Data
        {
            get
            {
                return data;
            }
            set
            {
                data = value;
                Sucess();
            }
        }

        /// <summary>
        /// Message
        /// </summary>
        public string Message { get; set; }
        /// <summary>
        /// 开发环境的错误信息
        /// </summary>
        public string MessageDev { get; set; }
        /// <summary>
        /// Message
        /// </summary>
        public string Msg { get; set; }
        /// <summary>
        /// ResponseResult
        /// </summary>
        public ResponseResult()
        {
            Code = 0;
            data = null;
            Message = "";
            MessageDev = "";
            Msg = "";
        }

        /// <summary>
        /// 成功
        /// </summary>
        /// <param name="message"></param>
        public void Sucess(string message = null)
        {
            Code = 200;
            if (message != null)
            {
                Message = message;
            }
        }

        /// <summary>
        /// IsSuccess
        /// </summary>
        /// <returns></returns>
        public bool IsSuccess()
        {
            return Code == 200;
        }

        public void TimeOut()
        {
            Code = 0;
            data = null;
            Message = "登入超时,请重新登入";
        }

        public void Error(string str)
        {
            Code = 0;
            data = null;
            Message = str;
        }
    }
}