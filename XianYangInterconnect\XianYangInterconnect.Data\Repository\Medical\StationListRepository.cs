﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Repository
{
    public class StationListRepository : IStationListRepository
    {
        private readonly MedicalContext _medicalContext = null;
        public StationListRepository(
            MedicalContext medicalContext
            )
        {
            _medicalContext = medicalContext;
        }

        /// <summary>
        /// 最大ID
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<int>> GetStationListMaxID(string hospitalID)
        {
            return await _medicalContext.StationListInfos.Where(m => m.HospitalID == hospitalID).Select(m => m.ID).ToListAsync();
        }

        /// <summary>
        /// 获取所有病区列表
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<StationListInfo>> GetStationList(string hospitalID)
        {
            return await _medicalContext.StationListInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
        }

    }
}
