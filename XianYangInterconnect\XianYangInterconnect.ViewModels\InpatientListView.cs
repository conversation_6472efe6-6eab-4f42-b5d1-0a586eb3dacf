﻿namespace XianYangInterconnect.ViewModels
{
    /// <summary>
    /// 入院记录
    /// </summary>   
    public class InPatientDataView
    {
        /// <summary>
        ///住院号码
        ///</summary>
        public string CaseNumber { get; set; }
        /// <summary>
        ///病案号码
        ///</summary>
        public string ChartNo { get; set; }
        /// <summary>
        ///身分证号
        ///</summary>
        public string IdentityID { get; set; }
        /// <summary>
        ///住院次数
        ///</summary>
        public int? NumberOfAdmissions { get; set; }
        /// <summary>
        ///科别
        ///</summary>
        public string Department { get; set; }
        /// <summary>
        ///科别代码
        ///</summary>
        public string DepartmentCode { get; set; }
        /// <summary>
        ///病区(护理单元)

        ///</summary>
        public string StationName { get; set; }
        /// <summary>
        ///病区(护理单元)代码
        ///</summary>
        public string StationCode { get; set; }
        /// <summary>
        ///床位号码
        ///</summary>
        public string BedNumber { get; set; }
        /// <summary>
        ///床位代码
        ///</summary>
        public string BedCode { get; set; }
        /// <summary>
        ///ICU注记
        ///</summary>
        public string ICUFlag { get; set; }
        /// <summary>
        ///诊断码
        ///</summary>
        public string ICDCode { get; set; }
        /// <summary>
        ///诊断
        ///</summary>
        public string Diagnosis { get; set; }
        /// <summary>
        ///主治医师工号
        ///</summary>
        public string AttendingPhysicianID { get; set; }
        /// <summary>
        ///护理级别
        ///</summary>
        public string NursingLevel { get; set; }
        /// <summary>
        ///护理等级代码
        ///</summary>
        public string NursingLevelCode { get; set; }
        /// <summary>
        ///费用类型
        ///</summary>
        public string BillingPattern { get; set; }
        /// <summary>
        ///入院日期时间
        ///</summary>
        public DateTime AdmissionDateTime { get; set; }
        /// <summary>
        ///出院日期时间
        ///</summary>
        public DateTime? DischargeDateTime { get; set; }

        /// <summary>
        /// 患者在院状态。
        /// 10预入院；20在院\入院；30在科\入科；
        /// 40预出院；50出科\不在科；60实际出院；
        /// 70出院未结算；80出院已结算；90出院召回；
        /// （30、40在患者清单显示）
        /// </summary>
        public int InHospitalStatus { get; set; }
        /// <summary>
        /// 年龄 int
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 年龄明细
        /// </summary>
        public string AgeDetail { get; set; }

        //public string AgeDetail { get; set; }
        /// <summary>
        ///病人姓名
        ///</summary>
        public string PatientName { get; set; }
        /// <summary>
        ///性别

        ///</summary>
        public string Gender { get; set; }
        /// <summary>
        ///出生日期

        ///</summary>
        public DateTime? DateOfBirth { get; set; }
        /// <summary>
        ///出生时间
        ///</summary>
        public TimeSpan? TimeOfBirth { get; set; }
        /// <summary>
        ///血型
        ///</summary>
        public string BloodType { get; set; }
        /// <summary>
        ///籍贯
        ///</summary>
        public string NativePlace { get; set; }
        /// <summary>
        ///籍贯码
        ///</summary>
        public string NativePlaceCode { get; set; }
        /// <summary>
        ///病人主诉
        ///</summary>
        public string ChiefComplaint { get; set; }
        /// <summary>
        /// 入科时间
        /// </summary>
        public DateTime? EnterWardTime { get; set; }
        /// <summary>
        /// 表示属于哪个科室的病人（原因:某病区可能存在妇科病人和产科病人，用此字段区分）
        /// </summary>
        public string SpecialRemark { get; set; }
        /// <summary>
        /// 床位标识
        /// </summary>
        public string BedRemark { get; set; }
        /// <summary>
        /// 是否为虚床 
        /// </summary>
        public bool IsVirtualBed { get; set; }
        /// <summary>
        ///死亡时间
        ///</summary>
        public DateTime? DeathTime { get; set; }
        /// <summary>
        ///胎儿序号
        ///</summary>
        public int BabySN { get; set; }
        /// <summary>
        /// 体重
        ///</summary>
        public int? Weight { get; set; }
        /// <summary>
        /// 身高
        ///</summary>
        public Decimal? Height { get; set; }
        /// <summary>
        /// 父母住院流水号
        ///</summary>
        public string ParentCaseNumber { get; set; }
        /// <summary>
        /// 父母住院流水号
        ///</summary>
        public string ParentChartNo { get; set; }
        /// <summary>
        /// 患者ID
        /// </summary>
        public string cardNo { get; set; }
    }
    public class InpatientListView
    {
        /// <summary>
        /// 状态编码
        /// </summary>
        public string status { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int total { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<HisInpatientView> list { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pageNum { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pageSize { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int size { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int startRow { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int endRow { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pages { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int prePage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int nextPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string isFirstPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string isLastPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string hasPreviousPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string hasNextPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigatePages { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<int> navigatepageNums { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigateFirstPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigateLastPage { get; set; }
    }

    public class HisInpatientView
    {
        /// <summary>
        /// 年龄
        /// </summary>
        public string age { get; set; }

        /// <summary>
        /// 是否药物过敏
        /// </summary>
        public string anaphyFlag { get; set; }

        /// <summary>
        /// 费用金额(已结)
        /// </summary>
        public decimal? balanceCost { get; set; }

        /// <summary>
        /// 预交金额(已结)
        /// </summary>
        public decimal? balancePrepay { get; set; }

        /// <summary>
        /// 床号
        /// </summary>
        public string bedNo { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        public DateTime? birthday { get; set; }

        /// <summary>
        /// 患者ID
        /// </summary>
        public string cardNo { get; set; }

        /// <summary>
        /// 主诉
        /// </summary>
        public string chiefComplaint { get; set; }

        /// <summary>
        /// 医师代码(主任)
        /// </summary>
        public string chiefDocCode { get; set; }

        /// <summary>
        /// 医师姓名(主任)
        /// </summary>
        public string chiefDocName { get; set; }

        /// <summary>
        /// 危重情况
        /// </summary>
        public string criticalFlag { get; set; }

        public DateTime? deathTime { get; set; }

        public string deptAdmissionTo { get; set; }
        public string deptAdmissionToName { get; set; }

        /// <summary>
        /// 科室代码
        /// </summary>
        public string deptCode { get; set; }

        /// <summary>
        /// 科室
        /// </summary>
        public string deptName { get; set; }

        /// <summary>
        /// 诊断2
        /// </summary>
        public string diagnose2 { get; set; }

        /// <summary>
        /// 诊断3
        /// </summary>
        public string diagnose3 { get; set; }

        /// <summary>
        /// 主治医生编码
        /// </summary>
        public string directorDocCode { get; set; }

        /// <summary>
        /// 主治医生姓名
        /// </summary>
        public string directorDocName { get; set; }

        /// <summary>
        /// 责任护士编码
        /// </summary>
        public string dutyNurseCode { get; set; }

        /// <summary>
        /// 责任护士姓名
        /// </summary>
        public string dutyNurseName { get; set; }

        public string emrId { get; set; }

        public string familyHistory { get; set; }

        /// <summary>
        /// 余额
        /// </summary>
        public string freeCost { get; set; }

        /// <summary>
        /// 身高
        /// </summary>
        public Decimal? height { get; set; }

        /// <summary>
        /// 电话号码
        /// </summary>
        public string homeTel { get; set; }

        /// <summary>
        /// 身份证号
        /// </summary>
        public string idenno { get; set; }
        /// <summary>
        /// 患者来源
        /// </summary>
        public string inAvenuei { get; set; }


        /// <summary>
        /// 入院时间
        /// </summary>
        public DateTime? inDate { get; set; }

        /// <summary>
        /// 在院天数
        /// </summary>
        public string inDays { get; set; }

        /// <summary>
        /// 住院流水号
        /// </summary>
        public string inpatientNo { get; set; }

        public string inSource { get; set; }

        /// <summary>
        /// 住院次数
        /// </summary>
        public int? inTimes { get; set; }

        /// <summary>
        /// 是否新生儿
        /// </summary>
        public string isNewBorn { get; set; }

        /// <summary>
        /// 联系人地址
        /// </summary>
        public string linkmanAdd { get; set; }

        /// <summary>
        /// 联系人姓名
        /// </summary>
        public string linkmanName { get; set; }

        /// <summary>
        /// 联系人电话
        /// </summary>
        public string linkmanTel { get; set; }

        /// <summary>
        /// 主诊断
        /// </summary>
        public string mainDiagnose { get; set; }

        /// <summary>
        /// 病区编号
        /// </summary>
        public string nurseCellCode { get; set; }

        /// <summary>
        /// 病区名称
        /// </summary>
        public string nurseCellName { get; set; }

        /// <summary>
        /// 护理级别
        /// </summary>
        public string nursingLevel { get; set; }

        /// <summary>
        /// 出院日期
        /// </summary>
        public DateTime? outDate { get; set; }

        public string outType { get; set; }

        /// <summary>
        /// 合同单位
        /// </summary>
        public string pactCode { get; set; }

        /// <summary>
        /// 合同单位名称
        /// </summary>
        public string pactName { get; set; }

        /// <summary>
        /// 患者ID
        /// </summary>
        public string patientId { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string patientName { get; set; }

        /// <summary>
        /// 住院号
        /// </summary>
        public string patientNo { get; set; }

        /// <summary>
        /// 患者状态
        /// </summary>
        public string patientState { get; set; }

        /// <summary>
        /// 费用类别
        /// </summary>
        public string payKindCode { get; set; }

        /// <summary>
        /// 费用类别名称
        /// </summary>
        public string payKindName { get; set; }

        /// <summary>
        /// 预交金
        /// </summary>
        public string prepayCost { get; set; }

        /// <summary>
        /// 现病史
        /// </summary>
        public string presentIllness { get; set; }

        /// <summary>
        /// 既往史
        /// </summary>
        public string previousHistory { get; set; }

        public string profCode { get; set; }
        public string profession { get; set; }

        /// <summary>
        /// 经治医生编码
        /// </summary>
        public string residencyDocCode { get; set; }

        /// <summary>
        /// 经治医生姓名
        /// </summary>
        public string residencyDocName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string sexCode { get; set; }

        /// <summary>
        /// 总费用
        /// </summary>
        public string totCost { get; set; }

        /// <summary>
        /// 体重
        /// </summary>
        public decimal? weight { get; set; }
        /// <summary>
        /// 归转标记
        /// </summary>
        public string zg { get; set; }
    }
}