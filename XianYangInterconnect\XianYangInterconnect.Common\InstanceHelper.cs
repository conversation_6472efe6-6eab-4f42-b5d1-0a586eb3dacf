﻿using System.Reflection;

namespace XianYangInterconnect.Common
{
    public class InstanceHelper
    {
        public static T GetInstance<T>(string instanceName)
        {
            return (T)Assembly.Load(Assembly.GetAssembly(typeof(T)).GetName().Name).CreateInstance(typeof(T).Namespace + "." + instanceName);
        }

        public static T GetInstance<T>(string instanceName, params object[] param)
        {
            return (T)Assembly.Load(Assembly.GetAssembly(typeof(T)).GetName().Name).CreateInstance(typeof(T).Namespace + "." + instanceName, true, BindingFlags.CreateInstance, null, param, Thread.CurrentThread.CurrentCulture, null);
        }
    }
}