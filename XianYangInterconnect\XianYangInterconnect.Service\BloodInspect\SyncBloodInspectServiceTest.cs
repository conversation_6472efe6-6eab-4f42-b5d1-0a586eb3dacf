using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service.BloodInspect
{
    /// <summary>
    /// SyncBloodInspectService 测试类
    /// 用于验证数据转换和处理逻辑
    /// </summary>
    public class SyncBloodInspectServiceTest
    {
        /// <summary>
        /// 测试HIS数据转换为输入数据
        /// </summary>
        public static void TestDataConversion()
        {
            // 创建测试数据
            var testHisData = new List<HisBloodInspectView>
            {
                new HisBloodInspectView
                {
                    RecordId = "TEST001",
                    CaseNumber = "ZY202501010001",
                    BloodNumber = "BLOOD001",
                    SourceUniqueID = "UNIQUE001",
                    PerformEmployeeID = "EMP001",
                    PerformEmployeeName = "张三",
                    PerformDateTime = DateTime.Now,
                    Temperature = "36.5",
                    HeartRate = "80",
                    Breathing = "18",
                    BloodPressure = "120/80",
                    NursingObservation = "患者状态良好，无异常反应",
                    PatientId = "PAT001",
                    PatientName = "李四",
                    DepartmentCode = "DEPT001",
                    DepartmentName = "内科",
                    WardCode = "WARD001",
                    WardName = "内科病区",
                    BedNo = "001",
                    CreateDateTime = DateTime.Now,
                    DeleteFlag = "",
                    ValidFlag = "1"
                },
                new HisBloodInspectView
                {
                    RecordId = "TEST002",
                    CaseNumber = "ZY202501010002",
                    BloodNumber = "BLOOD002",
                    SourceUniqueID = "UNIQUE002",
                    PerformEmployeeID = "EMP002",
                    PerformEmployeeName = "王五",
                    PerformDateTime = DateTime.Now.AddHours(-1),
                    Temperature = "37.0",
                    HeartRate = "85",
                    Breathing = "20",
                    BloodPressure = "130/85",
                    NursingObservation = "患者轻微发热，密切观察",
                    PatientId = "PAT002",
                    PatientName = "赵六",
                    DepartmentCode = "DEPT002",
                    DepartmentName = "外科",
                    WardCode = "WARD002",
                    WardName = "外科病区",
                    BedNo = "002",
                    CreateDateTime = DateTime.Now.AddHours(-1),
                    DeleteFlag = "",
                    ValidFlag = "1"
                }
            };

            // 测试转换
            var inputDataList = InterconnectCoreBloodInspectInputView.FromHisDataList(testHisData);

            // 验证转换结果
            Console.WriteLine($"原始数据条数: {testHisData.Count}");
            Console.WriteLine($"转换后数据条数: {inputDataList.Count}");

            foreach (var inputData in inputDataList)
            {
                Console.WriteLine($"病案号: {inputData.CaseNumber}");
                Console.WriteLine($"血袋号: {inputData.BloodNumber}");
                Console.WriteLine($"执行人ID: {inputData.PerformEmployeeID}");
                Console.WriteLine($"执行时间: {inputData.PerformDateTime}");
                Console.WriteLine($"键值对数量: {inputData.KeyValueItems.Count}");
                
                foreach (var kvp in inputData.KeyValueItems)
                {
                    Console.WriteLine($"  键: {kvp.Key}, 值: {kvp.Value}");
                }
                Console.WriteLine("---");
            }
        }

        /// <summary>
        /// 测试字段映射
        /// </summary>
        public static void TestFieldMapping()
        {
            var inputView = new InterconnectCoreBloodInspectInputView();
            
            // 测试各种字段映射
            inputView.SetKeyValue("EMRSource_72", "36.8");  // 体温
            inputView.SetKeyValue("EMRSource_74", "75");    // 心率
            inputView.SetKeyValue("EMRSource_163", "16");   // 呼吸
            inputView.SetKeyValue("EMRSource_164", "110/70"); // 血压
            inputView.SetKeyValue("EMRSource_168", "患者无不适"); // 护理观察
            inputView.SetKeyValue("EMRSource_999", "无效字段"); // 无效字段，应该被忽略

            Console.WriteLine("字段映射测试结果:");
            Console.WriteLine($"映射的键值对数量: {inputView.KeyValueItems.Count}");
            
            foreach (var kvp in inputView.KeyValueItems)
            {
                string fieldName = kvp.Key switch
                {
                    72 => "体温",
                    74 => "心率", 
                    163 => "呼吸",
                    164 => "血压",
                    168 => "护理观察",
                    _ => "未知字段"
                };
                Console.WriteLine($"  {fieldName}({kvp.Key}): {kvp.Value}");
            }
        }
    }
}
