﻿using MockPlatform.Models;

namespace XianYangInterconnect.ViewModels
{
    public class EmployeeListView : EmployeeCommonView
    {
        /// <summary>
        /// 员工ID
        /// </summary>
        public string EmplId { get; set; }
        /// <summary>
        /// 职务代号
        /// </summary>
        public string PosiName { get; set; }
        /// <summary>
        /// 职级名称
        /// </summary>
        public string LevlName { get; set; }
        /// <summary>
        /// 职级代号
        /// </summary>
        public string LevelName { get; set; }
        /// <summary>
        /// 所属科室号
        /// </summary>
        public string DeptId { get; set; }
        /// <summary>
        /// 所属科室名称
        /// </summary>
        public string DeptName { get; set; }
        /// <summary>
        /// 人员类型
        /// </summary>
        public string EmplTypeName { get; set; }
        /// <summary>
        /// 创建日期
        /// </summary>
        public DateTime CreateDate { get; set; }
    }
}
