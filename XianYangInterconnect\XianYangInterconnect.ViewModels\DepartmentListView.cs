﻿namespace XianYangInterconnect.ViewModels
{
    public class DepartmentListView
    {
        /// <summary>
        /// 成功码
        /// </summary>
        public int code { get; set; }
        /// <summary>
        /// 信息
        /// </summary>
        public string msg { get; set; }
        /// <summary>
        /// 床位数据
        /// </summary>
        public List<DepartmentData> data { get; set; }
    }
    public class DepartmentData
    {
        /// <summary>
        /// 科室编码
        /// </summary>
        public string deptCode { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>
        public string deptName { get; set; }
        /// <summary>
        /// 科室拼音
        /// </summary>
        public string spellCode { get; set; }
        /// <summary>
        /// 科室五笔码
        /// </summary>
        public string wbCode { get; set; }
        /// <summary>
        /// 科室英文
        /// </summary>
        public string deptEname { get; set; }
        /// <summary>
        /// 发药时间
        /// </summary>
        public string mediTime { get; set; }
        /// <summary>
        /// 周期开始
        /// </summary>
        public string cycleBegin { get; set; }
        /// <summary>
        /// 周期结束
        /// </summary>
        public string cycleEnd { get; set; }
        /// <summary>
        /// 是否挂号科室 （0：否，1：是）
        /// </summary>
        public string regdeptFlag { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string tatdeptFlag { get; set; }
        /// <summary>
        /// 特殊科室属性
        /// </summary>
        public string deptPro { get; set; }
        /// <summary>
        /// 警戒线
        /// </summary>
        public string alterMoney { get; set; }
        /// <summary>
        /// 扩展标志 －是否已经集中发送 0 未,1 已
        /// </summary>
        public string extFlag { get; set; }
        /// <summary>
        /// 扩展标志1
        /// </summary>
        public string ext1Flag { get; set; }
        /// <summary>
        /// 有效性标志 1在用 0 停用 2 废弃
        /// </summary>
        public string validState { get; set; }
        /// <summary>
        /// 顺序号
        /// </summary>
        public int sortId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int sortNo { get; set; }
        /// <summary>
        /// 操作员
        /// </summary>
        public string operCode { get; set; }
        /// <summary>
        ///  操作时间
        /// </summary>
        public string operDate { get; set; }
        /// <summary>
        /// 自定义码
        /// </summary>
        public string userCode { get; set; }
        /// <summary>
        /// 科室简称
        /// </summary>
        public string simpleName { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string createDate { get; set; }
        /// <summary>
        /// 科室专业分类编码
        /// </summary>
        public string deptProfCode { get; set; }
        /// <summary>
        /// 科室简介
        /// </summary>
        public string deptDesc { get; set; }
        /// <summary>
        /// 科室联系电话
        /// </summary>
        public string deptTel { get; set; }
        /// <summary>
        /// 子院娶编码
        /// </summary>
        public string subBranchCode { get; set; }
        /// <summary>
        /// 总院
        /// </summary>
        public string branchCode { get; set; }
        /// <summary>
        /// 科室地址
        /// </summary>
        public string deptAddress { get; set; }
        /// <summary>
        /// 科室类型
        /// </summary>
        public string deptType { get; set; }
        /// <summary>
        /// 病区编码
        /// </summary>
        public string wardareaCode { get; set; }
        /// <summary>
        /// 病区名称
        /// </summary>
        public string wardareaName { get; set; }
        /// <summary>
        /// 授权科室： 1-本科室，0-授权科室
        /// </summary>
        public string ownedType { get; set; }
    }
}
