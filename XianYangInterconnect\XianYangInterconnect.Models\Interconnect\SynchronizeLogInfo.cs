﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XianYangInterconnect.Models.Interconnect
{
    [Serializable]
    [Table("SynchronizeLog")]
    public class SynchronizeLogInfo 
    {
        /// <summary>
        /// ID
        /// </summary>
        [Key]
        [Column("SynchronizeLogID", TypeName = "varchar(32)")]
        public string ID { get; set; }
        /// <summary>
        /// 日志日期
        /// </summary>
        public DateTime SynchronizeDate { get; set; }
        /// <summary>
        /// ApiUrl
        /// </summary>
        [Column(TypeName = "varchar(400)")]
        public string ApiUrl { get; set; }
        /// <summary>
        /// Post或Get
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string PostOrGet { get; set; }
        /// <summary>
        /// 来源类型
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SourceType { get; set; }
        /// <summary>
        /// 来源Id
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string SourceID { get; set; }
        /// <summary>
        /// 排程开始时间
        /// </summary>
        public DateTime? StartDate { get; set; }
        /// <summary>
        /// 排程结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }
        /// <summary>
        /// 对象
        /// </summary>
        public string Arguments { get; set; }
        /// <summary>
        /// 成功标记
        /// </summary>
        [Column(TypeName = "char(1)")]
        public string SuccessFlag { get; set; } = "";
        /// <summary>
        /// 错误尝试次数
        /// </summary>
        public byte RetryTimes { get; set; }
        /// <summary>
        /// 修改人
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }
        /// <summary>
        /// 删除标记
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string DeleteFlag { get; set; }
        /// <summary>
        /// 移动端执行标记
        /// </summary>
        [Column(TypeName = "char(1)")]
        public string ClientType { get; set; }
        /// <summary>
        /// 推送响应结果
        /// </summary>
        [Column(TypeName = "varchar(8000)")]
        public string ResponseResult { get; set; }
        /// <summary>
        /// 病人住院序号
        /// </summary>
        [Column(TypeName = "varchar(32)")]
        public string InpatientID { get; set; }
        /// <summary>
        /// 病人住院号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string CaseNumber { get; set; }
    }
}
