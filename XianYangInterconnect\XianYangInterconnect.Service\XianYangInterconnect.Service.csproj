<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <None Remove=".filenesting.json" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Hangfire.Core" Version="1.8.16" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.UnitOfWork" Version="3.1.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\XianYangInterconnect.Data.Interface\XianYangInterconnect.Data.Interface.csproj" />
    <ProjectReference Include="..\XianYangInterconnect.Data\XianYangInterconnect.Data.csproj" />
    <ProjectReference Include="..\XianYangInterconnect.Service.Interface\XianYangInterconnect.Service.Interface.csproj" />
    <ProjectReference Include="..\XianYangInterconnect.ViewModels\XianYangInterconnect.ViewModels.csproj" />
  </ItemGroup>
</Project>