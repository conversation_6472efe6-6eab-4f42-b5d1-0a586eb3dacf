﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using XianYangInterconnect.API.Extensions;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Data.Repository;
using XianYangInterconnect.Service.Interface;

namespace XianYangInterconnect.API.Controllers
{
    /// <summary>
    /// 测试控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/sync")]
    [EnableCors("any")]
    public class TestController : ControllerBase
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IBedService _bedService;
        private readonly MQListenerService _mQListenerService;
        private readonly IBYJDataRepository _IBYJDataRepository;
        private readonly IExecDrugRepository _execDrugRepository;
        private readonly IHISPatientOrderRepository _hISPatientOrderRepository;

        /// <summary>
        /// 测试控制器的构造器
        /// </summary>
        /// <param name="bedService"></param>
        /// <param name="mQListenerService"></param>
        /// <param name="BYJDataRepository"></param>
        /// <param name="execDrugRepository"></param>
        /// <param name="hISPatientOrderRepository"></param>
        public TestController(IBedService bedService, MQListenerService mQListenerService
            , IBYJDataRepository BYJDataRepository
            , IExecDrugRepository execDrugRepository
            , IHISPatientOrderRepository hISPatientOrderRepository)
        {
            _bedService = bedService;
            _mQListenerService = mQListenerService;
            _IBYJDataRepository = BYJDataRepository;
            _execDrugRepository = execDrugRepository;
            _hISPatientOrderRepository = hISPatientOrderRepository;
        }


        /// <summary>
        /// 获取HIS摆药机数据
        /// </summary>
        /// <param name="minute">小于当前时间多少分钟</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetIBYJData")]
        public async Task<IActionResult> GetIBYJData(int minute)
        {
            var result = new ResponseResult
            {
                Data = await _IBYJDataRepository.GetAsync(minute)
            };
            return result.ToJson();
        }

        /// <summary>
        /// 获取HIS医嘱数据
        /// </summary>
        /// <param name="casenumber"></param>
        /// <param name="minute">小于当前时间多少分钟</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetPatientOrderByCasenumber")]
        public async Task<IActionResult> GetPatientOrderByCasenumber(string casenumber, int minute)
        {
            var result = new ResponseResult
            {
                Data = await _hISPatientOrderRepository.GetByCasenumber(casenumber, minute)
            };
            return result.ToJson();
        }

        /// <summary>
        /// 获取服务器时间
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetServerDateTime")]
        public IActionResult GetServerDateTime()
        {
            var result = new ResponseResult();
            result.Data = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            return result.ToJson();
        }

        /// <summary>
        /// 根据病区代码获取床位数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetBedListByStationCode")]
        public async Task<IActionResult> GetBedListByStationCode(string stationCode)
        {
            var result = new ResponseResult();
            result.Data = await _bedService.GetBedListByStationCode(stationCode);
            return result.ToJson();
        }

        [HttpGet]
        [NoAuthorization]
        [Route("TestIBMMQ")]
        public async Task<IActionResult> TestIBMMQ()
        {
            var result = new ResponseResult();
            _mQListenerService.ReceiveMessagesAsynchronous();
            return result.ToJson();
        }

        /// <summary>
        /// 根据时间差、CaseNumber、用药的开始结束时间，获取单患者药品执行信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="lastDateTime"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetExecDrugByTimeDiff")]
        public async Task<IActionResult> GetExecDrugByTimeDiff(string caseNumber, DateTime lastDateTime, DateTime beginTime, DateTime endTime)
        {
            var result = new ResponseResult();
            result.Data = await _execDrugRepository.GetByTimeDiff(caseNumber, lastDateTime, beginTime, endTime);
            return result.ToJson();
        }
    }
}