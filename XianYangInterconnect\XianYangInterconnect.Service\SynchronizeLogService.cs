﻿using Arch.EntityFrameworkCore.UnitOfWork;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Models.Interconnect;

namespace XianYangInterconnect.Service
{
    public class SynchronizeLogService(IUnitOfWork<DataOutContext> unitOfWorkOut) : ISynchronizeLogService
    {
        private readonly IUnitOfWork<DataOutContext> _unitOfWorkOut = unitOfWorkOut;

        /// <summary>
        /// 创建日志model类
        /// </summary>
        /// <param name="apiUrl">api地址</param>
        /// <param name="args">请求参数</param>
        /// <param name="inpatientID">患者CCC住院唯一ID</param>
        /// <param name="caseNumber">就诊流水号</param>
        /// <param name="isPost">是否为post请求</param>
        /// <returns></returns>
        public async Task<SynchronizeLogInfo> CreateSynchronizeLogInfo(string apiUrl, string args, string inpatientID,
            string caseNumber, bool isPost)
        {
            var logInfo = new SynchronizeLogInfo
            {
                ID = Guid.NewGuid().ToString("N"),
                SynchronizeDate = DateTime.Now,
                ApiUrl = apiUrl,
                PostOrGet = isPost ? "POST" : "Get",
                InpatientID = inpatientID,
                CaseNumber = caseNumber,
                Arguments = args,
                SuccessFlag = "",
                ModifyDateTime = DateTime.Now,
                ModifyEmployeeID = "Interconnect",
                DeleteFlag = ""
            };
            await _unitOfWorkOut.GetRepository<SynchronizeLogInfo>().InsertAsync(logInfo);
            return logInfo;
        }
    }
}
