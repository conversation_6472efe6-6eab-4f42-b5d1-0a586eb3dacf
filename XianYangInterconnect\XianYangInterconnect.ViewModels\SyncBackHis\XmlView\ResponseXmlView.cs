﻿ using System.Xml.Serialization;
namespace XianYangInterconnect.Service.SyncBackHis
{
    [XmlRoot(ElementName = "Envelope", Namespace = "http://www.w3.org/2003/05/soap-envelope")]
    public class ResponseXmlView
    {
        [XmlElement(ElementName = "Body", Namespace = "http://www.w3.org/2003/05/soap-envelope")]
        public ResponseXmlSoapBody Body { get; set; }
    }

    public class ResponseXmlSoapBody
    {
        [XmlElement(ElementName = "result", Namespace = "")]
        public Result Result { get; set; }
    }

    public class Result
    {
        [XmlElement(ElementName = "returnCode")]
        public string ReturnCode { get; set; }
        [XmlElement(ElementName = "returnMessage")]
        public string ReturnMessage { get; set; }
    }

}
