﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using XianYangInterconnect.API.Extensions;
using XianYangInterconnect.Common;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.API.Controllers
{
    /// <summary>
    /// 同步字典数据控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/Dictionary")]
    [EnableCors("any")]
    public class DictionaryController : ControllerBase
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IDictionaryService _dictionaryService;

        /// <summary>
        /// 同步人员数据构造函数
        /// </summary>
        /// <param name="dictionaryService"></param>
        public DictionaryController(IDictionaryService dictionaryService)
        {
            _dictionaryService = dictionaryService;
        }

        /// <summary>
        /// 获取员工列表
        /// </summary>
        /// <param name="emplCode">员工代码</param>
        /// <param name="emplName">员工姓名</param>
        /// <param name="spellCode">拼音码</param>
        /// <param name="wbCode">五笔</param>
        /// <param name="sexCode">性别</param>
        /// <param name="birthday">出生日期</param>
        /// <param name="posiCode">职务代号</param>
        /// <param name="levlCode">职级代号</param>
        /// <param name="educationCode">学历</param>
        /// <param name="idenno">身份证号</param>
        /// <param name="deptCode">所属科室号</param>
        /// <param name="nurseCellCode">所属护理站</param>
        /// <param name="emplType">人员类型(D医生、N护士、T技师、P药师、C厨师、F收款员、O其他)</param>
        /// <param name="expertFlag">是否专家</param>
        /// <param name="createDateStart">创建开始时间</param>
        /// <param name="createDateEnd">创建结束时间</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetAllEmployeeData")]
        public async Task<IActionResult> GetAllEmployeeData(string emplCode, string emplName, string spellCode, string wbCode, string sexCode, string birthday
            , string posiCode, string levlCode, string educationCode, string idenno, string deptCode, string nurseCellCode, string emplType, string expertFlag
            , string createDateStart, string createDateEnd)
        {
            var result = new ResponseResult();
            result.Data = await _dictionaryService.GetAllEmployeeData(emplCode, emplName, spellCode, wbCode, sexCode, birthday
            , posiCode, levlCode, educationCode, idenno, deptCode, nurseCellCode, emplType, expertFlag
            , createDateStart, createDateEnd);
            return result.ToJson();
        }

        /// <summary>
        /// 根据员工编号获取单个员工
        /// </summary>
        /// <param name="emplId">员工编号</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetOneEmployeeData")]
        public async Task<IActionResult> GetOneEmployeeData(string emplId)
        {
            var result = new ResponseResult();
            result.Data = await _dictionaryService.GetOneEmployeeData(emplId);
            return result.ToJson();
        }
        /// <summary>
        /// 获取床位字典
        /// </summary>
        /// <param name="bedCode">病床编码</param>
        /// <param name="bedPrice">床位价格</param>
        /// <param name="bedState">床位状态</param>
        /// <param name="deptCode">病区编码</param>
        /// <param name="roomCode">病室编码</param>
        /// <param name="roomUbedSum">空床数量</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetBedList")]
        public async Task<ActionResult<bool>> GetBedListAsync(string bedCode, string bedPrice, string bedState, string deptCode, string roomCode, string roomUbedSum)
        {
            return await _dictionaryService.GetBedListAsync(bedCode, bedPrice, bedState, deptCode, roomCode, roomUbedSum);
        }
        /// <summary>
        /// 清洗HIS病区科室数据
        /// </summary>
        /// <param name="branchCode">院区编码</param>
        /// <param name="deptCode">病区或科室编码</param>
        /// <param name="deptType">病区类型</param>
        /// <param name="validState">病区状态</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetStationAndDepartment")]
        public async Task<ActionResult<bool>> GetStationAndDepartment(string branchCode, string deptCode, string deptType, string validState)
        {
            return await _dictionaryService.GetStationAndDepartmentAsync(branchCode, deptCode, deptType, validState);
        }
        /// <summary>
        /// 获取医嘱字典
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetOrderDict")]
        public async Task<ActionResult<bool>> GetOrderDict()
        {
            return await _dictionaryService.GetOrderDictAsync();
        }

        /// <summary>
        /// 获取药品字典
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetDrugList")]
        public async Task<ActionResult<bool>> GetDrugList()
        {
            return await _dictionaryService.GetDrugList();
        }

        /// <summary>
        /// 通过插件同步CA
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncCAByPlugin")]
        public async Task<IActionResult> SyncCAByPluginAsync([FromBody] PluginCAView caUserInfo)
        {
           var result = new ResponseResult();
            result.Data = await _dictionaryService.SyncCAByPlugin(caUserInfo);
            return result.ToJson();
        }
    }
}
