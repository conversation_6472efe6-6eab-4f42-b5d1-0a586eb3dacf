﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XianYangInterconnect.Models.Base;

namespace XianYangInterconnect.Models
{
    /// <summary>
    /// 病床
    /// </summary>
    [Serializable]
    [Table("BedList")]
    public partial class BedListInfo : ModifyInfo
    {
        /// <summary>
        /// 流水号
        /// </summary>       
        [Key]
        [Column("BedID")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }
        /// <summary>
        /// 床位号码
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string BedNumber { get; set; }
        /// <summary>
        /// 病区序号
        /// </summary>
        public int StationID { get; set; }
        /// <summary>
        /// 科别序号
        /// </summary>
        public int DepartmentListID { get; set; }
        /// <summary>
        /// 重症病房注记(Y:ICU)
        /// </summary>
        [Column(TypeName = "char(1)")]
        public string ICUFlag { get; set; }
        /// <summary>
        /// 停用注记(空白:使用,*停用)
        /// </summary>
        [Column(TypeName = "char(1)")]
        public string DisableFlag { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public short Sort { get; set; }
        /// <summary>
        /// 新生儿标记(空白：不是新生儿，*是新生儿)
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string NewBornFlag { get; set; }

        /// <summary>
        /// 是否加床0一般1加床
        /// </summary>
        public bool? AdditionFlag { get; set; }
        /// <summary>
        /// 床位所属物理位置
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string BedLocation { get; set; }

        /// <summary>
        /// 病房名称
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string RoomCode { get; set; }
        /// <summary>
        /// 楼栋
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string WardBuilding { get; set; }
        /// <summary>
        /// 楼层
        /// </summary>
        [Column(TypeName = "varchar(200)")]
        public string WardFloor { get; set; }

        /// <summary>
        ///医院ID
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
    }
}
