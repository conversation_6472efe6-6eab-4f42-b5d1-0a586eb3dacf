﻿using System.Text;

namespace XianYangInterconnect.Common
{
    public static class EncryptionAndDecryption
    {
        /// <summary>
        /// 加密方法
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string Encryption(string str)
        {
            var newStr = "";
            char[] arr = str.ToCharArray();
            int newChar = 0;
            for (int i = 0; i < arr.Length; i++)
            {
                if (i % 2 == 0)
                {
                    newChar = arr[i] + i - 32;
                }
                else
                {
                    newChar = arr[i] - i + 8;
                }
                newStr = newStr + (char)newChar;
            }

            //转Base64
            byte[] base64Data = Encoding.UTF8.GetBytes(newStr);
            var base64Str = Convert.ToBase64String(base64Data);
            return base64Str.Replace("+", "-").Replace("/", "_");
        }

        //解密
        public static string DecryptStr(string decryptStr)
        {
            if (string.IsNullOrWhiteSpace(decryptStr))
            {
                return decryptStr;
            }
            //解析Base64
            var str = "";
            try
            {
                decryptStr = decryptStr.Replace("-", "+").Replace("_", "/");
                var isBase64 = ImageHelper.IsBase64(decryptStr);
                if (!isBase64)
                {
                    return decryptStr;
                }
                var base64Data = Convert.FromBase64String(decryptStr);
                str = Encoding.UTF8.GetString(base64Data);
            }
            catch (Exception e)
            {
                return e.ToString();
            }

            var newStrArrId = "";
            char[] arr = str.ToCharArray();
            int newChar = 0;
            for (int i = 0; i < arr.Length; i++)
            {
                if (i % 2 == 0)
                {
                    newChar = arr[i] - i + 32;
                }
                else
                {
                    newChar = arr[i] + i - 8;
                }
                newStrArrId = newStrArrId + (char)newChar;
            }
            return newStrArrId;
        }
    }
}