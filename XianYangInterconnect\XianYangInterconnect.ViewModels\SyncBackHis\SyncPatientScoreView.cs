﻿namespace XianYangInterconnect.ViewModels
{
    public class SyncPatientScoreView : SyncBackHISBasicData
    {
        /// <summary>
        /// 病人评量序号
        /// </summary>
        public string PatientScoreMainID { get; set; }
        /// <summary>
        /// 表单序号
        /// </summary>
        public int RecordListID { get; set; }
        /// <summary>
        /// 评估日期
        /// </summary>
        public DateTime AssessDate { get; set; }
        /// <summary>
        /// 评估时间
        /// </summary>
        public TimeSpan AssessTime { get; set; }
        /// <summary>
        /// 新增人工号
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 修改人工号
        /// </summary>
        public object ModifyEmployeeID { get; set; }
        /// <summary>
        /// 删除标记
        /// </summary>
        public string DeleteFlag { get; set; }
        /// <summary>
        /// 风险分数
        /// </summary>
        public int ScorePoint { get; set; }
        /// <summary>
        /// 风险距ID
        /// </summary>
        public int ScoreRangeID { get; set; }
        /// <summary>
        /// 风险描述
        /// </summary>
        public string ScoreRangeContent { get; set; }
        /// <summary>
        /// 新增人姓名
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 修改人姓名
        /// </summary>
        public string ModifyEmployeeName { get; set; }
    }
}
