﻿namespace XianYangInterconnect.Common
{
    public class ListSplit
    {
        public List<List<T>> Split<T>(List<T> list, int partNum)
        {
            var partCount = list.Count / partNum;
            if (list.Count % partNum > 0)
            {
                partCount++;
            }
            List<List<T>> result = new List<List<T>>();
            int j = partNum;
            for (int i = 0; i < list.Count; i += partNum)
            {
                var tmp = list.Take(j).Skip(i).ToList();
                j += partNum;
                result.Add(tmp);
            }
            return result;
        }

        public List<Tuple<string, List<T>>> SplitByID<T>(List<string> ids, List<T> orginalList)
        {
            var list = new List<Tuple<string, List<T>>>();
            List<T> tmpList;

            if (ids.Count == 0)
            {
                return list;
            }
            else if (ids.Count == 1)
            {
                list.Add(new Tuple<string, List<T>>(ids[0], orginalList));
                return list;
            }
            var indexs = new List<int>();
            for (int i = 0; i < ids.Count - 1; i++)
            {
                if (ids[i] != ids[i + 1])
                {
                    indexs.Add(i);
                }
            }
            var startIndex = 0;
            for (int i = 0; i <= indexs.Count; i++)
            {
                if (i == indexs.Count)
                {
                    tmpList = orginalList.Skip(startIndex).ToList();
                }
                else
                {
                    tmpList = orginalList.Skip(startIndex).Take(indexs[i] - startIndex + 1).ToList();
                }

                list.Add(new Tuple<string, List<T>>(ids[startIndex], tmpList));
                if (i < indexs.Count)
                {
                    startIndex = indexs[i] + 1;
                }
            }

            return list;
        }
    }
}