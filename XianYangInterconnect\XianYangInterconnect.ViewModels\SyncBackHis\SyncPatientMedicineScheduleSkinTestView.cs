﻿namespace XianYangInterconnect.ViewModels
{
    public class SyncPatientMedicineScheduleSkinTestView : SyncBackHISBasicData
    {
        /// <summary>
        /// 病人皮试序号
        /// </summary>
        public string PatientSkinTestID { get; set; }
        /// <summary>
        /// 组合号
        /// </summary>
        public string ComboNo { get; set; }
        /// <summary>
        /// 执行单流水号
        /// </summary>
        public string ExecSqnDrug { get; set; }
        /// <summary>
        /// 执行次数（新增：1，修改、删除：2）
        /// </summary>
        public string ExecTimes { get; set; }
        /// <summary>
        /// 项目类型 1 药品；2 非药品
        /// </summary>
        public string ItemType { get; set; }
        /// <summary>
        /// 执行日期时间
        /// </summary>
        public DateTime PerformDateTime { get; set; }
        /// <summary>
        /// 新增人工号
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 修改人工号
        /// </summary>
        public string ModifyEmployeeID { get; set; }
        /// <summary>
        /// 删除标记
        /// </summary>
        public string DeleteFlag { get; set; }
        /// <summary>
        /// 过敏测试结果code（0：阴性；1：阳性）
        /// </summary>
        public string AllergyResultCode { get; set; }
        /// <summary>
        /// 过敏测试结果名称
        /// </summary>
        public string AllergyResultName { get; set; }
        /// <summary>
        /// 新增人姓名
        /// </summary>
        public string AddEmployeeName { get; set; }
        /// <summary>
        /// 修改人姓名
        /// </summary>
        public string ModifyEmployeeName { get; set; }
    }
}
