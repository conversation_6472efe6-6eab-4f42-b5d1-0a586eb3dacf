﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XianYangInterconnect.Models.Base;

namespace XianYangInterconnect.Models
{
    /// <summary>
    /// 病人住院记录表
    /// </summary>
    [Serializable]
    [Table("InpatientData")]
    public class InpatientDataInfo : ModifyInfo
    {
        /// <summary>
        /// GUID-32
        /// </summary>
        [Key]
        [Column("InpatientID", TypeName = "char(32)")]
        public string ID { get; set; }

        /// <summary>
        /// 病人基本信息表ID
        /// </summary>
        [Column(TypeName = "char(32)")]
        public string PatientID { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 住院号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string CaseNumber { get; set; }

        /// <summary>
        /// 病历号
        /// </summary>
        [Column(TypeName = "varchar(30)")]
        public string ChartNo { get; set; }

        /// <summary>
        /// 住院次数
        /// </summary>
        public int NumberOfAdmissions { get; set; }

        /// <summary>
        /// 科别序号
        /// </summary>
        public int DepartmentListID { get; set; }

        /// <summary>
        /// 单位序号
        /// </summary>
        public int StationID { get; set; }

        /// <summary>
        /// 床位序号
        /// </summary>
        public int BedID { get; set; }

        /// <summary>
        /// 床号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string BedNumber { get; set; }

        /// <summary>
        /// ICU注记
        /// </summary>
        [Column(TypeName = "char(1)")]
        public string ICUFlag { get; set; }

        /// <summary>
        /// 诊断码
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string ICDCode { get; set; }

        /// <summary>
        /// 诊断名称
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string Diagnosis { get; set; }

        /// <summary>
        /// 主治医师
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string AttendingPhysicianID { get; set; }

        /// <summary>
        /// 护理级别
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string NursingLevel { get; set; }

        /// <summary>
        /// 费用类型
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string BillingPattern { get; set; }

        /// <summary>
        /// 住院日期
        /// </summary>
        public DateTime AdmissionDate { get; set; }

        /// <summary>
        /// 住院时间
        /// </summary>
        public TimeSpan AdmissionTime { get; set; }

        /// <summary>
        /// 出院日期
        /// </summary>
        public DateTime? DischargeDate { get; set; }

        /// <summary>
        /// 出院时间
        /// </summary>
        public TimeSpan? DischargeTime { get; set; }

        /// <summary>
        /// 病人护理程序码  00入院护理评估   10护理诊断  20护理计画  30展出排程   40護理执行
        /// </summary>
        [Column(TypeName = "char(2)")]
        public string NursingProcedureCode { get; set; }

        /// <summary>
        /// 电子病历标记
        /// </summary>
        [Column(TypeName = "char(1)")]
        public string EMRFlag { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int? Age { get; set; }

        /// <summary>
        /// 年龄明细
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string AgeDetail { get; set; }

        /// <summary>
        /// 预交金
        /// </summary>
        public decimal? PrePayments { get; set; }

        /// <summary>
        /// 总费用
        /// </summary>
        public decimal? TotalCharges { get; set; }

        /// <summary>
        /// 同步时间
        /// </summary>
        public DateTime? SyncDataTime { get; set; }

        /// <summary>
        /// 病人主诉
        /// </summary>
        [Column(TypeName = "varchar(500)")]
        public string ChiefComplaint { get; set; }

        /// <summary>
        /// 院内住院号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string LocalCaseNumber { get; set; }

        /// <summary>
        /// 归档标记
        /// </summary>
        [Column(TypeName = "varchar(2)")]
        public string EMRArchivingFlag { get; set; }

        /// <summary>
        /// 患者在院状态。
        /// 10预入院；20在院\入院；30在科\入科；
        /// 40预出院；50出科\不在科；60实际出院；
        /// 70出院未结算；80出院已结算；90出院召回；
        /// （30、40在患者清单显示）
        /// </summary>
        public int? InHospitalStatus { get; set; }
        /// <summary>
        /// 入科时间
        /// </summary>
        public DateTime? AdmWardDateTime { get; set; }
        /// <summary>
        /// 患者类型，1有效患者，2产婴新生儿，3取消入院无费用产生
        /// </summary>
        public byte? InpatientType { get; set; }
    }
}
