﻿using System.ComponentModel;

namespace XianYangInterconnect.ViewModels
{
    /// <summary>
    /// 配置参数()
    /// </summary>
    public class SystemConfig
    {
        /// <summary>
        /// 医院代码
        /// </summary>
        [Description("医院代码")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 语言
        /// </summary>
        [Description("语言")]
        public int Language { get; set; }

        /// <summary>
        /// 环境类别 1:Server(线上环境) 2、LocalHost（本地环境）
        /// </summary>
        [Description("环境类别")]
        public int ServerType { get; set; }
        /// <summary>
        /// 系统使用缓存类型
        /// </summary>
        public string UseCacheType { get; set; }
        /// <summary>
        /// Redis链接
        /// </summary>
        public string RedisConnection { get; set; }
        /// <summary>
        /// 最大同步并发数
        /// </summary>
        public int? MaxSyncConcurrency { get; set; }
        
    }
}