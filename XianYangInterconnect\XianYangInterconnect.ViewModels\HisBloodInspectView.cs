namespace XianYangInterconnect.ViewModels
{
    /// <summary>
    /// HIS血液检查数据视图模型
    /// </summary>
    public class HisBloodInspectView
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public string RecordId { get; set; }

        /// <summary>
        /// 病案号
        /// </summary>
        public string CaseNumber { get; set; }

        /// <summary>
        /// 血袋号
        /// </summary>
        public string BloodNumber { get; set; }

        /// <summary>
        /// 输血监测数据唯一ID
        /// </summary>
        public string SourceUniqueID { get; set; }

        /// <summary>
        /// 执行人ID
        /// </summary>
        public string PerformEmployeeID { get; set; }

        /// <summary>
        /// 执行人姓名
        /// </summary>
        public string PerformEmployeeName { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime PerformDateTime { get; set; }

        /// <summary>
        /// 体温
        /// </summary>
        public string Temperature { get; set; }

        /// <summary>
        /// 心率
        /// </summary>
        public string HeartRate { get; set; }

        /// <summary>
        /// 呼吸
        /// </summary>
        public string Breathing { get; set; }

        /// <summary>
        /// 血压
        /// </summary>
        public string BloodPressure { get; set; }

        /// <summary>
        /// 执行输血巡视时记录护理观察
        /// </summary>
        public string NursingObservation { get; set; }

        /// <summary>
        /// 患者ID
        /// </summary>
        public string PatientId { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// 科室编码
        /// </summary>
        public string DepartmentCode { get; set; }

        /// <summary>
        /// 科室名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 病区编码
        /// </summary>
        public string WardCode { get; set; }

        /// <summary>
        /// 病区名称
        /// </summary>
        public string WardName { get; set; }

        /// <summary>
        /// 床号
        /// </summary>
        public string BedNo { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateDateTime { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyDateTime { get; set; }

        /// <summary>
        /// 删除标记
        /// </summary>
        public string DeleteFlag { get; set; }

        /// <summary>
        /// 有效标记
        /// </summary>
        public string ValidFlag { get; set; }
    }

    /// <summary>
    /// HIS血液检查数据列表响应模型
    /// </summary>
    public class HisBloodInspectListView
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<HisBloodInspectView> Data { get; set; } = new List<HisBloodInspectView>();

        /// <summary>
        /// 总记录数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }
    }

    /// <summary>
    /// HIS血液检查数据API响应根对象
    /// </summary>
    public class HisBloodInspectResponse
    {
        /// <summary>
        /// 响应代码
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public HisBloodInspectListView Data { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success => Code == 200;
    }
}
