﻿using System.Xml;
using System.Xml.Serialization;

namespace XianYangInterconnect.ViewModels.SyncBackHis
{
    [XmlRoot(ElementName = "Envelope", Namespace = "http://www.w3.org/2003/05/soap-envelope")]
    public class SoapEnvelopeView
    {
        [XmlElement(ElementName = "Header", Namespace = "http://www.w3.org/2003/05/soap-envelope")]
        public object Header { get; set; }

        [XmlElement(ElementName = "Body", Namespace = "http://www.w3.org/2003/05/soap-envelope")]
        public SoapBody Body { get; set; }
    }

    public class SoapBody
    {
        [XmlElement(ElementName = "HIPMessageServer", Namespace = "urn:hl7-org:v3")]
        public HIPMessageServer HIPMessageServer { get; set; }
    }

    public class HIPMessageServer
    {
        [XmlElement(ElementName = "action", Namespace = "urn:hl7-org:v3")]
        public string Action { get; set; }

        [XmlElement(ElementName = "message", Namespace = "urn:hl7-org:v3")]
        public CDataWrapper Message { get; set; }
    }
    /// <summary>
    /// <![CDATA[...]]>包装类
    /// </summary>
    public class CDataWrapper : IXmlSerializable
    {
        public string Value { get; set; }

        public System.Xml.Schema.XmlSchema GetSchema() => null;

        public void ReadXml(XmlReader reader)
        {
            Value = reader.ReadElementContentAsString();
        }

        public void WriteXml(XmlWriter writer)
        {
            if (!string.IsNullOrEmpty(Value))
            {
                writer.WriteCData(Value);
            }
        }
    }
}