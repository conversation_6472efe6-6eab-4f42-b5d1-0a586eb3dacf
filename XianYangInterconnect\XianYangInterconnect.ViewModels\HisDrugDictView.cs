﻿namespace XianYangInterconnect.ViewModels
{
    public class DrugItem
    {
        /// <summary>
        /// 药品术语编
        /// </summary>
        public string drugtermId { get; set; }

        /// <summary>
        /// 商品名
        /// </summary>
        public string tradeName { get; set; }

        /// <summary>
        /// 商品名拼音码
        /// </summary>
        public string spellCode { get; set; }

        /// <summary>
        /// 商品名五笔码
        /// </summary>
        public string wbCode { get; set; }

        /// <summary>
        /// 英文品名
        /// </summary>
        public string englishName { get; set; }

        /// <summary>
        /// 通用名
        /// </summary>
        public string regularName { get; set; }

        /// <summary>
        /// 通用名拼音码
        /// </summary>
        public string regularSpellCode { get; set; }

        /// <summary>
        /// 通用名五笔码
        /// </summary>
        public string regularWbCode { get; set; }

        /// <summary>
        /// 英文名
        /// </summary>
        public string englishFormal { get; set; }

        /// <summary>
        /// 英文通用名
        /// </summary>
        public string englishRegular { get; set; }

        /// <summary>
        /// 国家编码
        /// </summary>
        public string gbCode { get; set; }

        /// <summary>
        /// 国际编码
        /// </summary>
        public string internationalCode { get; set; }

        /// <summary>
        /// 生产厂家
        /// </summary>
        public string producerName { get; set; }

        /// <summary>
        /// 药品性质
        /// </summary>
        public string drugQuality { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string specs { get; set; }

        /// <summary>
        /// 参考价格
        /// </summary>
        public string priceRef { get; set; }

        /// <summary>
        /// 剂型
        /// </summary>
        public string doseModel { get; set; }

        /// <summary>
        /// 剂型名称
        /// </summary>
        public string doseModelName { get; set; }

        /// <summary>
        /// 是否需皮试
        /// </summary>
        public string hypoTestFlag { get; set; }

        /// <summary>
        /// 是否皮试剂
        /// </summary>
        public string hypoReagentFlag { get; set; }

        /// <summary>
        /// 皮试剂编码
        /// </summary>
        public string reagentCode { get; set; }

        /// <summary>
        /// 皮试类别编码
        /// </summary>
        public string hypoKindCode { get; set; }

        /// <summary>
        /// 皮试类别名称
        /// </summary>
        public string hypoKindName { get; set; }

        /// <summary>
        /// 药品使用提示
        /// </summary>
        public string useTip { get; set; }

        /// <summary>
        /// 是否抗生素
        /// </summary>
        public string antibioticsFlag { get; set; }

        /// <summary>
        /// 抗生素等级
        /// </summary>
        public string antibioticsLv { get; set; }

        /// <summary>
        /// 与每千克体重换算系数
        /// </summary>
        public string weightFactor { get; set; }

        /// <summary>
        /// 与每平米体表面积换算系数
        /// </summary>
        public string surfaceFactor { get; set; }

        /// <summary>
        /// 剂量单位
        /// </summary>
        public string doseUnit { get; set; }

        /// <summary>
        /// 基本剂量
        /// </summary>
        public string baseDose { get; set; }

        /// <summary>
        /// 备用剂量单位
        /// </summary>
        public string bakDoseUnit { get; set; }

        /// <summary>
        /// 备用基本剂量
        /// </summary>
        public string bakBaseDose { get; set; }

        /// <summary>
        /// 最小单位
        /// </summary>
        public string minUnit { get; set; }

        /// <summary>
        /// 中间单位
        /// </summary>
        public string midUnit { get; set; }

        /// <summary>
        /// 中间数量
        /// </summary>
        public string midQty { get; set; }

        /// <summary>
        /// 包装单位
        /// </summary>
        public string packUnit { get; set; }

        /// <summary>
        /// 包装数量
        /// </summary>
        public string packQty { get; set; }

        /// <summary>
        /// 默认每次量单位等级
        /// </summary>
        public string pefDoseUnitLv { get; set; }

        /// <summary>
        /// 门诊默认取药单位等级
        /// </summary>
        public string putDrugUnitLv { get; set; }

        /// <summary>
        /// 住院默认取药单位等级
        /// </summary>
        public string inDrugUnitLv { get; set; }

        /// <summary>
        /// 默认特殊煎制法编码
        /// </summary>
        public string herbProcCode { get; set; }

        /// <summary>
        /// 默认特殊煎制法名称
        /// </summary>
        public string herbProcName { get; set; }

        /// <summary>
        /// 默认给药途径编码
        /// </summary>
        public string defUsageCode { get; set; }

        /// <summary>
        /// 默认给药途径名称
        /// </summary>
        public string defUsageName { get; set; }

        /// <summary>
        /// 默认频次编码
        /// </summary>
        public string defFreqCode { get; set; }

        /// <summary>
        /// 药理作用编码
        /// </summary>
        public string phaFunCode { get; set; }

        /// <summary>
        /// 药理作用路径
        /// </summary>
        public string phaFunPath { get; set; }

        /// <summary>
        /// 术语类型编码
        /// </summary>
        public string termClassId { get; set; }

        /// <summary>
        /// 术语类型名称
        /// </summary>
        public string termClassName { get; set; }

        /// <summary>
        /// 默认每次计量
        /// </summary>
        public string defOnceDose { get; set; }

        /// <summary>
        /// 最大每次量
        /// </summary>
        public string maxOnceDose { get; set; }

        /// <summary>
        /// 最大天数
        /// </summary>
        public string maxDays { get; set; }

        /// <summary>
        /// 累计天数
        /// </summary>
        public string additiveDays { get; set; }

        /// <summary>
        /// 累计数量
        /// </summary>
        public string additiveQty { get; set; }

        /// <summary>
        /// 每天最大用量
        /// </summary>
        public string maxDayDose { get; set; }

        /// <summary>
        /// 单日最大频次
        /// </summary>
        public string maxFrequency { get; set; }

        /// <summary>
        /// 拆分类型
        /// </summary>
        public string splitType { get; set; }

        /// <summary>
        /// 医保等级
        /// </summary>
        public string siGrade { get; set; }

        /// <summary>
        /// 适应症
        /// </summary>
        public string siMark { get; set; }

        /// <summary>
        /// 是否进口药
        /// </summary>
        public string importFlag { get; set; }

        /// <summary>
        /// 是否协定处方
        /// </summary>
        public string nostrumFlag { get; set; }

        /// <summary>
        /// GMP标志
        /// </summary>
        public string gmpFlag { get; set; }

        /// <summary>
        /// OTC标志
        /// </summary>
        public string otcFlag { get; set; }

        /// <summary>
        /// 大输液标志
        /// </summary>
        public string liquidFlag { get; set; }

        /// <summary>
        /// 贵重药标志
        /// </summary>
        public string preciousFlag { get; set; }

        /// <summary>
        /// 高危药标志
        /// </summary>
        public string dangerFlag { get; set; }

        /// <summary>
        /// 兴奋剂标志
        /// </summary>
        public string stimulantFlag { get; set; }

        /// <summary>
        /// 性别限制用药标志
        /// </summary>
        public string sexClass { get; set; }

        /// <summary>
        /// 儿童用药限制标志
        /// </summary>
        public string childFlag { get; set; }

        /// <summary>
        /// 计划生育药品标志
        /// </summary>
        public string familyplanFlag { get; set; }
    }

    public class DrugDict
    {
        /// <summary>
        /// 数据总数
        /// </summary>
        public int total { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<DrugItem> list { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pageNum { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pageSize { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int size { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int startRow { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int endRow { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pages { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int prePage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int nextPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string isFirstPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string isLastPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string hasPreviousPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string hasNextPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigatePages { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<int> navigatepageNums { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigateFirstPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigateLastPage { get; set; }
    }

    public class HisDrugDictView
    {
        /// <summary>
        /// 
        /// </summary>
        public int code { get; set; }
        /// <summary>
        /// 操作成功！
        /// </summary>
        public string msg { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DrugDict data { get; set; }
    }

}
