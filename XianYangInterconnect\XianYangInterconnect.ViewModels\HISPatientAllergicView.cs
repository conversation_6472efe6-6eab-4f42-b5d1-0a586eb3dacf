﻿namespace XianYangInterconnect.ViewModels
{
    public class HISPatientAllergicView
    {
        /// <summary>
        /// 过敏程度
        /// </summary>
        public string allergyLevelCode { get; set; }

        /// <summary>
        /// 过敏记录流水号
        /// </summary>
        public string allergyRecordId { get; set; }

        /// <summary>
        /// 非药品过敏源
        /// </summary>
        public string allergySource { get; set; }

        /// <summary>
        /// 过敏症状
        /// </summary>
        public string allergySymptom { get; set; }

        /// <summary>
        /// 过敏类型
        /// </summary>
        public string allergyType { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? beginTime { get; set; }

        /// <summary>
        /// 作废时间
        /// </summary>
        public DateTime? cancelDate { get; set; }

        /// <summary>
        /// 作废原因
        /// </summary>
        public string cancelDoc { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? createTime { get; set; }

        /// <summary>
        /// 作废人
        /// </summary>
        public string dcOper { get; set; }

        /// <summary>
        /// 删除原因
        /// </summary>
        public string dcReason { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime? dcTime { get; set; }

        /// <summary>
        /// 过敏药物名
        /// </summary>
        public string drugName { get; set; }

        /// <summary>
        /// 皮试类别编码
        /// </summary>
        public string hypoKindCode { get; set; }

        /// <summary>
        /// 无过敏史标识
        /// </summary>
        public string noneAllergyRecord { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string operDocCode { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime? operTime { get; set; }

        /// <summary>
        /// 患者ID
        /// </summary>
        public string patientId { get; set; }

        /// <summary>
        /// 就诊患者ID
        /// </summary>
        public string pid { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }

        /// <summary>
        /// 有效性标记
        /// </summary>
        public string validFlag { get; set; }

        /// <summary>
        /// 就诊类型
        /// </summary>
        public string visitType { get; set; }
    }
    public class Root
    {
        public int code { get; set; }
        public string msg { get; set; }
        public HISPatientAllergicListView data { get; set; }
    }

    public class HISPatientAllergicListView
    {
        public int total { get; set; }
        public List<HISPatientAllergicView> list { get; set; }
        public int pageNum { get; set; }
        public int pageSize { get; set; }
        public int size { get; set; }
        public int startRow { get; set; }
        public int endRow { get; set; }
        public int pages { get; set; }
        public int prePage { get; set; }
        public int nextPage { get; set; }
        public bool isFirstPage { get; set; }
        public bool isLastPage { get; set; }
        public bool hasPreviousPage { get; set; }
        public bool hasNextPage { get; set; }
        public int navigatePages { get; set; }
        public List<int> navigatepageNums { get; set; }
        public int navigateFirstPage { get; set; }
        public int navigateLastPage { get; set; }
    }
}
