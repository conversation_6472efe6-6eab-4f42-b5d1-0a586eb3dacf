﻿/**
*  2022-04-27 2575 调整计算输液剩余量逻辑,扩展可以泵速的计算 -杨欣欣
*/

namespace XianYangInterconnect.Common
{
    public class GetSurplus
    {
        /// <summary>
        /// 计算输液量
        /// </summary>
        /// <param name="speed">速度</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="patrolType">巡视方式</param>
        /// <returns></returns>
        public static decimal ComputeVolume(decimal speed, DateTime startDate, DateTime endDate, PatrolType patrolType)
        {
            //余量
            var volume = new decimal();
            //计算出间隔了多少分钟
            var time = endDate - startDate;
            var totalMinutes = (int)time.TotalMinutes;

            //泵速: 先转换成每分钟毫升数
            if (patrolType == PatrolType.Pumping)
            {
                //每小时毫升数转换为每分钟毫升数
                decimal speedByMin = speed / 60;
                //计算已输入了多少毫升数
                volume = speedByMin * totalMinutes;
                volume = decimal.Round(volume, 2);
            }

            if (patrolType == PatrolType.Dripping)
            {
                //计算总滴数
                var count = speed * totalMinutes;
                //以20滴 = 1ml转换
                volume = count / 20;
            }

            return volume;
        }
    }
}