﻿using System.Security.Cryptography;
using System.Text;

namespace XianYangInterconnect.Common
{
    public static class CreateMD5
    {
        public static string CreateMD5To32(string str)
        {
            var md5 = MD5.Create();
            byte[] bytes = Encoding.UTF8.GetBytes(str);
            string result = BitConverter.ToString(md5.ComputeHash(bytes));
            return result.Replace("-", "");
        }
    }
}