﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using NLog;
using System.Text;
using XianYangInterconnect.API.DataTransfer;
using XianYangInterconnect.API.Extensions;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Service.Interface.MQ;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.API.Controllers
{
    /// <summary>
    /// 数据获取接口清单
    /// </summary>
    [Produces("application/json")]
    [Route("api/SyncData")]
    [EnableCors("any")]
    public class SyncDataController : Controller
    {

        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISyncDatasLogServices _syncDatasLogServices;
        private readonly IOptions<SystemConfig> _config;
        private readonly SyncCoreService _syncCoreService;
        private readonly IBYJDataToPatientMedicineScheduleService _IBYJDataToPatientMedicineScheduleService;
        private readonly IPatientOperationService _patientOperationService;
        private readonly IExecDrugMQService _mqService;
        private readonly IPatientOrderMQService  _patientOrderMQService;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="syncDatasLogServices"></param>
        /// <param name="options"></param>
        /// <param name="syncCoreService"></param>
        /// <param name="BYJDataToPatientMedicineScheduleService"></param>
        /// <param name="patientOperationService"></param>
        /// <param name="mqService"></param>
        /// <param name="patientOrderMQService"></param>
        public SyncDataController(
            ISyncDatasLogServices syncDatasLogServices,
            IOptions<SystemConfig> options,
            SyncCoreService syncCoreService
            , IBYJDataToPatientMedicineScheduleService BYJDataToPatientMedicineScheduleService
            , IPatientOperationService patientOperationService
            , IExecDrugMQService mqService
            , IPatientOrderMQService patientOrderMQService

            )
        {
            _syncDatasLogServices = syncDatasLogServices;
            _config = options;
            _syncCoreService = syncCoreService;
            _IBYJDataToPatientMedicineScheduleService = BYJDataToPatientMedicineScheduleService;
            _patientOperationService = patientOperationService;
            _mqService = mqService;
            _patientOrderMQService = patientOrderMQService;
        }

        /// <summary>
        /// 获取数据
        /// </summary>
        /// <param name="syncData"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncData")]
        [NoAuthorization]
        public async Task<IActionResult> SyncData([FromBody] object syncData)
        {
            var guid = Guid.NewGuid().ToString("N");

            var result = new ResponseResult();
            if (syncData == null)
            {
                result.Data = false;
                result.Message = "数据为空";
                return result.ToJson();
            }

            result.Data = true;
            result.Message = "success";
            var headers = this.Request.Headers;
            var stringBuilder = new StringBuilder();
            var strHeaders = stringBuilder.ToString();
            _logger.Info("syncData" + syncData);
            var hospitalID = _config.Value.HospitalID;
            var syncDataLogView = SyncListDataTransfer.CreateSyncDataLogView(syncData, hospitalID);

            var resultFlag = await _syncDatasLogServices.SetSyncDataLog(syncDataLogView);
            result.Data = true;
            result.Message = "写入数据成功";
            if (!resultFlag)
            {
                result.Data = false;
                result.Message = "写入数据失败";
            }
            //启动定时任务
            //await _syncCoreService.SyncStartUp(syncDataLogView);
            return result.ToJson();
        }

        /// <summary>
        ///  新增给药执行MQ消息
        /// </summary>
        /// <param name="minute">小于当前时间多少分钟</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SetExecDrugMQ")]
        [DistributedLockFilter("SetExecDrugMQ", LockType.Redis)]
        [NoAuthorization]
        public async Task<IActionResult> SetExecDrugMQ(int minute)
        {
            var result = new ResponseResult
            {
                Data = await _mqService.SetExecDrugMQ(minute)
            };
            return result.ToJson();
        }

        /// <summary>
        ///  新增医嘱审核MQ消息
        /// </summary>
        /// <param name="minute">小于当前时间多少分钟</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SetPatientOrderConfirmMQ")]
        [DistributedLockFilter("SetPatientOrderConfirmMQ", LockType.Redis)]
        [NoAuthorization]
        public async Task<IActionResult> SetPatientOrderConfirmMQ(int minute)
        {
            var result = new ResponseResult
            {
                Data = await _patientOrderMQService.SetPatientOrderConfirmMQ(minute)
            };
            return result.ToJson();
        }


        /// <summary>
        ///  同步摆药机数据
        /// </summary>
        /// <param name="minute">小于当前时间多少分钟</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncBYJData")]
        [DistributedLockFilter("SyncBYJData", LockType.Redis)]
        [NoAuthorization]
        public async Task<IActionResult> SyncBYJData(int minute)
        {
            var result = new ResponseResult
            {
                Data = await _IBYJDataToPatientMedicineScheduleService.SyncBYJData(minute)
            };
            return result.ToJson();
        }


        /// <summary>
        /// 调用医嘱同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncPatientOrder")]
        [DistributedLockFilter("SyncPatientOrder", LockType.Redis)]
        [NoAuthorization]
        public async Task<IActionResult> SyncPatientOrder()
        {
            var result = new ResponseResult
            {
                Data = await _syncCoreService.SyncPatientOrder("8")
            };
            return result.ToJson();
        }
        /// <summary>
        /// 手动调用VTE同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncPatientVTE")]
        [NoAuthorization]
        public async Task<IActionResult> SyncPatientVTE()
        {
            var result = new ResponseResult
            {
                Data = await _syncCoreService.SyncPatientVTE("8")
            };
            return result.ToJson();
        }
        /// <summary>
        /// 手动调用患者同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncInpatientData")]
        [NoAuthorization]
        public async Task<IActionResult> SyncInpatientData()
        {
            var result = new ResponseResult
            {
                Data = await _syncCoreService.SyncInpatientData("8")
            };
            return result.ToJson();
        }
        /// <summary>
        /// 手动调用患者过敏同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncPatientAllergy")]
        [NoAuthorization]
        public async Task<IActionResult> SyncPatientAllergy()
        {
            var result = new ResponseResult
            {
                Data = await _syncCoreService.SyncPatientAllergy("8")
            };
            return result.ToJson();
        }
        /// <summary>
        /// 手动调用患者诊断同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncPatientDiagnosis")]
        [NoAuthorization]
        public async Task<IActionResult> SyncPatientDiagnosis()
        {
            var result = new ResponseResult
            {
                Data = await _syncCoreService.SyncPatientDiagnosis("8")
            };
            return result.ToJson();
        }
        /// <summary>
        /// 手动调用患者检验同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncPatientLab")]
        [NoAuthorization]
        public async Task<IActionResult> SyncPatientLab()
        {
            var result = new ResponseResult
            {
                Data = await _syncCoreService.SyncPatientLab("8")
            };
            return result.ToJson();
        }
        /// <summary>
        /// 手动调用患者主诉同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncPatientMedicalHistory")]
        [NoAuthorization]
        public async Task<IActionResult> SyncPatientMedicalHistory()
        {
            var result = new ResponseResult
            {
                Data = await _syncCoreService.SyncPatientMedicalHistory("8")
            };
            return result.ToJson();
        }
        /// <summary>
        /// 手动调用患者给药同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncPatientMedicine")]
        [NoAuthorization]
        [DistributedLockFilter("SyncPatientMedicine", LockType.Redis)]
        public async Task<IActionResult> SyncPatientMedicine()
        {
            var result = new ResponseResult
            {
                Data = await _syncCoreService.SyncPatientMedicine("8")
            };
            return result.ToJson();
        }
        /// <summary>
        /// 手动调用患者手术同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncPatientOperation")]
        [NoAuthorization]
        public async Task<IActionResult> SyncPatientOperation()
        {
            var result = new ResponseResult
            {
                Data = await _patientOperationService.SyncPatientOperation()
            };
            return result.ToJson();
        }
        /// <summary>
        /// 手动调用患者仪器数据同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncPatientMachine")]
        [NoAuthorization]
        public async Task<IActionResult> SyncPatientMachine()
        {
            var result = new ResponseResult
            {
                Data = await _syncCoreService.SyncPatientMachine("8")
            };
            return result.ToJson();
        }

        /// <summary>
        /// 手动调用患者输血监测数据同步
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncBloodInspectRecordDetail")]
        [NoAuthorization]
        public async Task<IActionResult> SyncBloodInspectRecordDetail()
        {
            var result = new ResponseResult
            {
                Data = await _syncCoreService.SyncBloodInspectRecordDetail()
            };
            return result.ToJson();
        }
    }
}
