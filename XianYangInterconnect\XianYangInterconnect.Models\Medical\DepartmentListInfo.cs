﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XianYangInterconnect.Models.Base;

namespace XianYangInterconnect.Models
{
    /// <summary>
    /// 类DepartmentList。
    /// </summary>
    [Serializable]
    [Table("DepartmentList")]
    public partial class DepartmentListInfo : ModifyInfo
    {
        /// <summary>
        /// 流水号
        /// </summary>
        [Key]
        [Column("DepartmentListID")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }
        /// <summary>
        /// 医疗院所代码
        /// </summary>
        [Column(TypeName = "varchar(2)")]
        public string HospitalID { get; set; }
        /// <summary>
        /// 科别
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string Department { get; set; }
        /// <summary>
        /// 科别代号
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string DepartmentCode { get; set; }
        /// <summary>
        /// 科别类型，0：内科、1：外科、2：专科、3：非临床科室
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string DepartmentPattern { get; set; }

        /// <summary>
        /// 科主任姓名
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string DepartmentDirector { get; set; }

        /// <summary>
        /// P儿科/G妇产/C延续
        /// </summary>
        public string SpecialListType { get; set; }
    }
}
