﻿using System.ComponentModel.DataAnnotations.Schema;

namespace XianYangInterconnect.Models.HIS
{
    [Serializable]
    [Table("VIEW_HL3C_EXECDRUG", Schema = "XYZXHIS")]
    public class ExecDrug
    {
        /// <summary>
        /// 执行单流水号
        /// </summary>
        [Column("EXECSQN", TypeName = "VARCHAR2(16)")]
        public string ExecSqn { get; set; }

        /// <summary>
        /// 药品编码
        /// </summary>
        [Column("DRUGCODE", TypeName = "VARCHAR2(12)")]
        public string DrugCode { get; set; }

        /// <summary>
        /// 医嘱流水号
        /// </summary>
        [Column("MOORDER", TypeName = "VARCHAR2(16)")]
        public string MoOrder { get; set; }

        /// <summary>
        /// 医嘱类别代码
        /// </summary>
        [Column("TYPECODE", TypeName = "VARCHAR2(2)")]
        public string TypeCode { get; set; }

        /// <summary>
        /// 住院流水号
        /// </summary>
        [Column("INPATIENTNO", TypeName = "VARCHAR2(14)")]
        public string InpatientNo { get; set; }

        /// <summary>
        /// 住院病历号
        /// </summary>
        [Column("PATIENTNO", TypeName = "VARCHAR2(10)")]
        public string PatientNo { get; set; }

        /// <summary>
        /// 医嘱护理站代码
        /// </summary>
        [Column("NURSECELLCODE", TypeName = "VARCHAR2(4)")]
        public string NurseCellCode { get; set; }

        /// <summary>
        /// 药品名称
        /// </summary>
        [Column("DRUGNAME", TypeName = "VARCHAR2(100)")]
        public string DrugName { get; set; }

        /// <summary>
        /// 医嘱说明
        /// </summary>
        [Column("MONOTE1", TypeName = "VARCHAR2(80)")]
        public string MoNote1 { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column("MONOTE2", TypeName = "VARCHAR2(500)")]
        public string MoNote2 { get; set; }

        /// <summary>
        /// 频次名称
        /// </summary>
        [Column("FREQUENCYNAME", TypeName = "VARCHAR2(30)")]
        public string FrequencyName { get; set; }

        /// <summary>
        /// 药品基本剂量
        /// </summary>
        [Column("BASEDOSE", TypeName = "NUMBER(10,4)")]
        public decimal BaseDose { get; set; }

        /// <summary>
        /// 用法名称
        /// </summary>
        [Column("USENAME", TypeName = "VARCHAR2(20)")]
        public string UseName { get; set; }

        /// <summary>
        /// 剂量单位
        /// </summary>
        [Column("DOSEUNIT", TypeName = "VARCHAR2(16)")]
        public string DoseUnit { get; set; }

        /// <summary>
        /// 每次剂量
        /// </summary>
        [Column("DOSEONCE", TypeName = "NUMBER(10,4)")]
        public decimal DoseOnce { get; set; }

        /// <summary>
        /// 首日标记
        /// </summary>
        [Column("FIRSTDAYFLAG", TypeName = "CHAR(0)")]
        public string FirstDayFlag { get; set; }

        /// <summary>
        /// 组合序号
        /// </summary>
        [Column("COMBNO", TypeName = "VARCHAR2(14)")]
        public string CombNo { get; set; }

        /// <summary>
        /// 要求执行时间
        /// </summary>
        [Column("USETIME", TypeName = "DATE")]
        public DateTime UseTime { get; set; }

        /// <summary>
        /// 医嘱医师代号
        /// </summary>
        [Column("DOCCODE", TypeName = "VARCHAR2(6)")]
        public string DocCode { get; set; }

        /// <summary>
        /// 作废时间
        /// </summary>
        [Column("VALIDDATE", TypeName = "DATE")]
        public DateTime? ValidDate { get; set; }

        /// <summary>
        /// 有效标志 1有效/0作废
        /// </summary>
        [Column("VALIDFLAG", TypeName = "VARCHAR2(1)")]
        public string ValidFlag { get; set; }

        /// <summary>
        /// 作废人代码
        /// </summary>
        [Column("VALIDUSERCD", TypeName = "VARCHAR2(6)")]
        public string ValidUserCd { get; set; }

        /// <summary>
        /// 药品用量
        /// </summary>
        [Column("QTYTOT", TypeName = "NUMBER(12,4)")]
        public decimal QtyTot { get; set; }

        /// <summary>
        /// 计价单位
        /// </summary>
        [Column("PRICEUNIT", TypeName = "VARCHAR2(16)")]
        public string PriceUnit { get; set; }

        /// <summary>
        /// 高危注记
        /// </summary>
        [Column("HIGHRISKFLAG", TypeName = "CHAR(0)")]
        public string HighRiskFlag { get; set; }

        /// <summary>
        /// 药品禁忌
        /// </summary>
        [Column("DRUGATTENTION", TypeName = "CHAR(0)")]
        public string DrugAttention { get; set; }

        /// <summary>
        /// 滴速/泵速
        /// </summary>
        [Column("SPEED", TypeName = "CHAR(0)")]
        public string Speed { get; set; } // 滴速

        /// <summary>
        /// 药品分类（如普通药品、精神二类、麻醉药品等
        /// </summary>
        [Column("DRUGTYPE", TypeName = "VARCHAR2(2)")]
        public string DrugType { get; set; }

        /// <summary>
        /// 药品规格
        /// </summary>
        [Column("SPECS", TypeName = "VARCHAR2(32)")]
        public string Specs { get; set; }

        /// <summary>
        /// 药品拆分，此次执行药品组号
        /// </summary>
        [Column("BARCODE", TypeName = "VARCHAR2(14)")]
        public string Barcode { get; set; } // 条码

        /// <summary>
        /// 医嘱开立日期
        /// </summary>
        [Column("ORDERSTARTDATETIME", TypeName = "DATE")]
        public DateTime OrderStartDateTime { get; set; }

        /// <summary>
        /// 医嘱停止时间
        /// </summary>
        [Column("ORDERSTOPDATETIME", TypeName = "DATE")]
        public DateTime? OrderStopDateTime { get; set; }

        /// <summary>
        /// 数据来源，1、给药拆分、2、口服药包药机包药数据
        /// </summary>
        [Column("SOURCETYPE", TypeName = "CHAR(1)")]
        public string SourceType { get; set; }

        /// <summary>
        /// 时间戳:记录此条数据的新增或最后修改时间，用于增量抽取，减轻服务器压力
        /// </summary>
        [Column("TIMESTAMP", TypeName = "DATE")]
        public DateTime TimeStamp { get; set; }
    }
}