﻿namespace XianYangInterconnect.ViewModels
{
    /// <summary>
    /// 患者风险评估接口的回传视图模型
    /// </summary>
    public class PatientRiskAssessmentView
    {
        /// <summary>
        /// 医院编码
        /// <para>varchar2(10)</para>
        /// </summary>
        public string HospitalCode { get; set; }
        /// <summary>
        /// 医院名称
        /// <para>varchar2(30)</para>
        /// </summary>
        public string HospitalName { get; set; }
        /// <summary>
        /// 院区编码
        /// <para>varchar2(10)</para>
        /// </summary>
        public string HpAreaCode { get; set; }
        /// <summary>
        /// 院区名称
        /// <para>varchar2(30)</para>
        /// </summary>
        public string HpAreaName { get; set; }
        /// <summary>
        /// 诊疗号
        /// <para>varchar2(14)</para>
        /// </summary>
        public string VisitNo { get; set; }
        /// <summary>
        /// 诊疗流水号
        /// <para>varchar2(20)</para>
        /// </summary>
        public string VisitSqNo { get; set; }
        /// <summary>
        /// 诊疗类型编码
        /// <para>varchar2(10)</para>
        /// </summary>
        public string VisitTypeCode { get; set; }
        /// <summary>
        /// 诊疗类型名称
        /// <para>varchar2(10)</para>
        /// </summary>
        public string VisitTypeName { get; set; }
        /// <summary>
        /// 患者姓名
        /// <para>varchar2(100)</para>
        /// </summary>
        public string PatientName { get; set; }
        /// <summary>
        /// 床号
        /// <para>varchar2(10)</para>
        /// </summary>
        public string BedNo { get; set; }
        /// <summary>
        /// 住院次数
        /// <para>varchar2(10)</para>
        /// </summary>
        public string Times { get; set; }
        /// <summary>
        /// 疼痛风险标识
        /// <para>varchar2(10)</para>
        /// </summary>
        public string PainFlag { get; set; }
        /// <summary>
        /// 体温风险标识
        /// <para>varchar2(10)</para>
        /// </summary>
        public string TemperatureFlag { get; set; }
        /// <summary>
        /// 压疮风险标识
        /// <para>varchar2(10)</para>
        /// </summary>
        public string SoresFlag { get; set; }
        /// <summary>
        /// 跌倒风险标识
        /// <para>varchar2(10)</para>
        /// </summary>
        public string FallFlag { get; set; }
        /// <summary>
        /// 营养风险标识
        /// <para>varchar2(10)</para>
        /// </summary>
        public string NutritionFlag { get; set; }
        /// <summary>
        /// 导管风险标识
        /// <para>varchar2(10)</para>
        /// </summary>
        public string CatheterFlag { get; set; }
        /// <summary>
        /// 预警风险标识
        /// <para>varchar2(10)</para>
        /// </summary>
        public string WarningFlag { get; set; }
        /// <summary>
        /// 隔离风险标识
        /// <para>varchar2(10)</para>
        /// </summary>
        public string QuarantineFlag { get; set; }
        /// <summary>
        /// 评估人编码
        /// <para>varchar2(10)</para>
        /// </summary>
        public string OperCode { get; set; }
        /// <summary>
        /// 评估人名称
        /// <para>varchar2(30)</para>
        /// </summary>
        public string OperName { get; set; }
        /// <summary>
        /// 操作科室编码
        /// <para>varchar2(10)</para>
        /// </summary>
        public string OperDeptCode { get; set; }
        /// <summary>
        /// 操作科室名称
        /// <para>varchar2(30)</para>
        /// </summary>
        public string OperDeptName { get; set; }
        /// <summary>
        /// 风险评估时间
        /// </summary>
        public string OperDate { get; set; }
        /// <summary>
        /// 备注
        /// <para>varchar2(100)</para>
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 跌倒评分
        /// <para>varchar2(10)</para>
        /// </summary>
        public string FallScore { get; set; }
        /// <summary>
        /// 压疮评分
        /// <para>varchar2(10)</para>
        /// </summary>
        public string SoresScore { get; set; }
        /// <summary>
        /// 拔管评分
        /// <para>varchar2(10)</para>
        /// </summary>
        public string CatheterScore { get; set; }
        /// <summary>
        /// 血栓评分
        /// <para>varchar2(10)</para>
        /// </summary>
        public string ThrombusScore { get; set; }
        /// <summary>
        /// 是否有效 1：有效 0：作废
        /// <para>varchar2(1)</para>
        /// </summary>
        public string ValidFlag { get; set; }
    }

}
