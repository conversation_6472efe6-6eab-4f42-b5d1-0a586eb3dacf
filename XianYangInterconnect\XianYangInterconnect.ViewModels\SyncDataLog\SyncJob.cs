﻿
namespace XianYangInterconnect.ViewModels
{
    public class SyncJob
    {
        /// <summary>
        /// 作业编号
        /// </summary>
        public string JobId { get; set; }
        /// <summary>
        /// 作业名称
        /// </summary>
        public string JobName { get; set; }

        /// <summary>
        /// 作业子编号
        /// </summary>
        public string SubJobID { get; set; }

        /// <summary>
        /// 作业开始时间
        /// </summary>
        public DateTime ModifyDateTime { get; set; }

        /// <summary>
        /// 作业状态 1、正在运行。0 停止运行
        /// </summary>
        public int JobStatus { get; set; }
    }
}
