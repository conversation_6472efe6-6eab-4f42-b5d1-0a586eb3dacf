﻿

namespace XianYangInterconnect.ViewModels.Message
{
    /// <summary>
    /// 解析消息表需要的信息
    /// </summary>
    public class MessageMainView
    {
        /// <summary>
        /// 消息流水号
        /// </summary>
        public string MessageID { get; set; }

        /// <summary>
        /// 获取消息时间
        /// </summary>
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 传递的View类型,旧的：(1或为空);新的格式(2)
        /// </summary>
        public string ViewType { get; set; }
        /// <summary>
        /// 消息类别（患者信息、医嘱、手术、给药等）
        /// </summary>
        public string MessageType { get; set; }

        /// <summary>
        /// 事件名称
        /// </summary>
        public string EventName { get; set; }

        /// <summary>
        /// 住院唯一号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 患者唯一号
        /// </summary>
        public string ChartNumber { get; set; }

        /// <summary>
        /// 消息信息
        /// </summary>
        public InpatientView InpatientView { get; set; } 
    }    
}
