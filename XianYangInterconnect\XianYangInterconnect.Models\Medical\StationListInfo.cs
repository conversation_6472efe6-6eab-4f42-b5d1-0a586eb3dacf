﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XianYangInterconnect.Models.Base;

namespace XianYangInterconnect.Models
{
    /// <summary>
    /// 部门信息表
    /// </summary>
    [Table("StationList")]
    public class StationListInfo : ModifyInfo
    {
        /// <summary>
        /// 单位序号
        /// </summary>
        [Key]
        [Column("StationID")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        /// <summary>
        /// 医疗院所代码
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 单位名称
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string StationName { get; set; }

        /// <summary>
        /// 单位代码
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string StationCode { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public short Sort { get; set; }

        /// <summary>
        /// ICU标识
        /// </summary>
        [Column(TypeName = "char(1)")]
        public string ICUFlag { get; set; }

        /// <summary>
        /// 虚拟病区标记
        /// </summary>
        public bool VirtalFlag { get; set; }

        /// <summary>
        /// 派班人员显示条件 a表示單位所有人員  s 表示依上班人員班別
        /// </summary>
        [Column(TypeName = "char(1)")]
        public string AttendanceCondition { get; set; }

        /// <summary>
        /// 评估对应问题点数
        /// </summary>
        public short? AssessToProblemPoint { get; set; }

        /// <summary>
        /// 护士长姓名
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string HeadNurse { get; set; }

        /// <summary>
        /// 统计码(目前仅银川在用)
        /// mzy, 2021-02-06
        /// </summary>
        [Column(TypeName = "varchar(10)")]
        public string StatisticsCode { get; set; }

        /// <summary>
        /// 病区分组
        /// </summary>
        public int? StationGroup { get; set; }

        /// <summary>
        /// 病区类型,0：内科、1：外科、2：专科、3：非临床病区
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string StationPattern { get; set; }
        /// <summary>
        /// 病区简称
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string StationShortName { get; set; }
    }
}
