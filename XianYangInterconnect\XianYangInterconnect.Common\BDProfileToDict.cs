﻿using NLog;

namespace XianYangInterconnect.Common
{
    public static class BDProfileToDict
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        public static List<Dictionary<string, object>> ProfileValueToDict(string settingVale, string profileValue)
        {
            //2021-07-29|(ce)||2021-07-29|(TEST)
            //[4649]|([173])
            //交付[6001302][6001308][6001309][6001310]

            if (profileValue.Length == 0 || string.IsNullOrEmpty(settingVale))
            {
                return null;
            }

            var dicts = new List<Dictionary<string, object>>();

            Dictionary<string, object> dict = null;

            //分割],判断设定有多少内容需要转换
            var settingStrings = settingVale.Split("]");

            //分割||,判断有多少笔数据
            var rowDatas = profileValue.Split("||");

            //表示只有一笔数据
            if (rowDatas.Length == 0)
            {
                dict = GetDict(settingStrings, profileValue);

                if (dict != null)
                {
                    dicts.Add(dict);
                }
            }
            else
            {
                //多笔
                foreach (var item in rowDatas)
                {
                    dict = GetDict(settingStrings, item);

                    if (dict != null)
                    {
                        dicts.Add(dict);
                    }
                }
            }

            if (dicts.Count == 0)
            {
                return null;
            }

            return dicts;
        }

        public static string ProfileToBDValue(string settingVale, string profileValue)
        {
            var data = ProfileValueToDict(settingVale, profileValue);

            if (data == null)
            {
                return "";
            }
            try
            {
                return ListToJson.ToJson(data);
            }
            catch (Exception)
            {
                _logger.Error("Profile数据转换失败,settingVale:" + settingVale + ",profileValue:" + profileValue);

                return "";
            }
        }

        private static Dictionary<string, object> GetDict(string[] settingStrings, string profileValue)
        {
            var dict = new Dictionary<string, object>();

            //切出字段内容
            var data = profileValue.Split("|");

            for (int i = 0; i < settingStrings.Length; i++)
            {
                var temp = settingStrings[i].Split("[");

                foreach (var detail in temp)
                {
                    if (string.IsNullOrEmpty(detail))
                    {
                        continue;
                    }

                    //split字段内容不需要解析,因此跳过
                    if (i >= data.Length)
                    {
                        continue;
                    }

                    if (!int.TryParse(detail, out int id))
                    {
                        data[i].Replace(detail, "");

                        continue;
                    }

                    //过滤配置加上的特殊符号
                    data[i] = data[i].Replace("(", "");

                    data[i] = data[i].Replace(")", "");

                    dict.Add(id.ToString(), data[i]);
                }
            }

            return dict;
        }
    }
}