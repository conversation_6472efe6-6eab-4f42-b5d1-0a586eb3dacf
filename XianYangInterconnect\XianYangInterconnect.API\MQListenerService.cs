﻿using Com.Neusoft.Nhip;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using NLog;
using System.Xml;
using XianYangInterconnect.API.MQService;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.API
{
    public class MQListenerService
    {

        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOptions<SystemConfig> _config;
        public MQListenerService(IOptions<SystemConfig> options)
        {
            _config = options;
        }

        /// <summary>
        /// 启动队列监听,监听患者入院信息
        /// </summary>
        public void ReceiveMessagesAsynchronous()
        {
            try
            {

                // 设定监听队列名及回调函数
                var queueName = "SHZHY_HLCCC_createMedicalVisit";
                //MQProxy.RegisterQueueListener(queueName, (c) => c.ToString());

                MQProxy.RegisterQueueListener(queueName, this.OnMessage);
                MQProxy.RegisterQueueListener("SHZHY_HLCCC_createAdmissions", this.OnMessage);
                // 设定第二个监听的队列
                Listener listener = new Listener();
                MQProxy.RegisterQueueListener(queueName, listener.OnMessage);

                // 启动队列监听
                MQProxy.StartListener();
            }
            catch (Exception ex)
            {
                _logger.Error(ex.Message);
            }

        }
        /// <summary>
        /// 获取本次配置文件测试
        /// </summary>
        /// <returns></returns>
        private string GetCfgValue()
        {
            string text = AppDomain.CurrentDomain.BaseDirectory + "\\MqProxy.cfg.xml";
            if (!File.Exists(text))
            {
                return "";
            }

            XmlDocument xmlDocument = new XmlDocument();
            xmlDocument.Load(text);
            //return SafeGetItemValue(xmlDocument, key, string.Empty);
            return "";
        }
        /// <summary>
        /// 回调方法，获取消息信息
        /// </summary>
        /// <param name="message"></param>
        private void OnMessage(string message)
        {
            System.Threading.Thread.Sleep(3000);
            SavetMessage(message);

        }
        /// <summary>
        /// 保存信息
        /// </summary>
        /// <param name=""></param>
        private void SavetMessage(string message)
        {
            try
            {
                var datetime = DateTime.Now;
                var fileName = datetime.ToString("yyyyMMddHHmmss") + ".txt";
                var filePath = AppDomain.CurrentDomain.BaseDirectory + "\\message\\" + fileName;
                File.WriteAllText(filePath, message);

            }
            catch (Exception ex)
            {
                _logger.Error("SavetMessage错误" + ex.Message);
                throw;
            }
        }

        private static string XmlToJson(string xml)
        {
            if (string.IsNullOrEmpty(xml))
            {
                return "";
            }
            var result = "";
            try
            {
                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xml);
                result = JsonConvert.SerializeXmlNode(doc);
            }
            catch (Exception ex)
            {
                Console.Write("XmlToJson转换失败/r/n" + ex.ToString());
                return "";
            }

            return result;
        }
    }
}
