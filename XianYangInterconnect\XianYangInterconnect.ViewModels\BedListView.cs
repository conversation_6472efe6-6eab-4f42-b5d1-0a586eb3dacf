﻿namespace XianYangInterconnect.ViewModels
{
    public class BedListView
    {
        /// <summary>
        /// 成功码
        /// </summary>
        public int code { get; set; }
        /// <summary>
        /// 信息
        /// </summary>
        public string msg { get; set; }
        /// <summary>
        /// 床位数据
        /// </summary>
        public List<BedData> data { get; set; }
    }
    public class BedData
    {
        /// <summary>
        /// 病床编码
        /// </summary>
        public string bedCode { get; set; }
        /// <summary>
        /// 病室编码
        /// </summary>
        public string deptCode { get; set; }
        /// <summary>
        /// 病室编码
        /// </summary>
        public string roomCode { get; set; }
        /// <summary>
        /// 空床数量
        /// </summary>
        public int roomUbedSum { get; set; }
        /// <summary>
        /// 床位状态
        /// </summary>
        public string bedState { get; set; }
        /// <summary>
        /// 床位价格
        /// </summary>
        public string bedPrice { get; set; }
    }
}
