﻿using System.ComponentModel.DataAnnotations.Schema;
using XianYangInterconnect.Models.Base;

namespace XianYangInterconnect.Models
{
    /// <summary>
    /// 配置字典
    /// </summary>
    [Serializable]
    [Table("SettingDescription")]
    public class SettingDescriptionInfo : ModifyInfo
    {
        /// <summary>
        /// 流水号
        /// </summary>  
        [Column("SettingID")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }
        /// <summary>
        /// 类别
        /// </summary>
        public int SettingType { get; set; }
        /// <summary>
        /// 类别码
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string SettingTypeCode { get; set; }
        /// <summary>
        /// 类别值
        /// </summary>
        [Column(TypeName = "varchar(50)")]
        public string TypeValue { get; set; }
        /// <summary>
        /// 说明
        /// </summary>
        [Column(TypeName = "varchar(100)")]
        public string Description { get; set; }
        /// <summary>
        /// 语言
        /// </summary>
        public int Language { get; set; }
        /// <summary>
        /// 医院编号
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }

        /// <summary>
        /// 类型，S:开关(switch),C:复选框(CheckBox),TN:数值输入框(TextNumber),T:输入框(Text)
        /// </summary>
        [Column(TypeName = "varchar(2)")]
        public string ControlerType { get; set; }
    }
}

