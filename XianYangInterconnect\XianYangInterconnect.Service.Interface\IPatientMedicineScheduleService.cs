﻿namespace XianYangInterconnect.Service.Interface
{
    public interface IPatientMedicineScheduleService
    {
        /// <summary>
        /// 根据病区同步患者用药信息
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        Task<bool> SyncPatientMedicineScheduleByStationID(int? stationID, DateTime? beginTime, DateTime? endTime, string caseNumber);

        /// <summary>
        /// 同步患者用药信息
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<bool> SyncPatientMedicine(string hospitalID);
    }
}
