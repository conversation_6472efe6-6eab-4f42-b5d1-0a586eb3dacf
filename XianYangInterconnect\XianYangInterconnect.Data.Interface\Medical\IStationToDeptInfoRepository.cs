﻿using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Interface
{
    public interface IStationToDeptInfoRepository : ICacheRepository
    {
        /// <summary>
        /// 获取病区科室数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<StationToDepartmentInfo>> GetAsync()
        {
            return (List<StationToDepartmentInfo>)await GetCacheAsync();
        }
    }
}
