﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data
{
    public class SyncDatasLogRepository : ISyncDatasLogRepository
    {
        private DataOutContext _DataOutConnection = null;

        public SyncDatasLogRepository(DataOutContext db)
        {
            _DataOutConnection = db;
        }

        public async Task<List<int>> GetSyncDataByDataType(string hospitalID, string syncDataType)
        {
            return await _DataOutConnection.SyncDataLogInfos.Where(m => m.HospitalID == hospitalID
            && m.SyncDataType == syncDataType && m.DataPumpFlag != "*" && m.Counts <= 2).OrderBy(m => m.AddDate)
            .OrderBy(m => m.ID).Select(m => m.ID).ToListAsync();
        }

        public async Task<List<int>> GetIDsByDataType(string hospitalID, string syncDataType,int minute)
        {
            minute = minute * -1;
            var minAddDate = DateTime.Now.AddMinutes(-minute);
            return await _DataOutConnection.SyncDataLogInfos
                .Where(m =>
                    m.SyncDataType == syncDataType
                   && m.HospitalID == hospitalID
                    && m.DataPumpFlag != "*"
                    && (m.Counts <= 2 || m.AddDate > minAddDate))
                .OrderBy(m => m.AddDate)
            .OrderBy(m => m.ID).Select(m => m.ID).ToListAsync(); 
        }


        public async Task<List<int>> GetSyncDataByTypeAndEvent(string hospitalID, string syncDataType, List<string> eventName)
        {
            return await _DataOutConnection.SyncDataLogInfos.Where(m => m.HospitalID == hospitalID
            && m.SyncDataType == syncDataType && eventName.Contains(m.EventName) && m.DataPumpFlag != "*" && m.Counts <= 2).OrderBy(m => m.AddDate)
            .OrderBy(m => m.ID).Select(m => m.ID).ToListAsync();
        }

        public async Task<SyncDataLogInfo> GetSyncDataByID(int id)
        {
            return await _DataOutConnection.SyncDataLogInfos.Where(m => m.ID == id).FirstOrDefaultAsync();
        }

        public async Task<List<SyncDataLogInfo>> GetSyncDataBySyncDataType(string hospitalID, string syncDataType)
        {
            return await _DataOutConnection.SyncDataLogInfos.Where(m => m.SyncDataType == syncDataType
            && m.HospitalID == hospitalID && m.DataPumpFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取多条需要同步的Json数据
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<Dictionary<int, string>> GetIDToSyncDataByIDs(List<int> ids)
        {
            return await _DataOutConnection.SyncDataLogInfos.AsNoTracking().Where(m => ids.Contains(m.ID))
                .Select(m => new { m.ID, m.SyncData }).ToDictionaryAsync(m => m.ID, m => m.SyncData);
        }
        /// <summary>
        /// 根据事件类别集合获取数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataTypes"></param>
        /// <returns></returns>
        public async Task<List<int>> GetSyncDataByTypeList(string hospitalID, List<string> syncDataTypes)
        {
            return await _DataOutConnection.SyncDataLogInfos.Where(m => m.HospitalID == hospitalID
            && syncDataTypes.Contains(m.SyncDataType) && m.DataPumpFlag != "*" && m.Counts <= 2).OrderBy(m => m.AddDate)
            .OrderBy(m => m.ID).Select(m => m.ID).ToListAsync();
        }
        /// <summary>
        /// 根据数据类型获取数据
        /// </summary>
        /// <param name="hospitalID">医院编码</param>
        /// <param name="syncDataType">同步数据类型</param>
        /// <returns></returns>
        public async Task<List<SyncDataLogInfo>> GetSyncDatasByDataType(string hospitalID, string syncDataType)
        {
            return await _DataOutConnection.SyncDataLogInfos.Where(m => m.HospitalID == hospitalID
            && m.SyncDataType == syncDataType && m.DataPumpFlag != "*" && m.Counts <= 2).OrderBy(m => m.AddDate)
            .OrderBy(m => m.ID).ToListAsync();
        }
        /// <summary>
        /// 根据数据类型获取未抽取数据，或者新增时间在数据
        /// </summary>
        /// <param name="hospitalID">医院编码</param>
        /// <param name="syncDataType">同步数据类型</param>
        /// <returns></returns>
        public async Task<List<SyncDataLogInfo>> GetSyncDatasByDate(string hospitalID, string syncDataType)
        {
            var minAddDate = DateTime.Now.AddMinutes(-3);
            return await _DataOutConnection.SyncDataLogInfos
                .Where(m =>
                    m.SyncDataType == syncDataType
                   && m.HospitalID == hospitalID
                    && m.DataPumpFlag != "*"
                    && (m.Counts <= 2 || m.AddDate > minAddDate ))
                .OrderBy(m => m.AddDate)
                .ThenBy(m => m.ID)
                .ToListAsync();
        }
    }
}