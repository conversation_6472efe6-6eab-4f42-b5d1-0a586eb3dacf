using Hangfire;
using Microsoft.Extensions.Options;
using NLog;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Web;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Data.Interface.Medical;
using XianYangInterconnect.Models;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Services.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    public class InpatientService : IInpatientService
    {
        #region 引用
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IStationListRepository _stationListRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IBedListRepository _bedListRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IRequestApiService _requestApiService;
        private readonly IOptions<SystemConfig> _config;
        private readonly IAPISettingRepository _aPISettingRepository;
        private readonly ISyncDatasLogRepository _syncDatasLogRepository;
        private readonly ISyncDatasLogServices _syncDatasLogServices;
        private readonly ISettingDescriptionRepository _settingDescriptionRepository;
        private readonly IStationGroupListRepository _stationGroupListRepository;
        private readonly IPatientBasicDataRepository _patientBasicDataRepository;
        private readonly IPatientLabservice _patientLabservice;
        #endregion

        #region 常量
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string MODIFY_PERSON = "XianYangInterconnect";
        /// <summary>
        /// 患者状态
        /// </summary>
        private const string STRING_EMPTY = "";
        private static readonly List<string> PATIENT_STATES = new List<string> { "B", "O", "P", "N", "I", "R" };

        /// <summary>
        /// 注册住院患者登记信息
        /// </summary>
        private const string EVENTNAME_II0012 = "II0012_CreateMedicalVisit";
        /// <summary>
        /// 变更住院患者登记信息
        /// </summary>
        private const string EVENTNAME_IU0013 = "IU0013_UpdateMedicalVisit";
        /// <summary>
        /// 注册住院患者接诊信息
        /// </summary>
        private const string EVENTNAME_II0015 = "II0015_CreateAdmissions";
        /// <summary>
        /// 注册患者转科转病区转出信息
        /// </summary>
        private const string EVENTNAME_II0016 = "II0016_CreateTransferDeptOut";
        /// <summary>
        /// 取消患者转科信息
        /// </summary>
        private const string EVENTNAME_ID0017 = "ID0017_DeleteTransferDeptOut";
        /// <summary>
        /// 注册患者转科转病区转入信息
        /// </summary>
        private const string EVENTNAME_II0018 = "II0018_CreateTransferDeptIn";
        /// <summary>
        /// 注册患者床位变更信息
        /// </summary>
        private const string EVENTNAME_II0020 = "II0020_CreateTransferBed";
        /// <summary>
        /// 注册患者医护变更信息
        /// </summary>
        private const string EVENTNAME_II0021 = "II0021_CreateTransferDoctor";
        /// <summary>
        /// 注册患者出院登记信息
        /// </summary>
        private const string EVENTNAME_II0022 = "II0022_createDischargedInfo";
        /// <summary>
        /// 取消患者出院登记信息
        /// </summary>
        private const string EVENTNAME_ID0024 = "ID0024_DeleteDischargedInfo";
        /// <summary>
        /// 无费退院登记信息
        /// </summary>
        private const string EVENTNAME_II0025 = "II0025_CreateNoFeeDischargedInfo";
        /// <summary>
        /// 注册患者诊断信息
        /// </summary>
        private const string EVENTNAME_PI0116 = "PI0116_CreatePatientDiagnosis";
        /// <summary>
        /// 作废患者诊断信息
        /// </summary>
        private const string EVENTNAME_PD0118 = "PD0118_DeletePatientDiagnosis";
        /// <summary>
        /// 注册患者过敏信息
        /// </summary>
        private const string EVENTNAME_PI0119 = "PI0119_createAllergy";
        /// <summary>
        /// 注册患者过敏信息
        /// </summary>
        private const string EVENTNAME_PU0120 = "PU0120_updateAllergy";
        /// <summary>
        /// 注册患者过敏信息
        /// </summary>
        private const string EVENTNAME_PD0121 = "PD0121_deleteAllergy";
        /// <summary>
        /// 注册患者检验信息
        /// </summary>
        private const string SHZHY_HLCCC = "SHZHY_HLCCC_createLabReportEvent";
        /// <summary>
        /// 门诊住院标识
        /// </summary>
        private const string VISIT_TYPE = "2";
        /// <summary>
        /// 产科Code
        /// </summary>
        private const string OBSTETRICS_STATION_CODE = "2104";
        #endregion

        #region 构造器
        public InpatientService(IStationListRepository stationListRepository
            , IAppConfigSettingRepository appConfigSettingRepository
            , IInpatientDataRepository inpatientDataRepository
            , IRequestApiService requestApiService
            , IOptions<SystemConfig> config
            , IAPISettingRepository aPISettingRepository
            , IDepartmentListRepository departmentListRepository
            , IBedListRepository bedListRepository
            , ISyncDatasLogRepository syncDatasLogRepository
            , ISyncDatasLogServices syncDatasLogServices
            , ISettingDescriptionRepository settingDescriptionRepository
            , IStationGroupListRepository stationGroupListRepository
            , IPatientBasicDataRepository patientBasicDataRepository
            , IPatientLabservice patientLabservice
            )
        {
            _stationListRepository = stationListRepository;
            _departmentListRepository = departmentListRepository;
            _bedListRepository = bedListRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _requestApiService = requestApiService;
            _config = config;
            _aPISettingRepository = aPISettingRepository;
            _syncDatasLogRepository = syncDatasLogRepository;
            _syncDatasLogServices = syncDatasLogServices;
            _settingDescriptionRepository = settingDescriptionRepository;
            _stationGroupListRepository = stationGroupListRepository;
            _patientBasicDataRepository = patientBasicDataRepository;
            _patientLabservice = patientLabservice;
        }
        #endregion

        /// <summary>
        /// 同步病人信息
        /// </summary>
        /// <param name="stationCode">不传默认获取所有病区患者</param>
        /// <param name="patientState">患者状态，R-住院登记，I-病房接诊，B-出院登记，O-出院结算，P-预约出院，N-无费退院</param>
        /// <param name="patientState"></param>
        /// <param name="startDate">出院开始时间</param>
        /// <param name="endDate">出院结束时间</param>
        /// <returns></returns>
        public async Task<bool> SyncInPatientByStationCode(string stationCode, string patientState, DateTime? startDate = null, DateTime? endDate = null)
        {
            try
            {
                var hospitalID = _config.Value.HospitalID;
                var api = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetHisPatientInfoAPI");
                if (string.IsNullOrEmpty(api))
                {
                    _logger.Error("GetHisPatientInfoAPI未配置");
                    return false;
                }

                var stationList = await _stationListRepository.GetStationList(hospitalID);
                if (stationList == null || stationList.Count == 0)
                {
                    _logger.Error("未获取到病区信息");
                    return false;
                }

                var stationCodes = string.IsNullOrEmpty(stationCode) ? stationList.Select(x => x.StationCode).ToList() : [stationCode];
                var statusList = !string.IsNullOrEmpty(patientState) ? [patientState] : PATIENT_STATES.ToList();

                foreach (var item in stationCodes)
                {
                    // 执行住院患者同步操作
                    await SyncInPatient(item, statusList, startDate, endDate, api);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步住院病人信息失败: {ex.Message}", ex);
                return false;
            }
        }
        /// <summary>
        /// 同步单病人信息
        /// </summary>
        /// <param name="patientState">患者状态，R-住院登记，I-病房接诊，B-出院登记，O-出院结算，P-预约出院，N-无费退院</param>
        /// <param name="chartNo"></param>
        /// <param name="patientState"></param>
        /// <returns></returns>
        public async Task<bool> SyncInPatientByChartNo(string chartNo, string caseNumber, string patientState)
        {
            try
            {
                var hospitalID = _config.Value.HospitalID;
                var api = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetHisPatientInfoAPI").ConfigureAwait(false);
                if (string.IsNullOrEmpty(api))
                {
                    _logger.Error("API地址未配置");
                    return false;
                }

                var nursingLevelSettingList = await _settingDescriptionRepository.GetBySettingTypeCode("NursingLevel");

                if (!string.IsNullOrEmpty(chartNo))
                {
                    var inpatientDatas = await SyncOneInPatient(chartNo, caseNumber, api, nursingLevelSettingList, patientState);
                    if (inpatientDatas == null || inpatientDatas.Count == 0)
                    {
                        _logger.Warn($"患者信息获取失败: {chartNo}，未获取到相关数据");
                        return false;
                    }
                    // 同步之前获取一次目前已存在的患者流水号
                    var inHospitalCaseNumbers = await _inpatientDataRepository.GetByCaseNumberListAsync(inpatientDatas.Select(m => m.CaseNumber).ToList()).ConfigureAwait(false);
                    await SyncPatientDataByPatientStatus(inpatientDatas, patientState).ConfigureAwait(false);
                    if (patientState == "I")
                    {
                        foreach (var inpatientData in inpatientDatas)
                        {
                            if (!inHospitalCaseNumbers.Contains(inpatientData.CaseNumber))
                            {
                                BackgroundJob.Enqueue(() => _patientLabservice.SyncPatientTest(inpatientData.cardNo, inpatientData.CaseNumber));
                            }
                        }
                        // 同步新生儿患者信息
                        foreach (var inpatientData in inpatientDatas)
                        {
                            if (!inHospitalCaseNumbers.Contains(inpatientData.CaseNumber) && inpatientData.CaseNumber.Contains('B'))
                            {
                                BackgroundJob.Enqueue(() => SyncNewBornDataAsync(inpatientData.CaseNumber, MODIFY_PERSON));
                            }
                        }
                    }
                    return true;
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步住院病人信息失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取单患者信息
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="caseNumber"></param>
        /// <param name="api"></param>
        /// <param name="nursingLevelSettingList"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        private async Task<List<InPatientDataView>> SyncOneInPatient(string chartNo, string caseNumber, string api, List<SettingDescriptionInfo> nursingLevelSettingList, string status = "I")
        {
            string requestParams = $"?patientNo={chartNo}&pageFlag=false";

            var requestApi = api + requestParams;

            // 这里建议采用异步调用并处理结果
            var result = await HttpHelper.HttpGetAsync(requestApi, "application/json");
            var response = ListToJson.ToList<ResponseResult>(result);
            if (response == null || response.Data == null)
            {
                _logger.Warn("患者信息获取失败" + ":" + chartNo + "信息数据失败，未获取到相关数据");
                return null;
            }
            // 对 result 做相应的处理
            var inpatientListView = ListToJson.ToList<InpatientListView>(response.Data.ToString());
            if (inpatientListView == null || inpatientListView.list == null || inpatientListView.list.Count == 0)
            {
                _logger.Warn("患者信息获取失败" + ":" + chartNo + "信息数据失败，未获取到相关数据");
                return null;
            }
            if (!string.IsNullOrEmpty(caseNumber))
            {
                inpatientListView.list = inpatientListView.list.Where(x => x.inpatientNo == caseNumber).ToList();
            }
            var inpatientDatas = CreateInpatientData(inpatientListView.list, status, nursingLevelSettingList);
            return inpatientDatas;
        }
        /// <summary>
        /// 根据状态同步患者信息
        /// </summary>
        /// <param name="inPatientDataViews"></param>
        /// <param name="status"></param>
        /// 患者状态，R-住院登记，I-病房接诊，B-出院登记，O-出院结算，P-预约出院，N-无费退院
        /// <returns></returns>
        private async Task SyncPatientDataByPatientStatus(List<InPatientDataView> inPatientDataViews, string status)
        {
            // 检查状态并调用相应的方法
            switch (status)
            {
                case "I":
                    // 同步当前在院患者
                    await SyncNowStationInpatientData(inPatientDataViews);
                    break;

                case "O":
                case "B":
                    // 同步出院患者
                    await SyncDischargeInpatientData(inPatientDataViews);
                    break;

                case "P":
                    // 同步预出院患者
                    await SyncNowStationInpatientData(inPatientDataViews);
                    break;

                case "N":
                case "R":
                    // 同步无费用出院患者
                    await SyncNoFeeDischargeInpatientData(inPatientDataViews);
                    break;

                // 如果有其他状态，可以继续添加 case
                default:
                    // 如果没有找到匹配的状态，可以考虑抛出异常或记录日志
                    _logger.Warn("未找到匹配的状态" + status);
                    break;
            }
        }

        /// <summary>
        /// 同步当前在院患者
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private async Task SyncNowStationInpatientData(List<InPatientDataView> list)
        {
            await _requestApiService.RequestAPI("SyncInpatient", ListToJson.ToJson(list), null, 300000);
        }

        /// <summary>
        /// 同步出院患者
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private async Task SyncDischargeInpatientData(List<InPatientDataView> list)
        {
            await _requestApiService.RequestAPI("SyncDischargeInpatient", ListToJson.ToJson(list), null, 30000);
        }

        /// <summary>
        /// 同步退院患者
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private async Task SyncNoFeeDischargeInpatientData(List<InPatientDataView> list)
        {
            await _requestApiService.RequestAPI("SyncNoFeeDischargeInpatient", ListToJson.ToJson(list), null, 30000);
        }

        /// <summary>
        /// 根据病区Code获取病人信息(目前只有I和O状态可用)
        /// </summary>
        /// <param name="stationCode"></param>
        /// <param name="statusList">患者状态，R-住院登记，I-病房接诊，B-出院登记，O-出院结算，P-预约出院，N-无费退院</param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="url"></param>
        /// <returns></returns>
        private async Task<bool> SyncInPatient(string stationCode, List<string> statusList, DateTime? startDate, DateTime? endDate, string url)
        {
            var nursingLevelSettingList = await _settingDescriptionRepository.GetBySettingTypeCode("NursingLevel");
            foreach (var status in statusList)
            {
                if (!PATIENT_STATES.Contains(status))
                {
                    continue;
                }

                string requestParams = CreateRequestParams(status, stationCode, startDate, endDate);
                var result = "";
                try
                {
                    result = await HttpHelper.HttpGetAsync(url + requestParams, "application/json");
                    var response = ListToJson.ToList<ResponseResult>(result);

                    if (response == null || response.Data == null)
                    {
                        _logger.Warn($"同步病区编码 {stationCode} 信息数据失败，未获取到相关数据");
                        continue;
                    }

                    var inpatientListView = ListToJson.ToList<InpatientListView>(response.Data.ToString());
                    if (inpatientListView == null || inpatientListView.list == null || inpatientListView.list.Count == 0)
                    {
                        _logger.Warn($"同步病区编码 {stationCode} 信息数据转换失败");
                        continue;
                    }

                    var inPatientDataViews = CreateInpatientData(inpatientListView.list, status, nursingLevelSettingList);
                    // 同步之前获取一次目前已存在的患者流水号
                    var caseNmubers = inPatientDataViews.Select(m => m.CaseNumber).ToList();
                    var inHospitalCaseNumbers = await _inpatientDataRepository.GetByCaseNumberListAsync(caseNmubers);
                    await SyncPatientDataByPatientStatus(inPatientDataViews, status);
                    if (status == "I")
                    {
                        // 同步新入患者检验信息
                        foreach (var inpatientData in inPatientDataViews)
                        {
                            if (!inHospitalCaseNumbers.Contains(inpatientData.CaseNumber))
                            {
                                BackgroundJob.Enqueue(() => _patientLabservice.SyncPatientTest(inpatientData.cardNo, inpatientData.CaseNumber));
                            }
                        }
                        // 同步新生儿患者信息
                        foreach (var inpatientData in inPatientDataViews)
                        {
                            if (!inHospitalCaseNumbers.Contains(inpatientData.CaseNumber)&& inpatientData.CaseNumber.Contains('B'))
                            {
                                BackgroundJob.Enqueue(() => SyncNewBornDataAsync(inpatientData.CaseNumber, MODIFY_PERSON));
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"同步病区编码 {stationCode} 时发生异常: {ex.Message},调用HIS结果result:{result}", ex);
                }
            }
            return true;
        }
        /// <summary>
        /// 组装请求参数
        /// </summary>
        /// <param name="statu"></param>
        /// <param name="stationCode"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        private string CreateRequestParams(string statu, string stationCode, DateTime? startDate, DateTime? endDate)
        {
            string requestParams = $"?patientState={statu}&pageFlag=false";

            if (!string.IsNullOrEmpty(stationCode))
            {
                requestParams += $"&nurseCellCode={stationCode}";
            }
            if (statu == "B" || statu == "O")
            {
                string formattedStartDate = startDate?.ToString("yyyy-MM-dd") ?? DateTime.Now.AddDays(-3).ToString("yyyy-MM-dd HH:mm");
                string formattedEndDate = endDate?.ToString("yyyy-MM-dd") ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm");
                requestParams += $"&outBeginDate={formattedStartDate}&outEndDate={formattedEndDate}";
            }
            return requestParams;
        }

        /// <summary>
        /// 创建住院患者列表
        /// </summary>
        /// <param name="list"></param>
        /// <param name="state"></param>
        /// <param name="nursingLevelSettingList"></param>
        /// <returns></returns>
        private List<InPatientDataView> CreateInpatientData(List<HisInpatientView> list, string status, List<SettingDescriptionInfo> nursingLevelSettingList)
        {
            var inpatientList = new List<InPatientDataView>();

            // 无费退院时,入院时间可能为空
            if (status != "N" && status != "R" && list != null)
            {
                list = list.Where(m => m.inDate.HasValue).ToList();
            }

            // 缓存正则表达式对象
            var babyRegex = new Regex(@"^B(\d)");
            var ageRegex = new Regex(@"(\d+)(?=岁)");

            foreach (var item in list)
            {
                var inpatient = new InPatientDataView()
                {
                    CaseNumber = item.inpatientNo,
                    ChartNo = item.patientNo,
                    IdentityID = item.idenno,
                    NumberOfAdmissions = item.inTimes,
                    Department = item.deptName,
                    DepartmentCode = item.deptCode,
                    StationName = item.nurseCellName,
                    StationCode = item.nurseCellCode,
                    BedNumber = item.bedNo,
                    BedCode = item.bedNo,
                    Diagnosis = item.mainDiagnose,
                    AttendingPhysicianID = item.directorDocCode,
                    BillingPattern = item.payKindName,
                    PatientName = item.patientName,
                    Gender = (item.sexCode == "F") ? "女" : (item.sexCode == "M" ? "男" : null),
                    DateOfBirth = item.birthday ?? DateTime.MinValue,
                    NativePlace = item.linkmanAdd,
                    ChiefComplaint = item.chiefComplaint,
                    EnterWardTime = item.inDate,
                    AdmissionDateTime = item.inDate ?? DateTime.Now,
                    cardNo = item.cardNo,
                };

                // 处理出院时间和死亡时间
                if (item.deathTime.HasValue)
                {
                    inpatient.DeathTime = item.deathTime.Value;
                    inpatient.DischargeDateTime = item.deathTime.Value;
                }
                else
                {
                    var dischargeDateTime = GetDischargeDateAndTime(item.outDate);
                    inpatient.DischargeDateTime = dischargeDateTime;
                }

                // 处理住院状态
                switch (status)
                {
                    case "I":
                        inpatient.InHospitalStatus = 30;
                        break;
                    case "O":
                    case "B":
                        inpatient.InHospitalStatus = 60;
                        break;
                    case "P":
                        inpatient.InHospitalStatus = 40;
                        break;
                    case "N":
                    case "R":
                        inpatient.InHospitalStatus = 0;
                        break;
                    default:
                        inpatient.InHospitalStatus = -1; // 处理未知状态
                        break;
                }

                // 判断住院号是否包含字母"B",字母"B"表示为新生儿
                if (item.patientNo.Contains('B'))
                {
                    var babyMatch = babyRegex.Match(item.patientNo);

                    if (babyMatch.Success && int.TryParse(babyMatch.Groups[1].Value, out int babySN))
                    {
                        inpatient.BabySN = babySN;
                        inpatient.BedNumber = $"{item.bedNo}_{babySN}";

                        // 替换B和B后面的数字
                        if (inpatient.CaseNumber.IndexOf('B') is int bIndex && bIndex + 1 < inpatient.CaseNumber.Length)
                        {
                            inpatient.ParentCaseNumber = inpatient.CaseNumber.Substring(0, bIndex) + '0' + inpatient.CaseNumber.Substring(bIndex + 2);
                        }
                    }
                }

                // 处理年龄
                var ageMatch = ageRegex.Match(item.age);
                inpatient.Age = ageMatch.Success ? int.Parse(ageMatch.Value) : 0;
                inpatient.AgeDetail = item.age;

                // 获取护理级别
                var nursingLevel = nursingLevelSettingList.FirstOrDefault(m => m.Description == item.nursingLevel);
                if (nursingLevel != null)
                {
                    inpatient.NursingLevel = nursingLevel.TypeValue;
                    inpatient.NursingLevelCode = nursingLevel.TypeValue;
                }

                inpatientList.Add(inpatient);
            }

            return inpatientList;
        }
        /// <summary>
        /// 获取出院时间
        /// </summary>
        /// <param name="outDate"></param>
        /// <returns></returns>
        public DateTime? GetDischargeDateAndTime(DateTime? outDate)
        {
            return (outDate.HasValue && IsValidDate(outDate.Value)) ? outDate : null;
        }

        private bool IsValidDate(DateTime date)
        {
            // 判断日期是否超出正常范围
            return date.Year >= 1900; // 可以根据业务需求调整年份下限
        }

        /// <summary>
        /// 获取单个患者主诉信息
        /// </summary>
        /// <param name="visitNo">用户流水号</param>
        /// <param name="visitType">门诊住院标识</param>
        /// <returns></returns>
        public async Task<bool> GetSingleInpatientChiefComplaintAsync(string visitNo, string visitType)
        {
            if (string.IsNullOrEmpty(visitType))
            {
                visitType = VISIT_TYPE;
            }
            if (string.IsNullOrEmpty(visitNo))
            {
                _logger.Warn($"获取单个患者主诉信息参数错误,visitNo = {visitNo},visitType = {visitType}");
                return false;
            }
            //获取HIS数据配置
            var url = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetInpatientChief");
            //存储数据的配置
            var responseUrlView = await _aPISettingRepository.GetAPIAddress("SyncInpatientChief");
            //url = "https://localhost:1099/api/8/InpatientList/GetSingleInpatientChiefComplaint";
            //responseUrlView = new ApiUrlView { ApiUrl = "https://localhost:62288/api/SyncInpatient/SyncInpatientChiefComplaint" };
            if (string.IsNullOrEmpty(url) || responseUrlView == null || string.IsNullOrEmpty(responseUrlView.ApiUrl))
            {
                _logger.Error($"获取链接配置失败");
                return false;
            }
            var urlParams = $"{url}?visitNo={visitNo}&visitType={visitType}";
            string hisResult = await HttpHelper.HttpGetAsync(urlParams);
            var result = ListToJson.ToList<ChiefComplaintView>(hisResult);
            if (result == null || result.data.Count == 0)
            {
                _logger.Warn("同步床位信息数据失败，未获取到相关数据");
                return false;
            }
            //数据虽然是集合但是每次只有一条,所以取第一条
            HttpHelper.SendObjectAsJsonInBody(responseUrlView.ApiUrl, new StringKeyValueView { Key = result.data.FirstOrDefault().patientId, Value = result.data.FirstOrDefault().chiefValue });
            return true;
        }

        /// <summary>
        /// 同步患者过敏数据
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientAllergic(string chartNo)
        {
            var hospitalID = _config.Value.HospitalID;
            var chartNoList = new List<string>();
            if (string.IsNullOrEmpty(chartNo))
            {
                chartNoList = await _inpatientDataRepository.GetCaseNumbers(hospitalID);
            }
            else
            {
                chartNoList.Add(chartNo);
            }
            foreach (var chartNoItem in chartNoList)
            {
                await SyncPatientAllergicByPatientId(chartNoItem);
            }
            return true;
        }
        /// <summary>
        /// 同步过敏数据
        /// </summary>
        /// <param name="chartNoItem"></param>
        /// <returns></returns>
        private async Task SyncPatientAllergicByPatientId(string chartNoItem)
        {
            var api = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetPatientAllergicAPI");
            //api = "http://************/emr-service/api/v1/past/drug-allergy/query";
            //api = "https://localhost:1056/api/8/Patient/GetPatientAllergic";
            string requestParams = $"/{chartNoItem}?pageFlag=false";
            var requestApi = api + requestParams;
            var result = await HttpHelper.HttpGetAsync(requestApi, "application/json");
            var response = ListToJson.ToList<ResponseResult>(result);
            if (response == null || response.Data == null)
            {
                _logger.Warn("同步病案号:" + chartNoItem + "过敏信息数据失败，未获取到相关数据");
                return;
            }
            // 对 result 做相应的处理
            var HISPatientAllergicList = ListToJson.ToList<HISPatientAllergicListView>(response.Data.ToString());
            if (HISPatientAllergicList == null || HISPatientAllergicList.list == null || HISPatientAllergicList.list.Count == 0)
            {
                _logger.Warn("同步病案号:" + chartNoItem + "过敏信息数据失败");
                return;
            }
            var list = new List<PatientAllergicView>();
            foreach (var item in HISPatientAllergicList.list)
            {
                var patientAllergicView = new PatientAllergicView
                {
                    // 基本信息赋值
                    AllergyRecordID = item.allergyRecordId, // 过敏药物ID
                    ChartNo = chartNoItem,                      // 患者ID
                    AllergenName = item.drugName,           // 过敏药物名
                    AllergyType = item.allergyType,         // 过敏类型
                    DrugCode = item.hypoKindCode,           // 皮试类别编码
                    BeginTime = item.beginTime,             // 开始时间
                    CancelDate = item.cancelDate,           // 作废时间
                    CancelReason = item.cancelDoc,          // 作废原因
                    AddPerson = item.operDocCode,           // 操作人
                    CreateTime = item.createTime,           // 创建时间
                    DeletePerson = item.dcOper,             // 作废人
                    DeleteReason = item.dcReason,           // 删除原因
                    DeleteTime = item.dcTime,               // 删除时间
                    CaseNumber = item.pid,                  // 就诊患者ID
                    Remark = item.remark,                   // 备注
                    ValidFlag = item.validFlag == "1" ? true : item.validFlag == "0" ? false : (bool?)null,             // 有效性标记
                };
                list.Add(patientAllergicView);
            }
            await _requestApiService.RequestAPI("SyncPatientAllergic", ListToJson.ToJson(list), null);
        }
        /// <summary>
        /// 根据住院号获取病人诊断信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientDiagnosis(string caseNumber)
        {
            var hospitalID = _config.Value.HospitalID;
            var list = new List<string>();
            var api = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetPatientDiagnosisAPI");
            //api = "http://10.10.12.208:7801/roc/emr-service/api/v1/diagnose/query";
            if (string.IsNullOrEmpty(caseNumber))
            {
                list = await _inpatientDataRepository.GetCaseNumbers(hospitalID);
            }
            else
            {
                list.Add(caseNumber);
            }
            var inpatientDataList = await _inpatientDataRepository.GetInpatientListByCaseNumberListAsync(list);
            foreach (var caseNum in list)
            {
                var inpatientData = inpatientDataList.Where(m => m.CaseNumber == caseNum).FirstOrDefault();
                if (inpatientData == null)
                {
                    _logger.Info("住院号:" + caseNum + "没有获取到住院信息");
                    continue;
                }
                var cardNo = await GetHISPatientCardNo(inpatientData.ChartNo, inpatientData.CaseNumber);
                if (cardNo == null)
                {
                    continue;
                }
                string requestParams = $"?clinciNo={inpatientData.CaseNumber}&patientId={cardNo}";
                var requestApi = api + requestParams;
                var result = await HttpHelper.HttpGetAsync(requestApi, "application/json");
                var response = new ResponseResult();
                try
                {
                    response = ListToJson.ToList<ResponseResult>(result);
                    if (response == null || response.Data == null)
                    {
                        _logger.Warn("同步病案号" + caseNum + "诊断信息数据失败，未获取到相关数据");
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Warn($"解析诊断接口返回数据失败，错误信息：{result}" + ex.ToString());
                    continue;
                }
                // 对 result 做相应的处理
                var patientDiagnosis = new HisDiagnosisView();
                try
                {
                    patientDiagnosis = ListToJson.ToList<HisDiagnosisView>(response.Data.ToString());
                    if (patientDiagnosis == null || patientDiagnosis.list == null || patientDiagnosis.list.Count <= 0)
                    {
                        _logger.Warn("同步病案号" + caseNum + "诊断信息数据转换失败");
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Warn($"解析诊断明细数据失败，错误信息：{patientDiagnosis}" + ex.ToString());
                    continue;
                }
                var patientDiagnosisList = CreatePatientDiagnosisItemViewList(patientDiagnosis.list, inpatientData);
                await _requestApiService.RequestAPI("SyncpatientDiagnosis", ListToJson.ToJson(patientDiagnosisList), null, 300);
            }
            return true;
        }
        /// <summary>
        /// 生成患者诊断信息
        /// </summary>
        /// <param name="patientDiagnosis"></param>
        /// <param name="inpatientData"></param>
        /// <returns></returns>
        private List<PatientDiagnosisItemView> CreatePatientDiagnosisItemViewList(List<PateintDiagnosisItem> patientDiagnosis, InpatientDataInfo inpatientData)
        {
            patientDiagnosis = patientDiagnosis.Where(m => m.visitType == "1" && m.validFlag == "1" && m.clinicNo == inpatientData.CaseNumber).ToList();
            if (patientDiagnosis.Count <= 0)
            {
                return [];
            }
            var list = new List<PatientDiagnosisItemView>();
            int sort = 1;
            foreach (var hisItem in patientDiagnosis)
            {
                var patientDiagnosisInfo = new PatientDiagnosisItemView()
                {
                    InpatientID = inpatientData.ID,
                    PatientID = inpatientData.PatientID,
                    StationID = inpatientData.StationID,
                    DepartmentListID = inpatientData.DepartmentListID,
                    CaseNumber = inpatientData.CaseNumber,
                    ChartNo = inpatientData.ChartNo,
                    DiagnosisCode = hisItem.icd10Code ?? "",
                    ICDCode = hisItem.icd10Code,
                    DiagnosisName = hisItem.icd10Name ?? "",
                    DiagnosisType = hisItem.mainDiagnoseFlag == "1" ? "A" : "P",
                    DiagnosisHISNote = "",
                    Sort = sort,
                    DoctorNo = "",
                    MainFlag = hisItem.mainDiagnoseFlag == "1" ? "*" : ""
                };
                patientDiagnosisInfo.DiagnosisType = hisItem.diseaseTypeCode switch
                {
                    "5504" => "A",
                    "5502" => "D",
                    _ => "P",// 默认值，可以根据需求调整
                };
                if (patientDiagnosisInfo.Sort == 0)
                {
                    patientDiagnosisInfo.MainFlag = "*";
                }
                if (inpatientData.DischargeDate.HasValue && inpatientData.DischargeTime.HasValue)
                {
                    patientDiagnosisInfo.OutTime = inpatientData.DischargeDate.Value.Add(inpatientData.DischargeTime.Value);
                }
                var time = DateTime.Now;
                patientDiagnosisInfo.DiagnosisTime = time;
                patientDiagnosisInfo.AddDate = time;
                patientDiagnosisInfo.AddEmployeeID = MODIFY_PERSON;
                patientDiagnosisInfo.ModifyDate = time;
                patientDiagnosisInfo.ModifyPersonID = MODIFY_PERSON;
                sort++;
                list.Add(patientDiagnosisInfo);
            }
            return list;
        }

        /// <summary>
        /// 根据病案号同步出院患者信息
        /// </summary>
        /// <param name="localCaseNumber"></param>
        /// <param name="chartNo"></param>
        /// <param name="caseNumber"></param>
        /// <param name="patientState"></param>
        /// <returns></returns>
        public async Task<bool> SyncDischargeInPatientByCaseNumber(string localCaseNumber, string chartNo, string caseNumber, string patientState)
        {
            var hospitalID = _config.Value.HospitalID;
            var api = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetHisPatientInfoAPI");
            //api = "http://************/curr-service/api/v1/common/in-patient/query"; // 硬编码的 API 地址
            var nursingLevelSettingList = await _settingDescriptionRepository.GetBySettingTypeCode("NursingLevel");
            // 如果本地病例号或病历号不为空，直接同步单个住院病人
            var inpatientDatas = new List<InpatientDataInfo>();
            if (string.IsNullOrEmpty(caseNumber) && string.IsNullOrEmpty(chartNo))
            {
                inpatientDatas = await _inpatientDataRepository.GetAllInpatientDataAsync(hospitalID);
            }
            else
            {
                if (!string.IsNullOrEmpty(caseNumber))
                {
                    inpatientDatas.Add(await _inpatientDataRepository.GetInpatientDataInfoAsync(caseNumber));
                }

                if (!string.IsNullOrEmpty(chartNo))
                {
                    inpatientDatas.AddRange(await _inpatientDataRepository.GetInpatientDataInfoByChartNoAsync(chartNo));
                }
            }
            var inpatientViewDatas = new List<InPatientDataView>();
            foreach (var inpatientData in inpatientDatas)
            {
                var inpatientViews = await SyncOneInPatient(inpatientData.ChartNo, inpatientData.CaseNumber, api, nursingLevelSettingList, patientState);
                if (inpatientViews != null && inpatientViews.Count > 0)
                {
                    inpatientViewDatas.AddRange(inpatientViews);
                }
            }
            if (inpatientViewDatas.Count > 0)
            {
                await SyncPatientDataByPatientStatus(inpatientViewDatas, patientState);
            }
            return true;
        }

        /// <summary>
        /// 同步患者基本信息(MQ消息队列保存调用)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        public async Task<bool> SyncInpatientData(string hospitalID)
        {
            MessageView messageView;
            bool result = false;
            var logInfos = await _syncDatasLogRepository.GetSyncDatasByDate(hospitalID, "Inpatient");
            foreach (var logInfo in logInfos)
            {

                messageView = ListToJson.ToList<MessageView>(logInfo.SyncData);
                if (messageView == null || messageView.InpatientView == null)
                {
                    await _syncDatasLogServices.ModifySyncDataLog(logInfo.ID, result);
                    continue;
                }
                _logger.Info("MQ发来消息,现在开始同步住院号为:" + messageView.InpatientView.CaseNumber + "的住院信息");
                var inpatientView = messageView.InpatientView;
                result = await SyncPatientByEventName(messageView.InpatientTransferView, hospitalID, inpatientView.ChartNumber, inpatientView.CaseNumber, inpatientView.OutpatientID, messageView.EventName);
                await _syncDatasLogServices.ModifySyncDataLog(logInfo.ID, result);
            }
            return result;
        }
        /// <summary>
        /// 同步患者诊断信息(MQ消息队列保存调用)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientDiagnosisData(string hospitalID)
        {
            SyncDataLogInfo logInfo;
            MessageView messageView;
            bool result = false;
            var logIDs = await _syncDatasLogRepository.GetSyncDataByDataType(hospitalID, "PatientDiagnosis");
            foreach (var logID in logIDs)
            {
                logInfo = await _syncDatasLogRepository.GetSyncDataByID(logID);
                if (logInfo == null)
                {
                    continue;
                }
                messageView = ListToJson.ToList<MessageView>(logInfo.SyncData);
                if (messageView == null || messageView.InpatientView == null)
                {
                    await _syncDatasLogServices.ModifySyncDataLog(logID, result);
                    continue;
                }
                _logger.Info("MQ发来消息,现在开始同步住院号为:" + messageView.InpatientView.CaseNumber + "的诊断信息");
                var inpatientView = messageView.InpatientView;
                result = await SyncPatientDiagnosis(inpatientView.CaseNumber);
                await _syncDatasLogServices.ModifySyncDataLog(logID, result);
            }
            return true;
        }
        /// <summary>
        /// 同步患者过敏信息(MQ消息队列保存调用)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientAllergy(string hospitalID)
        {
            SyncDataLogInfo logInfo;
            MessageView messageView;
            bool result = false;
            var logIDs = await _syncDatasLogRepository.GetSyncDataByDataType(hospitalID, "PatientAllergy");
            foreach (var logID in logIDs)
            {
                logInfo = await _syncDatasLogRepository.GetSyncDataByID(logID);
                if (logInfo == null)
                {
                    continue;
                }
                messageView = ListToJson.ToList<MessageView>(logInfo.SyncData);
                if (messageView == null || messageView.InpatientView == null)
                {
                    await _syncDatasLogServices.ModifySyncDataLog(logID, result);
                    continue;
                }
                _logger.Info("MQ发来消息,现在开始同步住院号为:" + messageView.InpatientView.CaseNumber + "的住院信息");
                var inpatientView = messageView.InpatientView;
                result = await SyncPatientAllergic(inpatientView.ChartNumber);
                await _syncDatasLogServices.ModifySyncDataLog(logID, result);
            }
            return true;
        }

        /// <summary>
        /// 根据serviceType同步患者信息
        /// </summary>
        /// <param name="inpatientTransferView"></param>
        /// <param name="hospitalID"></param>
        /// <param name="chartNumber"></param>
        /// <param name="caseNumber"></param>
        /// <param name="outPatientID"></param>
        /// <param name="serviceType"></param>
        /// 患者状态，R-住院登记，I-病房接诊，B-出院登记，O-出院结算，P-预约出院，N-无费退院
        /// <returns></returns>
        public async Task<bool> SyncPatientByEventName(InpatientTransferView inpatientTransferView, string hospitalID, string chartNumber, string caseNumber, string outPatientID, string serviceType)
        {
            bool flag = false;

            try
            {
                switch (serviceType)
                {
                    // 住院信息相关事件
                    case EVENTNAME_II0012:
                    case EVENTNAME_IU0013:
                    case EVENTNAME_II0015:
                    case EVENTNAME_II0020:
                    case EVENTNAME_II0021:
                    case EVENTNAME_ID0024:
                        flag = await SyncInPatientByChartNo(chartNumber, caseNumber, "I");
                        break;

                    // 转科信息事件
                    case EVENTNAME_II0016:
                        flag = await SyncTransferDept(caseNumber, inpatientTransferView, "Out", hospitalID, true);
                        flag = await SyncInPatientByChartNo(chartNumber, caseNumber, "I");
                        break;
                    //转入信息时间
                    case EVENTNAME_II0018:
                        flag = await SyncTransferDept(caseNumber, inpatientTransferView, "In", hospitalID, true);
                        flag = await SyncInPatientByChartNo(chartNumber, caseNumber, "I");
                        break;
                    //取消转科
                    case EVENTNAME_ID0017:
                        flag = await SyncTransferDept(caseNumber, inpatientTransferView, "Out", hospitalID, false);
                        flag = await SyncInPatientByChartNo(chartNumber, caseNumber, "I");
                        break;

                    // 出院登记信息事件
                    case EVENTNAME_II0022:
                        flag = await SyncOutPatientEvents(chartNumber, caseNumber);
                        break;

                    // 无费退院事件
                    case EVENTNAME_II0025:
                        flag = await SyncInPatientByChartNo(chartNumber, caseNumber, "N");
                        break;

                    // 未匹配的事件类型
                    default:
                        flag = false;
                        break;
                }
            }
            catch (Exception ex)
            {
                flag = false;
                _logger.Error("患者信息更新失败, 事件类型: " + serviceType, ex.ToString());
            }

            return flag;
        }

        /// <summary>
        /// 处理出院事件的逻辑
        /// </summary>
        /// <param name="chartNumber"></param>
        /// <param name="caseNumber"></param>
        /// 患者状态，R-住院登记，I-病房接诊，B-出院登记，O-出院结算，P-预约出院，N-无费退院
        /// <returns></returns>
        private async Task<bool> SyncOutPatientEvents(string chartNumber, string caseNumber)
        {
            try
            {
                await SyncInPatientByChartNo(chartNumber, caseNumber, "B");
                await SyncInPatientByChartNo(chartNumber, caseNumber, "O");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("出院事件处理失败", ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 同步患者主诉
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientMedicalHistory(string hospitalID)
        {
            SyncDataLogInfo logInfo;
            MessageView messageView;
            bool result = false;
            var logIDs = await _syncDatasLogRepository.GetSyncDataByDataType(_config.Value.HospitalID, "PatientMedicalHistory");
            foreach (var logID in logIDs)
            {
                logInfo = await _syncDatasLogRepository.GetSyncDataByID(logID);
                if (logInfo == null)
                {
                    await _syncDatasLogServices.ModifySyncDataLog(logID, result);
                    continue;
                }
                messageView = ListToJson.ToList<MessageView>(logInfo.SyncData);
                if (messageView == null || messageView.InpatientView == null)
                {
                    await _syncDatasLogServices.ModifySyncDataLog(logID, result);
                    continue;
                }
                result = await GetSingleInpatientChiefComplaintAsync(messageView.InpatientView.CaseNumber, VISIT_TYPE);
                await _syncDatasLogServices.ModifySyncDataLog(logID, result);
            }
            return true;
        }

        /// <summary>
        /// 同步患者入出转信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="inpatientTransferView">转出科室</param>
        /// <param name="eventType">事件类型(转出/转入)</param>
        /// <param name="hospitalID"></param>
        /// <param name="isTransfer">是否转科(ture转科,false 撤销转科)</param>
        /// <returns></returns>
        public async Task<bool> SyncTransferDept(string caseNumber, InpatientTransferView inpatientTransferView, string eventType, string hospitalID, bool isTransfer)
        {
            _logger.Info("开始同步患者" + caseNumber + "的" + eventType + "转科信息");

            var stationList = await _stationListRepository.GetStationList(hospitalID);
            var departmentList = await _departmentListRepository.GetDepartmentList(hospitalID);
            var bedList = await _bedListRepository.GetBedList(hospitalID);

            var inpatientData = await _inpatientDataRepository.GetInpatientDataInfo(caseNumber);
            if (inpatientData == null)
            {
                _logger.Warn("同步患者入出转信息失败, 患者住院号:" + caseNumber + "没有获取到病人住院信息");
                return false;
            }

            // 判断转科信息是否有效
            if (!ValidateTransferInfo(caseNumber, inpatientTransferView, eventType, departmentList, stationList, bedList))
            {
                return false;
            }

            // 创建转科实体信息
            var inpatientTransferDataView = await CreateTransferDataView(caseNumber, inpatientTransferView, eventType, departmentList, stationList, bedList);
            if (inpatientTransferDataView == null)
            {
                _logger.Warn("创建患者转科信息失败,创建inpatientTransferDataView实体类失败");
                return false;
            }

            // 同步转科信息(判断是转科还是取消)
            if (isTransfer)
            {
                return await SyncInpatientTransferDept(inpatientTransferDataView);
            }
            else
            {
                return await SyncCancelInpatientTransferDept(inpatientTransferDataView);
            }
        }
        /// <summary>
        /// 判断转科信息是否有效
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="inpatientTransferView"></param>
        /// <param name="eventType"></param>
        /// <param name="departmentList"></param>
        /// <param name="stationList"></param>
        /// <param name="bedList"></param>
        /// <returns></returns>
        private bool ValidateTransferInfo(string caseNumber, InpatientTransferView inpatientTransferView, string eventType,
         List<DepartmentListInfo> departmentList, List<StationListInfo> stationList, List<BedListInfo> bedList)
        {
            string departmentCode = eventType == "Out" ? inpatientTransferView.TransferOutDepartmentCode : inpatientTransferView.TransferInDepartmentCode;
            string stationCode = eventType == "Out" ? inpatientTransferView.TransferOutStationCode : inpatientTransferView.TransferInStationCode;
            string bedCode = eventType == "Out" ? inpatientTransferView.TransferOutBedCode : inpatientTransferView.TransferInBedCode;
            DateTime? transferDatetime = eventType == "Out" ? inpatientTransferView.TransferOutDatatime : inpatientTransferView.TransferInDatatime;

            var department = departmentList.FirstOrDefault(m => m.DepartmentCode == departmentCode);
            if (department == null)
            {
                _logger.Warn($"患者住院号:{caseNumber}没有获取到{(eventType == "Out" ? "转出" : "转入")}科室信息");
                return false;
            }

            var station = stationList.FirstOrDefault(m => m.StationCode == stationCode);
            if (station == null)
            {
                _logger.Warn($"患者住院号:{caseNumber}没有获取到{(eventType == "Out" ? "转出" : "转入")}病区信息");
                return false;
            }

            if (!transferDatetime.HasValue || transferDatetime.Value.Year < 1901)
            {
                _logger.Warn($"患者住院号:{caseNumber}{(eventType == "Out" ? "转出" : "转入")}日期不符合规范,日期为:{transferDatetime.ToString()}");
                return false;
            }

            var bed = bedList.FirstOrDefault(x => x.StationID == station.ID && stationCode + x.BedNumber == bedCode.Trim());
            if (bed == null)
            {
                _logger.Warn($"患者住院号:{caseNumber}没有获取到{(eventType == "Out" ? "转出" : "转入")}病床信息");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 创建转科实体信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="inpatientTransferView"></param>
        /// <param name="eventType"></param>
        /// <param name="departmentList"></param>
        /// <param name="stationList"></param>
        /// <param name="bedList"></param>
        /// <returns></returns>
        private async Task<InpatientTransferDataView> CreateTransferDataView(string caseNumber, InpatientTransferView inpatientTransferView,
            string eventType, List<DepartmentListInfo> departmentList, List<StationListInfo> stationList, List<BedListInfo> bedList)
        {
            var inpatient = await _inpatientDataRepository.GetInpatientDataInfo(caseNumber);
            if (inpatient == null)
            {
                _logger.Warn("创建转科信息失败, 患者住院号:" + caseNumber + "没有获取到病人住院信息");
                return null;
            }
            var inpatientTransferDataView = new InpatientTransferDataView
            {
                CaseNumber = caseNumber,
                InpatientID = inpatient.ID
            };

            if (eventType == "Out")
            {
                var transferOutDept = departmentList.FirstOrDefault(m => m.DepartmentCode == inpatientTransferView.TransferOutDepartmentCode);
                var transferOutStation = stationList.FirstOrDefault(m => m.StationCode == inpatientTransferView.TransferOutStationCode);
                var transferOutBed = bedList.FirstOrDefault(m => m.StationID == transferOutStation.ID &&
                                                                inpatientTransferView.TransferOutStationCode + m.BedNumber == inpatientTransferView.TransferOutBedCode.Trim());
                inpatientTransferDataView.inpatientTransferOutDataView = new InpatientTransferOutDataView
                {
                    TransferOutDepartmentID = transferOutDept.ID,
                    TransferOutStationID = transferOutStation.ID,
                    TransferOutBedID = transferOutBed.ID,
                    TransferOutBedNumber = transferOutBed.BedNumber,
                    TransferOutDatatime = inpatientTransferView.TransferOutDatatime.Value,
                    TransferOutEmployeeID = inpatientTransferView.TransferOutEmployeeID
                };
            }
            else if (eventType == "In")
            {
                var transferInDept = departmentList.FirstOrDefault(m => m.DepartmentCode == inpatientTransferView.TransferInDepartmentCode);
                var transferInStation = stationList.FirstOrDefault(m => m.StationCode == inpatientTransferView.TransferInStationCode);
                var transferInBed = bedList.FirstOrDefault(m => m.StationID == transferInStation.ID &&
                                                               inpatientTransferView.TransferInStationCode + m.BedNumber == inpatientTransferView.TransferInBedCode.Trim());
                inpatientTransferDataView.inpatientTransferInDataView = new InpatientTransferInDataView
                {
                    TransferInDepartmentID = transferInDept.ID,
                    TransferInStationID = transferInStation.ID,
                    TransferInBedID = transferInBed.ID,
                    TransferInBedNumber = transferInBed.BedNumber,
                    TransferInDatatime = inpatientTransferView.TransferInDatatime.Value
                };
            }

            return inpatientTransferDataView;
        }

        /// <summary>
        /// 同步患者转科数据
        /// </summary>
        /// <param name="inpatientTransferDataView"></param>
        /// <returns></returns>
        private async Task<bool> SyncInpatientTransferDept(InpatientTransferDataView inpatientTransferDataView)
        {
            try
            {
                await _requestApiService.RequestAPI("SyncInpatientTransferDept", ListToJson.ToJson(inpatientTransferDataView), null, 300000);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("患者转科数据同步失败,住院号:" + inpatientTransferDataView.CaseNumber, ex.ToString());
                return false;
            }
        }
        /// <summary>
        /// 同步患者取消转科数据
        /// </summary>
        /// <param name="inpatientTransferDataView"></param>
        /// <returns></returns>
        private async Task<bool> SyncCancelInpatientTransferDept(InpatientTransferDataView inpatientTransferDataView)
        {
            try
            {
                await _requestApiService.RequestAPI("SyncCancelInpatientTransferDept", ListToJson.ToJson(inpatientTransferDataView), null, 300000);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error("取消患者转科数据同步失败,住院号:" + inpatientTransferDataView.CaseNumber, ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 根据病区分组同步患者信息
        /// </summary>
        /// <param name="stationGroupID"></param>
        /// <returns></returns>
        public async Task<bool> SyncInPatientByStationGroup(int stationGroupID)
        {
            var stationList = await _stationGroupListRepository.GetStationGroupListByGroupID(stationGroupID);
            if (stationList == null || stationList.Count <= 0)
            {
                _logger.Warn("没有获取到病区信息");
                return false;
            }
            foreach (var station in stationList)
            {
                //同步出院信息
                await SyncInPatientByStationCode(station.StationCode, "B", null, null);
                await SyncInPatientByStationCode(station.StationCode, "O", null, null);
                //同步入院信息
                await SyncInPatientByStationCode(station.StationCode, "I", null, null);
                //同步无费退院信息
                await SyncInPatientByStationCode(station.StationCode, "N", null, null);
                // 状态为入院登记也按无费退院处理
                await SyncInPatientByStationCode(station.StationCode, "R", null, null);
            }
            return true;
        }

        #region 同步新生儿信息
        /// <summary>
        /// 同步新生儿信息（根据新生儿住院号）
        /// </summary>
        /// <param name="babyCaseNumber"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<bool> SyncNewBornDataAsync(string babyCaseNumber, string userID)
        {
            if (string.IsNullOrEmpty(babyCaseNumber))
            {
                return false; 
            }
            // 正则表达式：匹配 "B" 及其后第一个数字（\d）
            string pattern = @"B\d";
            // 替换为 "00"
            string result = Regex.Replace(babyCaseNumber, pattern, "00");
            if (result.Length < 4)
            {
                _logger.Warn($"无效新生儿住院号格式: {babyCaseNumber}");
                return false;
            }
            return await GetNewBornData(result, userID);
        }

        /// <summary>
        /// 同步新生儿信息
        /// </summary>
        /// <param name="parentCaseNumber">母亲住院流水号</param>
        /// <param name="userID">操作人</param>
        /// <returns></parentCaseNumber>
        public async Task<bool> GetNewBornData(string parentCaseNumber, string userID)
        {
            const int timeoutMilliseconds = 300000;
            const int maxNewbornSequence = 9;

            try
            {
                if (!ValidateParentCaseNumber(parentCaseNumber, out string baseNumber))
                {
                    return false;
                }

                var newBornRecords = new List<NewBornRecordView>();
                var url = await _appConfigSettingRepository.GetConfigSettingValue(
                    APPCONFIGSETTING_SETTINGTYPE_CONFIGS,
                    "GetHisPatientInfoAPI"
                );
                var count = 0;
                for (int i = 1; i <= maxNewbornSequence; i++)
                {
                    string newbornNumber = $"B{i}{baseNumber.Substring(2)}";
                    var inpatientList = await FetchInpatientData(url, new Dictionary<string, string>
                    {
                        ["patientState"] = "I",
                        ["patientNo"] = newbornNumber
                    });

                    newBornRecords = await ProcessInpatientList(inpatientList, parentCaseNumber, newBornRecords, newbornNumber);
                    // 如果当前新生儿记录列表为空，继续下一次循环
                    if (newBornRecords.Count == 0)
                    {
                        continue;
                    }
                    if (newBornRecords.Count > 0)
                    {
                        count++;
                    }
                    //循环到新生儿后没有再获取到新一胎婴儿信息时，跳出循环
                    if (count > newBornRecords.Count)
                    {
                        break;
                    }
                }
                if (newBornRecords.Count == 0)
                {
                    _logger.Warn($"无有效新生儿记录 | 母亲住院号：{parentCaseNumber}");
                    return false;
                }
                newBornRecords.ForEach(m => m.RecordPersonID = userID);
                return await SyncNewBornRecords(newBornRecords, timeoutMilliseconds);
            }
            catch (Exception ex)
            {
                _logger.Error($"同步新生儿异常: {ex.Message}");
            }
            return false;
        }

        /// <summary>
        /// 验证母亲住院流水号格式
        /// </summary>
        /// <param name="parentCaseNumber"></param>
        /// <param name="baseNumber"></param>
        /// <returns></returns>
        private bool ValidateParentCaseNumber(string parentCaseNumber, out string baseNumber)
        {
            if (string.IsNullOrEmpty(parentCaseNumber))
            {
                _logger.Warn("母亲住院流水号不能为空");
                baseNumber = null;
                return false;
            }
            if (parentCaseNumber.Length < 4)
            {
                _logger.Warn($"无效母亲住院流水号格式: {parentCaseNumber}");
                baseNumber = null;
                return false;
            }
            baseNumber = parentCaseNumber.Substring(4);
            return true;
        }

        #region Helper Methods
        private async Task<InpatientListView> FetchInpatientData(string baseUrl, Dictionary<string, string> queryParams)
        {
            var requestUrl = BuildRequestUrl(baseUrl, queryParams);
            var responseContent = await HttpHelper.HttpGetAsync(requestUrl, "application/json");

            if (string.IsNullOrWhiteSpace(responseContent))
            {
                _logger.Warn($"空响应 | URL: {requestUrl}");
                return null;
            }

            var response = ListToJson.ToList<ResponseResult>(responseContent);
            return response?.Data != null
                ? ListToJson.ToList<InpatientListView>(response.Data.ToString())
                : null;
        }

        private async Task<List<NewBornRecordView>> ProcessInpatientList(
            InpatientListView inpatientList,
            string parentCaseNumber,
            List<NewBornRecordView> resultRecords,
            string currentNewbornNumber = null)
        {
            if (inpatientList?.list == null || inpatientList.list.Count == 0)
            {
                var logContext = currentNewbornNumber != null
                    ? $"母病住院流水号：{parentCaseNumber} | 新生儿住院号号：{currentNewbornNumber}"
                    : $"病区：{OBSTETRICS_STATION_CODE}";
                _logger.Warn($"无住院数据 | {logContext}");
                return resultRecords;
            }

            foreach (var inpatient in inpatientList.list.Where(IsValidNewBornRecord))
            {
                //创建新生儿记录
                var newBornRecord = await CreateNewBornRecord(inpatient);
                if (newBornRecord == null) continue;
                resultRecords.Add(newBornRecord);
            }
            return resultRecords;
        }
        /// <summary>
        /// 检核信息
        /// </summary>
        /// <param name="inpatient"></param>
        /// <returns></returns>
        private bool IsValidNewBornRecord(HisInpatientView inpatient)
            => !string.IsNullOrEmpty(inpatient.inpatientNo) && inpatient.inpatientNo.Contains('B');
        /// <summary>
        /// 调用API同步新生儿记录
        /// </summary>
        /// <param name="records"></param>
        /// <param name="timeout"></param>
        /// <returns></returns>
        private async Task<bool> SyncNewBornRecords(List<NewBornRecordView> records, int timeout)
        {
            try
            {
                await _requestApiService.RequestAPI(
                    "SyncNewBornData",
                    ListToJson.ToJson(records),
                    null,
                    timeout
                );
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步失败: {ex.Message}");
                return false;
            }
        }
        #endregion

        /// <summary>
        /// 构建带查询参数的请求URL
        /// </summary>
        private string BuildRequestUrl(string baseUrl, Dictionary<string, string> parameters)
        {
            var uriBuilder = new UriBuilder(baseUrl);
            var query = HttpUtility.ParseQueryString(uriBuilder.Query);

            foreach (var param in parameters)
            {
                query[param.Key] = param.Value;
            }

            uriBuilder.Query = query.ToString();
            return uriBuilder.ToString();
        }

        /// <summary>
        /// 创建新生儿记录
        /// </summary>
        private async Task<NewBornRecordView> CreateNewBornRecord(HisInpatientView inpatient)
        {
            try
            {
                var record = new NewBornRecordView
                {
                    CaseNumber = inpatient.inpatientNo,
                    ChartNo = inpatient.patientNo,
                    BabyName = inpatient.patientName,
                    Birthday = inpatient.birthday,
                    DeathTime = inpatient.deathTime,
                    Weight = FormatWeight(inpatient.weight),
                    Height = FormatHeight(inpatient.height),
                    RecordPersonID = ""
                };

                // 处理性别信息
                switch (inpatient.sexCode)
                {
                    case "F":
                        record.Gender = "2";
                        record.GenderName = "女";
                        break;
                    case "M":
                        record.Gender = "1";
                        record.GenderName = "男";
                        break;
                    default:
                        record.Gender = null;
                        record.GenderName = null;
                        break;
                }

                // 解析婴儿序号
                var babyNumberMatch = Regex.Match(inpatient.patientNo, @"^B(\d)");
                if (babyNumberMatch.Success && int.TryParse(babyNumberMatch.Groups[1].Value, out int babyNumber))
                {
                    record.OrderSN = babyNumber;
                    record.BedNumber = $"{inpatient.bedNo}_{babyNumber}";

                    // 生成母亲病历号和图表号
                    var parentCaseNumber = GenerateParentIdentifier(inpatient.inpatientNo, 'B');
                    var parentChartNo = GenerateParentIdentifier(inpatient.patientNo, 'B');
                    record.ParentCaseNumber = !string.IsNullOrEmpty(parentCaseNumber) ? parentCaseNumber : "";
                    record.ParentChartNo = !string.IsNullOrEmpty(parentChartNo) ? parentChartNo : "";

                    // 获取母亲姓名
                    if (!string.IsNullOrEmpty(parentChartNo))
                    {
                        var parentName = await _patientBasicDataRepository.GetPatientNameByChartNo(parentChartNo);
                        record.ParentName = parentName;
                    }
                }

                return record;
            }
            catch (Exception ex)
            {
                _logger.Warn($"创建新生儿记录失败：{ex.Message} | 住院号：{inpatient.inpatientNo}");
                return null;
            }
        }

        /// <summary>
        /// 生成母亲记录标识符
        /// </summary>
        private string GenerateParentIdentifier(string source, char identifier)
        {
            // 找到首次出现的identifier及其后面的第一个字符
            int index = source.IndexOf(identifier);
            if (index != -1 && index + 1 < source.Length)
            {
                // 创建一个新的字符串，将identifier及其后面的一个字符替换为'0'
                char[] chars = source.ToCharArray();
                chars[index] = '0';
                chars[index + 1] = '0';
                return new string(chars);
            }
            return source;
        }

        /// <summary>
        /// 格式化体重数据（克转换）
        /// </summary>
        private string FormatWeight(decimal? weight)
        {
            return weight.HasValue ? weight.Value.ToString(CultureInfo.InvariantCulture) : null;
        }

        /// <summary>
        /// 格式化身高数据
        /// </summary>
        private string FormatHeight(decimal? height)
        {
            return height?.ToString(CultureInfo.InvariantCulture);
        }

        #endregion
        /// <summary>
        /// 获取病人就诊卡号
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        private async Task<string> GetHISPatientCardNo(string chartNo, string caseNumber)
        {
            var api = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetHisPatientInfoAPI").ConfigureAwait(false);
            if (string.IsNullOrEmpty(api))
            {
                _logger.Error("API地址未配置");
                return null;
            }
            string requestParams = $"?patientNo={chartNo}&pageFlag=false";
            var requestApi = api + requestParams;
            // 这里建议采用异步调用并处理结果
            var result = await HttpHelper.HttpGetAsync(requestApi, "application/json");
            _logger.Info("诊断接口查询患者信息，返回数据：" + result);
            var response = new ResponseResult();
            try
            {
                response = ListToJson.ToList<ResponseResult>(result);
                if (response == null || response.Data == null)
                {
                    _logger.Warn("患者信息获取失败" + ":" + chartNo + "信息数据失败，未获取到相关数据");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Warn($"诊断接口查询患者卡号信息解析错误，错误信息：{result}" + ex.ToString());
                return null;
            }
            var inpatientListView = new InpatientListView();
            try
            {
                // 对 result 做相应的处理
                inpatientListView = ListToJson.ToList<InpatientListView>(response.Data.ToString());
                if (inpatientListView == null || inpatientListView.list == null || inpatientListView.list.Count == 0)
                {
                    _logger.Warn("患者信息获取失败" + ":" + chartNo + "信息数据失败，未获取到相关数据");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.Warn($"诊断接口解析患者明细信息错误，错误信息：{response}" + ex.ToString());
                return null;
            }
            if (!string.IsNullOrEmpty(caseNumber))
            {
                return inpatientListView.list.FirstOrDefault(x => x.inpatientNo == caseNumber)?.cardNo;
            }
            return null;
        }
    }
}