﻿using NLog;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Services.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    public class NursingManagementService : INursingManagementService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IRequestApiService _requestApiService;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 责护排班
        /// </summary>
        private const string POSTNAMEVALUE = "责";

        public NursingManagementService(IRequestApiService requestApiService
            , IAppConfigSettingRepository appConfigSettingRepository)
        {
            _requestApiService = requestApiService;
            _appConfigSettingRepository = appConfigSettingRepository;
        }
        #region 同步护理管理排班数据
        /// <summary>
        /// 获取护理管理排班数据
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        public async Task<(bool, List<NurseShiftView>)> GetShitData(DateTime? startDate, DateTime? endDate)
        {
            try
            {
                if (!startDate.HasValue || !endDate.HasValue)
                {
                    startDate = startDate ?? DateTime.Now;
                    endDate = endDate ?? DateTime.Now;
                }
                var hisDataUrl = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetNursingManagementShiftData");
                hisDataUrl += $"?begin={startDate.Value:yyyy-MM-dd}&end={endDate.Value:yyyy-MM-dd}";
                var result = HttpHelper.SendObjectAsJsonInBody(hisDataUrl, null);
                if (result == null)
                {
                    _logger.Warn($"同步排班信息数据失败，访问接口获取数据为空");
                    return (false, null);
                }
                var shiftViewList = ListToJson.ToList<List<NursingManagementShiftView>>(result);
                // 获取责护排班数据
                shiftViewList = shiftViewList.Where(m => !string.IsNullOrEmpty(m.PostName) && m.PostName.Contains(POSTNAMEVALUE)).ToList();
                if (shiftViewList.Count <= 0)
                {
                    _logger.Warn($"同步排班信息数据失败，筛选责护数据为空");
                    return (false, null);
                }
                var nurseShiftList = new List<NurseShiftView>();
                foreach (var hrShit in shiftViewList)
                {
                    var view = CreateNurseShift(hrShit);
                    nurseShiftList.Add(view);
                }
                if (nurseShiftList.Count > 0)
                {
                    await _requestApiService.RequestAPI("SyncNursingManagementShiftData", ListToJson.ToJson(nurseShiftList));
                }
                return (true, nurseShiftList);
            }
            catch (Exception ex)
            {
                _logger.Warn($"同步排班信息数据失败，异常信息：{ex}");
                return (false, null);
            }
        }
        /// <summary>
        /// 转换排班数据
        /// </summary>
        /// <param name="hrShit">护理管理排班数据</param>
        /// <returns></returns>
        private static NurseShiftView CreateNurseShift(NursingManagementShiftView hrShit)
        {
            var dateTimeOffset = DateTimeOffset.FromUnixTimeMilliseconds(hrShit.ShiftDate);
            var htShitDate = dateTimeOffset.LocalDateTime;
            var shiftView = new NurseShiftView()
            {
                ShiftDate = htShitDate,
                EmployeeID = hrShit.EmployeeID,
                HRShift = hrShit.Shift,
                HRShiftName = hrShit.PostName,
                HRShiftSection = htShitDate.ToString("yyyy-MM-dd HH:mm:ss"),
            };
            return shiftView;
        }
        #endregion

        #region 同步护理管理人员信息
        /// <summary>
        /// 同步护理管理人员信息
        /// </summary>
        /// <param name="employeeIDs">人员编号集合</param>
        /// <returns></returns>
        public async Task<bool> SyncNursingManagementEmployeeBasicData(string[] employeeIDs)
        {
            try
            {
                var nursingManagementDataUrl = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetNursingManagementEmployeeData");
                var result = HttpHelper.SendObjectAsJsonInBody(nursingManagementDataUrl, employeeIDs);
                if (result == null)
                {
                    _logger.Warn($"同步护理管理人员信息数据失败，访问接口获取数据为空");
                    return false;
                }
                var employeeViewList = ListToJson.ToList<List<NursingManagementEmployeeBasicDataView>>(result);
                if (employeeViewList.Count <= 0)
                {
                    _logger.Warn($"同步护理管理人员信息数据失败，筛选责护数据为空");
                    return false;
                }
                var addEmployeeList = new List<EmployeeBasicDataView>();
                foreach (var employeeView in employeeViewList)
                {
                    var view = CreateEmployeeBasicData(employeeView);
                    addEmployeeList.Add(view);
                }
                if (addEmployeeList.Count > 0)
                {
                    await _requestApiService.RequestAPI("SyncNursingManagementEmployeeData", ListToJson.ToJson(addEmployeeList));
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"同步护理管理人员信息数据失败，异常信息：{ex}");
                return false;
            }
        }
        /// <summary>
        /// 创建护理管理人员信息View
        /// </summary>
        /// <param name="employeeView">人员信息</param>
        /// <returns></returns>
        private static EmployeeBasicDataView CreateEmployeeBasicData(NursingManagementEmployeeBasicDataView employeeView)
        {
            var birthDay = employeeView.BirthDay.HasValue ? DateTimeOffset.FromUnixTimeMilliseconds(employeeView.BirthDay.Value).LocalDateTime : (DateTime?)null;
            var entryDate = employeeView.EntryDate.HasValue ? DateTimeOffset.FromUnixTimeMilliseconds(employeeView.EntryDate.Value).LocalDateTime : (DateTime?)null;
            var joinDate = employeeView.JoinDate.HasValue ? DateTimeOffset.FromUnixTimeMilliseconds(employeeView.JoinDate.Value).LocalDateTime : (DateTime?)null;
            var leaveDateTime = employeeView.LeaveDateTime.HasValue ? DateTimeOffset.FromUnixTimeMilliseconds(employeeView.LeaveDateTime.Value).LocalDateTime : (DateTime?)null;

            var employeeBasicView = new EmployeeBasicDataView()
            {
                EmployeeID = employeeView.EmployeeID,
                EmployeeName = employeeView.EmployeeName,
                ShortCutSpell = "",
                Gender = "",
                Race = employeeView.Race,
                BirthDay = birthDay,
                LunarBirthday = "",
                IDCard = employeeView.IDCard,
                HireCategory = employeeView.HireCategory,
                EmployeeCategory = employeeView.EmployeeType,
                NativePlace = employeeView.NativePlace,
                Marriage = employeeView.Marriage,
                EducationDegree = employeeView.EducationDegree,
                GraduatedSchool = employeeView.GraduatedSchool,
                Title = employeeView.Title,
                JoinDate = joinDate,
                EntryDate = entryDate,
                LeaveStatus = employeeView.LeaveFlag ? "1" : "0",
                LeaveDate = leaveDateTime,
                Sort = 0,
                HREmployeeID = ""
            };
            return employeeBasicView;
        }
        #endregion
    }
}
