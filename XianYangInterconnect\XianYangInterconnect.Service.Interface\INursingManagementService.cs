﻿using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service.Interface
{
    public interface INursingManagementService
    {
        /// <summary>
        /// 获取护理管理排班数据
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        Task<(bool, List<NurseShiftView>)> GetShitData(DateTime? startDate, DateTime? endDate);
        /// <summary>
        /// 同步护理管理人员信息
        /// </summary>
        /// <param name="employeeIDs">人员编号集合</param>
        /// <returns></returns>
        Task<bool> SyncNursingManagementEmployeeBasicData(string[] employeeIDs);
    }
}
