﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using System.Net.Http.Headers;
using System.Text;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;
using XianYangInterconnect.ViewModels;


namespace XianYangInterconnect.Service
{
    public class SyncDatasLogServices : ISyncDatasLogServices
    {
        private readonly ISyncDatasLogRepository _syncDatasLogRepository;
        private readonly IUnitOfWork<DataOutContext> _unitOfWorkOut;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly DataOutContext _dataOutContext;

        /// <summary>
        /// SyncDataLogInfo数据异动人员ID默认值
        /// </summary>
        private const string MODIFYPERSONID = "TongBu";

        public SyncDatasLogServices(ISyncDatasLogRepository syncDatasLogRepository
            , IUnitOfWork<DataOutContext> unitOfWork
            , IAppConfigSettingRepository appConfigSettingRepository
            , DataOutContext dataOutContext)
        {
            _syncDatasLogRepository = syncDatasLogRepository;
            _unitOfWorkOut = unitOfWork;
            _appConfigSettingRepository = appConfigSettingRepository;
            _dataOutContext = dataOutContext;
        }

        //新增同步记录
        public async Task<bool> SetSyncDataLog(SyncDataLogView syncDataLogView)
        {
            //当前推送，不存储的数据           
            var syncDataLog = CreateSyncDataLogInfo(syncDataLogView);
            await _dataOutContext.AddAsync(syncDataLog);
            try
            {
                await _dataOutContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.Error("新增数据同步记录错误" + ex.ToString());
                return false;
            }
            return true;
        }

        //打印接口数据
        public async Task<string> PrintSyncDataLog(int id)
        {
            var syncDataLog = await _syncDatasLogRepository.GetSyncDataByID(id);
            if (syncDataLog == null)
            {
                return "";
            }
            _logger.Info("ID为：" + id + "的数据：" + syncDataLog.SyncData);
            return syncDataLog.SyncData;
        }


        //更新同步记录
        public async Task<bool> ModifySyncDataLog(int id, bool syncResult)
        {
            var syncDataLog = await _syncDatasLogRepository.GetSyncDataByID(id);
            if (syncResult)
            {
                syncDataLog.DataPumpFlag = "*";
                syncDataLog.DataPumpDate = DateTime.Now;
            }
            syncDataLog.Counts = (syncDataLog.Counts ?? 0) + 1;
            syncDataLog.DataPumpDate = DateTime.Now;
            syncDataLog.ModifyPersonID = "Sys";
            try
            {
                 _unitOfWorkOut.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error("新增数据同步记录错误（ModifySyncDataLog）,ID:" + id.ToString() + ex.ToString());
                return false;
            }
            return true;
        }


        /// <summary>
        /// 创建日记记录数据
        /// </summary>
        /// <param name="syncDataLogView"></param>
        /// <returns></returns>
        private SyncDataLogInfo CreateSyncDataLogInfo(SyncDataLogView syncDataLogView)
        {
            var syncDataLogInfo = new SyncDataLogInfo
            {
                HospitalID = syncDataLogView.HospitalID,
                SyncDataType = syncDataLogView.SyncDataType ?? "",
                SyncData = syncDataLogView.SyncData?.ToString() ?? "",
                CaseNumber = syncDataLogView.CaseNumber,
                EventName = syncDataLogView.EventName,
                AddDate = DateTime.Now,
                ModifyDate = DateTime.Now,
                ModifyPersonID = "TongBu",
                AddPersonID = "TongBu",
                DeleteFlag = "",
                DataPumpFlag = "",
                Counts = 0
            };
            if (syncDataLogInfo.SyncDataType == "患者同步" || syncDataLogInfo.SyncDataType == "诊断变更事件")
            {
                syncDataLogInfo.DataPumpFlag = "*";
            }
            return syncDataLogInfo;
        }


        /// <summary>
        /// 保存HIS的Json数据
        /// </summary>
        /// <param name="settingValue"></param>
        /// <param name="password"></param>
        /// <param name="hospitalID"></param>
        /// <param name="apiCode"></param>
        private void SavePlatformJson(string url, string password, string hospitalID, string apiCode)
        {
            DateTime now = DateTime.Now;
            var syncData = GetPlatformJson(url, password);
            if (string.IsNullOrEmpty(syncData)) return;
            SyncDataLogInfo logInfo = new SyncDataLogInfo
            {
                HospitalID = hospitalID,
                SyncDataType = apiCode,
                SyncData = syncData,
                AddPersonID = MODIFYPERSONID,
                AddDate = now,
                ModifyPersonID = MODIFYPERSONID,
                ModifyDate = now,
                DataPumpFlag = "",
                DataPumpDate = null,
                Counts = 0
            };
            //字典类接口统一为MS000，需要自行分类
            if (apiCode == "MS000")
            {
                //人员
                if (logInfo.SyncData.Contains("MS024")) logInfo.SyncDataType = "MS024";
                //科室
                if (logInfo.SyncData.Contains("MS025")) logInfo.SyncDataType = "MS025";
                //药品
                if (logInfo.SyncData.Contains("MS028")) logInfo.SyncDataType = "MS028";
                //频次
                if (logInfo.SyncData.Contains("MS059")) logInfo.SyncDataType = "MS059";
                //诊断
                if (logInfo.SyncData.Contains("MS002")) logInfo.SyncDataType = "MS002";
            }
            _unitOfWorkOut.GetRepository<SyncDataLogInfo>().Insert(logInfo);
        }
        /// <summary>
        /// 获取HIS的Json数据
        /// </summary>
        /// <param name="url"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        private string GetPlatformJson(string url, string password)
        {
            string result = "";
            try
            {
                HttpClient httpClient = new HttpClient();
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.UTF8.GetBytes(password)));
                result = httpClient.GetAsync(url).Result.Content.ReadAsStringAsync().Result;
                httpClient.Dispose();
            }
            catch (Exception ex)
            {
                _logger.Error("GetPlatformJson失败：url=" + url + "错误信息：" + ex.ToString());
                return "";
            }
            if (result == "{\"status\":0}")
            {
                return "";
            }
            return result;
        }
    }
}
