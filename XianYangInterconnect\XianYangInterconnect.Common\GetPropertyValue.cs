﻿using System.Reflection;

namespace XianYangInterconnect.Common
{
    public class GetPropertyValue
    {
        /// <summary>
        /// 获得对象字段对应的值
        /// </summary>
        /// <typeparam name="M"></typeparam>
        /// <param name="main"></param>
        /// <param name="propertyname"></param>
        /// <returns></returns>
        public static string GetValue<M>(M mainInfo, string propertyname)
        {
            Type type = typeof(M);

            PropertyInfo property = type.GetProperty(propertyname);

            if (property == null) return string.Empty;

            object o = property.GetValue(mainInfo, null);

            if (o == null) return string.Empty;

            return o.ToString();
        }
    }
}