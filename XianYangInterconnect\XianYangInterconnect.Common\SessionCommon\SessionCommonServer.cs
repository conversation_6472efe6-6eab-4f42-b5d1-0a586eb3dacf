﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using NLog;

namespace XianYangInterconnect.Common.SessionCommon
{
    /// <summary>
    /// ServerSession
    /// </summary>
    public class SessionCommonServer
    {
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private static IMemoryCache _memoryCache;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IOptions<CommonSystemConfig> _config;

        /// <summary>
        /// ServerSession
        /// </summary>
        /// <param name="sessionService"></param>
        /// <param name="httpContextAccessor"></param>
        public SessionCommonServer(
           IMemoryCache memoryCache
            , IHttpContextAccessor httpContextAccessor
            , IOptions<CommonSystemConfig> options
            )
        {
            _memoryCache = memoryCache;
            _httpContextAccessor = httpContextAccessor;
            _config = options;
        }

        public Session GetSessionByCache()
        {
            var token = "";
            var session = new Session();
            try
            {
                token = _httpContextAccessor.HttpContext.GetCommonToken();
                if (!string.IsNullOrWhiteSpace(token))
                {
                    token = token.Trim();
                }
            }
            catch (Exception)
            {
                _logger.Warn("没有获取到Token,从本地配置文件获取");
            }

            if (!string.IsNullOrWhiteSpace(token))
            {
                session = GetSession(token);
            }
            //如果没有Token 获取默认的病区
            if (session == null || string.IsNullOrWhiteSpace(token))
            {
                var hospitalID = _config.Value.HospitalID;
                var language = _config.Value.Language;
                //获取登录前时候记录的HospitLID及Language
                //记录当先登录医院的用户信息，在登录前因Token不存在，获取缓存使用,在ServerSession有Set
                try
                {
                    var loginSession = GetSession("UserLoginHospitalLanguage");
                    if (loginSession != null)
                    {
                        hospitalID = loginSession.HospitalID;
                        language = loginSession.Language;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Error($"GetSession(UserLoginHospitalLanguage)方法报错：{ex}");
                }
                session = new Session()
                {
                    HospitalID = hospitalID,
                    Language = language
                };
            }

            return session;
        }

        /// <summary>
        /// 获取session
        /// </summary>
        /// <returns></returns>
        public async Task<Session> GetSession()
        {
            var token = "";
            var session = new Session();
            try
            {
                token = _httpContextAccessor.HttpContext.GetCommonToken();
                if (!string.IsNullOrWhiteSpace(token))
                {
                    token = token.Trim();
                }
            }
            catch (Exception)
            {
                _logger.Warn("没有获取到Token,从本地配置文件获取");
            }

            if (!string.IsNullOrWhiteSpace(token))
            {
                session = await GetAsync(token);
            }
            //如果没有Token 获取默认的病区
            if (session == null || string.IsNullOrWhiteSpace(token))
            {
                var hospitalID = _config.Value.HospitalID;
                var language = _config.Value.Language;
                //获取登录前时候记录的HospitLID及Language
                //记录当先登录医院的用户信息，在登录前因Token不存在，获取缓存使用,在ServerSession有Set
                var loginSession = await GetAsync("UserLoginHospitalLanguage");
                if (loginSession != null)
                {
                    hospitalID = loginSession.HospitalID;
                    language = loginSession.Language;
                }
                session = new Session()
                {
                    HospitalID = hospitalID,
                    Language = language
                };
            }

            return await Task.Run(() => session);
        }

        public Tuple<string, int> GetParamsByKey(string key)
        {
            var strs = key.Split("_");
            var hospitalID = "";
            int language = 1;
            if (strs.Length >= 3)
            {
                hospitalID = strs[1];
                int.TryParse(strs[2], out language);
            }
            return Tuple.Create(hospitalID, language);
        }

        private Session GetSession(string token)
        {
            return _memoryCache.Get<Session>(token);
        }

        private async Task<Session> GetAsync(string token)
        {
            return await Task.Factory.StartNew(() =>
            {
                return _memoryCache.TryGetValue<Session>(token, out var session) ? session : null;
            });
        }
    }
}