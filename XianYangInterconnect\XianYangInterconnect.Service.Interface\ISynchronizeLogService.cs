﻿using XianYangInterconnect.Models.Interconnect;

namespace XianYangInterconnect.Service
{
    public interface ISynchronizeLogService
    {
        /// <summary>
        /// 创建日志model类
        /// </summary>
        /// <param name="apiUrl">api地址</param>
        /// <param name="args">请求参数</param>
        /// <param name="inpatientID">患者CCC住院唯一ID</param>
        /// <param name="caseNumber">就诊流水号</param>
        /// <param name="isPost">是否为post请求</param>
        /// <returns></returns>
        Task<SynchronizeLogInfo> CreateSynchronizeLogInfo(string apiUrl, string args, string inpatientID,
            string caseNumber, bool isPost);
    }
}
