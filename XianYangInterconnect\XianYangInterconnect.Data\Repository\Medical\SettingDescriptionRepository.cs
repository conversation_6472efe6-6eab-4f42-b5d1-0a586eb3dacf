﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using XianYangInterconnect.Common;
using XianYangInterconnect.Common.SessionCommon;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Repository
{
    public class SettingDescriptionRepository : ISettingDescriptionRepository
    {
        private readonly MedicalContext _medicalContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;

        /// <summary>
        /// 所用医院通用
        /// </summary>
        private const string SETTINGDESCRIPTION_HOSPITALID = "999999";

        public SettingDescriptionRepository(
            MedicalContext db
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            , GetCacheService getCacheService
        )
        {
            _medicalContext = db;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _getCacheService = getCacheService;
        }

        public async Task<List<SettingDescriptionInfo>> GetBySettingTypeCode(string settingTypeCode)
        {
            var datas = await GetAsync();
            return datas.Where(t => t.SettingTypeCode == settingTypeCode).ToList();
        }

        public async Task<string> GetTypeValue(string settingTypeCode)
        {
            var datas = await GetAsync();
            if (datas != null)
            {
                return datas.Where(t => t.SettingTypeCode == settingTypeCode).Select(m => m.TypeValue).FirstOrDefault();
            }
            return null;
        }

        public async Task<List<SettingDescriptionInfo>> GetAsync()
        {
            return await GetCacheAsync() as List<SettingDescriptionInfo>;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<SettingDescriptionInfo>>(key, GetDataBaseListData);
        }
        /// <summary>
        /// 获取数据库数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            dict.TryGetValue("language", out var language);
            var data = await _medicalContext.SettingDescriptions.Where(m => (m.HospitalID == SETTINGDESCRIPTION_HOSPITALID || m.HospitalID == hospitalID.ToString())
            && m.Language == (Int32)language && m.DeleteFlag != "*").ToListAsync();
            return data.OrderBy(m => m.HospitalID).GroupBy(m => new { m.Language, m.SettingTypeCode, m.SettingType, m.TypeValue }, (k, group) => group.First()).ToList(); ;
        }

        public string GetCacheType()
        {
            return CacheType.SettingDescription.GetKey(_sessionCommonServer);
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }
    }
}
