﻿namespace XianYangInterconnect.ViewModels
{
    public class OperationParamView
    {
        public List<PatientOperationView> InsertOperations { get; set; }

        public List<PatientOperationView> UpdateOperations { get; set; }

        public List<PatientOperationView> DeleteOperations { get; set; }

        public OperationParamView()
        {
            InsertOperations = [];
            UpdateOperations = [];
            DeleteOperations = [];
        }
    }

    public class OperationDataQuery
    {
        public string date { get; set; }
        public List<DataQuery> multiApply { get; set; }

    }

    public class DataQuery
    {
        public string applyCode { get; set; }
        public string patientId { get; set; }
    }
}