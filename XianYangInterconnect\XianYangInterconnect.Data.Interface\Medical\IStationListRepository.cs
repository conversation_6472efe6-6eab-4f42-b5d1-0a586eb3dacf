using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Interface
{
    public interface IStationListRepository
    {
        /// <summary>
        /// 最大ID
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<int>> GetStationListMaxID(string hospitalID);
        /// <summary>
        /// 获取所有病区
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<StationListInfo>> GetStationList(string hospitalID);
    }
}
