﻿

using XianYangInterconnect.Models.HIS;

namespace XianYangInterconnect.Data.Interface
{
    public interface IBYJDataRepository
    {
        /// <summary>
        /// 获取HIS摆药机数据
        /// </summary>
        /// <param name="minute">小于当前时间多少分钟</param>
        /// <returns></returns>
        Task<List<BYJReturnDataInfo>> GetAsync(int minute);


        /// <summary>
        /// 获取HIS摆药机数据
        /// </summary>
        /// <param name="barCode"></param>
        /// <returns></returns>
        Task<List<BYJReturnDataInfo>> GetDataByBarCodes(List<string> barCodes);


        /// <summary>
        /// 获取口服药执行时间
        /// </summary>
        /// <param name="barcode"></param>
        /// <returns></returns>
        Task<BYJDrugExec> GetByBarcode(string barcode);

        /// <summary>
        /// 获取当先时间多少分钟前需要执行的标签数据
        /// </summary>
        /// <param name="minute"></param>
        /// <returns></returns>
        Task<List<string>> GetBarcodes(int minute);
    }
}
