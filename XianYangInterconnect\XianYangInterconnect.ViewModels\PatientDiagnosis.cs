﻿namespace XianYangInterconnect.ViewModels
{
    using System;

    public class PateintDiagnosisItem
    {
        /// <summary>
        /// 诊断diagnoseId
        /// </summary>
        public string diagnoseId { get; set; }
        /// <summary>
        /// 是否有效，1有效；0无效
        /// </summary>
        public string validFlag { get; set; }
        /// <summary>
        /// 是否是主诊断:1主诊断0非主诊断
        /// </summary>
        public string mainDiagnoseFlag { get; set; }
        /// <summary>
        /// 是否疑似(0:False 1:True)
        /// </summary>
        public string doubtfulFlag { get; set; }
        /// <summary>
        /// 科室编码
        /// </summary>
        public string operDeptCode { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>
        public string operDeptName { get; set; }
        /// <summary>
        /// 操作人编码
        /// </summary>
        public string operCode { get; set; }
        /// <summary>
        /// 操作人姓名
        /// </summary>
        public string operName { get; set; }
        /// <summary>
        /// 就诊流水
        /// </summary>
        public string serialNo { get; set; }
        /// <summary>
        /// 就诊类型:1住院;2门诊
        /// </summary>
        public string visitType { get; set; }
        /// <summary>
        /// 诊断类型编码，5504入院诊断、5502出院诊断
        /// </summary>
        public string diseaseTypeCode { get; set; }
        /// <summary>
        /// 诊断类型
        /// </summary>
        public string diseaseType { get; set; }
        /// <summary>
        /// 前缀名称
        /// </summary>
        public string prefixName { get; set; }
        /// <summary>
        /// 后缀名称
        /// </summary>
        public string postfixName { get; set; }
        /// <summary>
        /// 西医疾病ID
        /// </summary>
        public string diseaseId { get; set; }
        /// <summary>
        /// 患者卡号
        /// </summary>
        public string patientId { get; set; }
        /// <summary>
        /// 就诊流水号
        /// </summary>
        public string clinicNo { get; set; }
        /// <summary>
        /// 科室编码
        /// </summary>
        public string deptCode { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>
        public string deptName { get; set; }
        /// <summary>
        /// ICD10编码
        /// </summary>
        public string icd10Code { get; set; }
        /// <summary>
        /// ICD10名称(诊断名称)
        /// </summary>
        public string icd10Name { get; set; }
        /// <summary>
        /// 开立时间
        /// </summary>
        public string createdTime { get; set; }
    }

    public class PatientDiagnosisView
    {
        public int code { get; set; }
        public string msg { get; set; }
        public HisDiagnosisView data { get; set; }
    }
    /// <summary>
    /// 病人诊断数据
    /// </summary>
    public class PatientDiagnosisItemView
    {
        /// <summary>
        /// 病人住院序号
        /// </summary>
        public string InpatientID { get; set; }
        /// <summary>
        /// 病人序号
        /// </summary>
        public string PatientID { get; set; }
        /// <summary>
        /// 单位序号
        /// </summary>
        public int StationID { get; set; }
        /// <summary>
        /// 科别序号
        /// </summary>
        public int DepartmentListID { get; set; }
        /// <summary>
        /// 住院号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病历号
        /// </summary>
        public string ChartNo { get; set; }
        /// <summary>
        /// HIS诊断码
        /// </summary>
        public string DiagnosisCode { get; set; }
        /// <summary>
        /// 诊断名称
        /// </summary>
        public string DiagnosisName { get; set; }
        /// <summary>
        /// ICD诊断码
        /// </summary>
        public string ICDCode { get; set; }
        /// <summary>
        /// ICD版本，参见Setting
        /// </summary>
        public string ICDVersion { get; set; }
        /// <summary>
        /// 显示排序
        /// </summary>
        public int Sort { get; set; }
        /// <summary>
        /// 诊断医生
        /// </summary>
        public string DoctorNo { get; set; }
        /// <summary>
        /// 诊断时间
        /// </summary>
        public DateTime? DiagnosisTime { get; set; }
        /// <summary>
        /// 是否主诊断
        /// </summary>
        public string MainFlag { get; set; }
        /// <summary>
        /// 出院时间
        /// </summary>
        public DateTime? OutTime { get; set; }
        /// <summary>
        /// 诊断类别 A入院/P住院中/D出院
        /// </summary>
        public string DiagnosisType { get; set; }
        /// <summary>
        /// 诊断类别说明
        /// </summary>
        public string DiagnosisHISNote { get; set; }
        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeID { get; set; }
        /// <summary>
        /// 新增日期
        /// </summary>
        public DateTime AddDate { get; set; }
        /// <summary>
        /// 修改人员
        /// </summary>   
        public string ModifyPersonID { get; set; }
        /// <summary>
        /// 修改时间
        /// </summary>      
        public DateTime? ModifyDate { get; set; }
        /// <summary>
        /// 删除标志 *表示删除
        /// </summary>  
        public string DeleteFlag { get; set; }
    }
    /// <summary>
    /// 
    /// </summary>
    public class HisDiagnosisView
    {
        /// <summary>
        /// 
        /// </summary>
        public int total { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<PateintDiagnosisItem> list { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pageNum { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pageSize { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int size { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int startRow { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int endRow { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int pages { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int prePage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int nextPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string isFirstPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string isLastPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string hasPreviousPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string hasNextPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigatePages { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<int> navigatepageNums { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigateFirstPage { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int navigateLastPage { get; set; }
    }

}