﻿using System.ComponentModel.DataAnnotations.Schema;

namespace XianYangInterconnect.Models.HIS
{
    [Serializable]
    [Table("TB_BYJ_RETURNDATA")]
    public partial class BYJReturnDataInfo
    {
        /// <summary>
        /// 主键流水号
        /// </summary>
        [Column("APPLY_NUMBER", TypeName = "VARCHAR2(50)")]
        public string APPLY_NUMBER { get; set; }
        /// <summary>
        /// HIS执行档流水号
        /// </summary>
        [Column("EXEC_SQN", TypeName = "VARCHAR2(50)")]
        public string EXEC_SQN { get; set; }
        /// <summary>
        /// Casenumber
        /// </summary>
        [Column("INPATIENT_NO", TypeName = "VARCHAR2(50)")]
        public string INPATIENT_NO { get; set; }
        /// <summary>
        /// 患者姓名
        /// </summary>
        [Column("PATIENTNAME", TypeName = "VARCHAR2(50)")]
        public string PATIENTNAME { get; set; }
        /// <summary>
        /// 药品Code
        /// </summary>
        [Column("DRUG_CODE", TypeName = "VARCHAR2(50)")]
        public string DRUG_CODE { get; set; }
        /// <summary>
        /// 药品名称
        /// </summary>
        [Column("DRUG_NAME", TypeName = "VARCHAR2(100)")]
        public string DRUG_NAME { get; set; }
        /// <summary>
        /// 剂量
        /// </summary>
        [Column("DOSE", TypeName = "VARCHAR2(50)")]
        public string DOSE { get; set; }
        /// <summary>
        /// 单位
        /// </summary>
        [Column("MEDUNIT", TypeName = "VARCHAR2(50)")]
        public string MEDUNIT { get; set; }
        /// <summary>
        /// 组号
        /// </summary>
        [Column("BARCODE", TypeName = "VARCHAR2(50)")]
        public string BARCODE { get; set; }
       /// <summary>
       /// 操作人
       /// </summary>
        [Column("OPERCODE", TypeName = "VARCHAR2(50)")]
        public string OPERCODE { get; set; }
        /// <summary>
        /// 包药机包药时间
        /// </summary>
        [Column("OPERTIME", TypeName = "DATE")]
        public DateTime OPERTIME { get; set; }
        /// <summary>
        /// 包药机回写HIS时间
        /// </summary>
        [Column("OPERDATE", TypeName = "DATE")]
        public DateTime OPERDATE { get; set; }
    }
}
