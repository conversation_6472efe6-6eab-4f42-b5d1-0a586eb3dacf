﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;
using XianYangInterconnect.ViewModels;

namespace Medical.Data.Repository
{
    public class APISettingRepository : IAPISettingRepository
    {
        private readonly MedicalContext _medicalContext;
        private readonly IMemoryCache _memoryCache;
        private readonly IOptions<SystemConfig> _config;
        private readonly GetCacheService _getCacheService;
        public APISettingRepository(
              MedicalContext medicalContext
            , IMemoryCache memoryCache
            , IOptions<SystemConfig> config
             , GetCacheService getCacheService
            )
        {
            _medicalContext = medicalContext;
            _memoryCache = memoryCache;
            _config = config;
            _getCacheService = getCacheService;
        }

        /// <summary>
        /// 根据settingCode获取settingValue
        /// </summary>
        /// <param name="settingCode">类型码</param>
        /// <returns></returns>
        public async Task<string> GetSettingBySettingCode(string serverCode, string settingCode)
        {
            var apiSettings = await this.GetAllAsync<APISettingInfo>();
            // 获取套接字
            var url = apiSettings.FirstOrDefault(m => m.SettingType == _config.Value.ServerType && m.SettingCode == serverCode)?.SettingValue;
            // 获取接口
            var api = apiSettings.FirstOrDefault(m => m.SettingType == 9 && m.SettingCode == settingCode)?.SettingValue;
            if (string.IsNullOrEmpty(url) || string.IsNullOrEmpty(api))
            {
                return "";
            }
            return $"{url}{api}";
        }

        public async Task<ApiUrlView> GetAPIAddress(string apiCode)
        {
            //获取APi信息
            var apiSettingInfo = await GetUrlBySettingTypeAndSettingCode(9, apiCode);
            if (apiSettingInfo == null) { return null; }
            //获取呼叫地址前缀
            var apiServer = await GetUrlBySettingTypeAndSettingCode(_config.Value.ServerType, apiSettingInfo.ServerCode);
            //如果取不到本地配置的地址，则获取默认配置地址
            apiServer ??= await GetUrlBySettingTypeAndSettingCode(1, apiSettingInfo.ServerCode);

            var apiAddress = apiServer.SettingValue + apiSettingInfo.SettingValue;
            var apiUrl = new ApiUrlView
            {
                ApiUrl = apiAddress,
                CallType = apiSettingInfo.CallType,
                Description = apiSettingInfo.Description
            };
            return apiUrl;
        }

        public async Task<APISettingInfo> GetUrlBySettingTypeAndSettingCode(int settingType, string settingCode)
        {
            var apiSettings = await this.GetAllAsync<APISettingInfo>();
            return apiSettings.Where(m => m.SettingType == settingType && m.SettingCode == settingCode).FirstOrDefault();
        }

        public async Task<object> GetCacheAsync(dynamic Query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<APISettingInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _medicalContext.APISettingInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.APISettingInfo.ToString();
        }

        /// <summary>
        /// 根据settingCode获取远程调用地址集合
        /// </summary>
        /// <param name="settingCode">类别码</param>
        /// <returns>远程调用相关信息apiView集合</returns>
        public async Task<List<ApiUrlView>> GetAPIAddressList(string settingCode)
        {
            //获取APi信息
            var apiSettings = await this.GetAllAsync<APISettingInfo>();
            apiSettings = apiSettings.Where(m => m.SettingType == 9 && m.SettingCode == settingCode).ToList();
            if (apiSettings.Count <= 0)
            {
                return [];
            }
            var apiUrlViews = new List<ApiUrlView>();
            foreach (var apiSettingItem in apiSettings)
            {
                //获取呼叫地址前缀
                var apiServer = await GetUrlBySettingTypeAndSettingCode(_config.Value.ServerType, apiSettingItem.ServerCode);
                //如果取不到本地配置的地址，则获取默认配置地址
                apiServer ??= await GetUrlBySettingTypeAndSettingCode(1, apiSettingItem.ServerCode);

                var apiUrl = new ApiUrlView
                {
                    ApiUrl = apiServer.SettingValue + apiSettingItem.SettingValue,
                    CallType = apiSettingItem.CallType,
                    Description = apiSettingItem.Description
                };
                apiUrlViews.Add(apiUrl);
            }

            return apiUrlViews;
        }
    }
}