﻿namespace XianYangInterconnect.Services.Interface
{
    public interface IRequestApiService
    {
        /// <summary>
        /// 根据settingCode调用API并返回数据
        /// </summary>
        /// <param name="settingCode">ApiSetting表中SettingCode字段</param>
        /// <param name="param">参数，post时参数为json格式，get时参数为?a=xxx&b=yyy格式</param>
        /// <param name="token">可不传</param>
        /// <param name="contentType">默认application/json可不传，短信发送需要传对应的媒体类型</param>
        /// <param name="seconds">超时时间，默认30s</param>
        /// <returns></returns>
        Task<object> RequestAPI(string settingCode, string param, string token = null, int seconds = 30, string contentType = "application/json");

        /// <summary>
        /// 根据settingCode调用AppConfigSettingAPI并返回数据
        /// </summary>
        /// <param name="settingType">AppConfigSetting表中SettingType字段，配置类型</param>
        /// <param name="settingCode">AppConfigSetting表中SettingCode字段</param>
        /// <param name="param">参数，post时参数为json格式，get时参数为?a=xxx&b=yyy格式</param>
        /// <param name="httpType">请求类型，1：Post请求；2：Get请求</param>
        /// <param name="token">可不传</param>
        /// <param name="contentType">默认application/json可不传，短信发送需要传对应的媒体类型</param>
        /// <returns></returns>
        Task<string> RequestAPIByAppconfigSetting(string settingType, string settingCode, string param, int httpType, string token = null, string contentType = "application/json", Dictionary<string, string> headers = null);
    }
}
