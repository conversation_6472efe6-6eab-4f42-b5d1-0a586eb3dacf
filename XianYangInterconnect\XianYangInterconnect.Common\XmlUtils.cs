﻿using System.Text;
using System.Xml;
namespace XianYangInterconnect.Common
{
    public class XmlUtiles
    {

        /// <summary>
        /// 加载XML并初始化命名空间管理器
        /// </summary>
        /// <param name="xml">XML</param>
        /// <param name="namespaces">命名空间字典：前缀 => URI</param>
        public static (XmlDocument, XmlNamespaceManager) LoadXml(string xml, Dictionary<string, string> namespaces)
        {
            var xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(xml);

            var nsmgr = new XmlNamespaceManager(xmlDoc.NameTable);
            foreach (var ns in namespaces)
            {
                nsmgr.AddNamespace(ns.Key, ns.Value);
            }
            return (xmlDoc, nsmgr);
        }

        /// <summary>
        /// 替换单个节点的值（支持命名空间）
        /// </summary>
        /// <param name="xmlDoc"></param>
        /// <param name="xpath">带命名空间前缀的XPath路径</param>
        /// <param name="newValue">替换的新值</param>
        /// <param name="nsmgr">命名空间</param>
        /// <returns>替换成功返回true，找不到节点返回false</returns>
        public static bool ReplaceSingleNodeValue(XmlDocument xmlDoc,string xpath, string newValue, XmlNamespaceManager nsmgr)
        {
            if (xmlDoc == null || nsmgr == null)
            {
                throw new InvalidOperationException("请先调用 LoadXml 加载XML文件和命名空间");
            }
            XmlNode node = xmlDoc.SelectSingleNode(xpath, nsmgr);
            if (node != null)
            {
                if (node is XmlAttribute attr)
                {
                    attr.Value = newValue;
                }
                else
                {
                    node.InnerText = newValue;
                }
                return true;
            }
            else
            {
                return false;
            }
        }
        #region XPath查询节点时,命名空间相关方法
        /// <summary>
        /// 添加命名空间前缀 
        /// </summary>
        /// <param name="rawPath">文档中提供的路径</param>
        /// <param name="nsPrefix">命名空间前缀 </param>
        /// <returns></returns>
        public static string AddNamespacePrefixToXPath(string rawPath, string nsPrefix)
        {
            var segments = rawPath.Trim('/').Split('/');

            var withNs = segments.Select(seg =>
            {
                if (seg.StartsWith("@"))
                {
                    // 属性直接返回，不加前缀
                    return seg;
                }

                int predicateStart = seg.IndexOf('[');
                if (predicateStart >= 0)
                {
                    string nodeName = seg.Substring(0, predicateStart);
                    string predicate = seg.Substring(predicateStart);
                    string predicateWithNs = AddPrefixToPredicate(predicate, nsPrefix);
                    return $"{nsPrefix}:{nodeName}{predicateWithNs}";
                }
                else
                {
                    return $"{nsPrefix}:{seg}";
                }
            });

            return "/" + string.Join("/", withNs);
        }
        /// <summary>
        /// 条件表达式（子节点）添加命名空间前缀 
        /// </summary>
        /// <param name="predicate"></param>
        /// <param name="nsPrefix">命名空间前缀 </param>
        /// <returns></returns>
        private static string AddPrefixToPredicate(string predicate, string nsPrefix)
        {
            // 去除最外层中括号
            if (predicate.StartsWith("[") && predicate.EndsWith("]"))
            {
                predicate = predicate.Substring(1, predicate.Length - 2);
            }

            var sb = new StringBuilder();
            int i = 0;
            bool inQuotes = false;
            char quoteChar = '\0';

            while (i < predicate.Length)
            {
                char c = predicate[i];

                if (inQuotes)
                {
                    sb.Append(c);
                    if (c == quoteChar)
                    {
                        inQuotes = false;
                    }
                    i++;
                    continue;
                }

                if (c == '\'' || c == '"')
                {
                    inQuotes = true;
                    quoteChar = c;
                    sb.Append(c);
                    i++;
                    continue;
                }

                if (c == '@')
                {
                    // 处理属性名，不加命名空间前缀
                    sb.Append(c);
                    i++;
                    while (i < predicate.Length && (char.IsLetterOrDigit(predicate[i]) || predicate[i] == '_' || predicate[i] == '-' || predicate[i] == '.'))
                    {
                        sb.Append(predicate[i]);
                        i++;
                    }
                    continue;
                }

                // 处理节点名
                if (char.IsLetter(c))
                {
                    int start = i;
                    while (i < predicate.Length && (char.IsLetterOrDigit(predicate[i]) || predicate[i] == '_' || predicate[i] == '-'))
                    {
                        i++;
                    }
                    string nodeName = predicate.Substring(start, i - start);
                    sb.Append(nsPrefix).Append(':').Append(nodeName);
                    continue;
                }

                // 其他字符原样输出
                sb.Append(c);
                i++;
            }

            return "[" + sb.ToString() + "]";
        }
        #endregion
    }
}
