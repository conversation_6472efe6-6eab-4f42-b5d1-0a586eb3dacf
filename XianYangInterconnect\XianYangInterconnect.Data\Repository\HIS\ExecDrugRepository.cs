﻿using Microsoft.EntityFrameworkCore;
using NLog;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models.HIS;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Data.Repository
{
    public class ExecDrugRepository : IExecDrugRepository
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly HISDbContext _dbContext = null;

        public ExecDrugRepository(HISDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<ExecDrug>> GetByTimeDiff(string caseNumber, DateTime lastDateTime, DateTime beginTime, DateTime endTime)
        {
            var execDrugs = await _dbContext.ExecDrugs.Where(m => m.InpatientNo == caseNumber && m.SourceType == "1"
                            && m.UseTime >= beginTime && m.UseTime <= endTime
                            && (m.TimeStamp >= lastDateTime || m.TimeStamp == DateTime.MinValue)).ToListAsync();
            return execDrugs;
        }

        public async Task<List<PatientExecsqnView>> GetByUseTime(int minute)
        {
            var datetime = DateTime.Now.AddMinutes(minute * -1);
            var endDatetime = DateTime.Now.AddDays(2);
            endDatetime = new DateTime(endDatetime.Year, endDatetime.Month, endDatetime.Day, 23, 59, 59);
            return await _dbContext.ExecDrugs.Where(m => m.UseTime >= datetime && m.UseTime <= endDatetime).Select(
                m => new PatientExecsqnView 
                {
                    Casenumber=m.InpatientNo,
                    Execsqn=m.ExecSqn,
                }              
             ).ToListAsync();
        }

        public async Task<List<ExecDrug>> GetByUseTime(string caseNumber, DateTime beginTime, DateTime endTime)
        {
            return await _dbContext.ExecDrugs.Where(m => m.InpatientNo == caseNumber && m.SourceType == "1" && m.UseTime >= beginTime && m.UseTime <= endTime).ToListAsync();
        }
    }
}