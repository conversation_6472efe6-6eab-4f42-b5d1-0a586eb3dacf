﻿using XianYangInterconnect.Models;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Data.Interface
{
    public interface IAPISettingRepository : ICacheRepository
    {
        /// <summary>
        /// 根据settingCode获取settingValue
        /// </summary>
        /// <param name="serverCode">服务地址</param>
        /// <param name="settingCode">类型码</param>
        /// <returns></returns>
        Task<string> GetSettingBySettingCode(string serverCode, string settingCode);
        /// <summary>
        /// 根据apiCode获取api地址
        /// </summary>
        /// <param name="apiCode"></param>
        /// <returns></returns>
        Task<ApiUrlView> GetAPIAddress(string apiCode);
        /// <summary>
        /// 根据settingType和settingCode获取APISettingInfo
        /// </summary>
        /// <param name="settingType"></param>
        /// <param name="settingCode"></param>
        /// <returns></returns>
        Task<APISettingInfo> GetUrlBySettingTypeAndSettingCode(int settingType, string settingCode);

        /// <summary>
        /// 根据settingCode获取远程调用地址集合
        /// </summary>
        /// <param name="settingCode">类别码</param>
        /// <returns>远程调用相关信息apiView集合</returns>
        Task<List<ApiUrlView>> GetAPIAddressList(string settingCode);
    }
}