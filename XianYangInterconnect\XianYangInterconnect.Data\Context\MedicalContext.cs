﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Models;
using XianYangInterconnect.Models.Medical;

namespace XianYangInterconnect.Data.Context
{
    public class MedicalContext : DbContext
    {

        public MedicalContext(DbContextOptions<MedicalContext> options)
           : base(options)
        { }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            //AppConfig配置表
            builder.Entity<AppConfigSettingInfo>().HasKey(t => (new { t.AppConfigSettingID, t.HospitalID }));
            base.OnModelCreating(builder);
        }

        /// <summary>
        /// API配置表
        /// </summary>
        public DbSet<APISettingInfo> APISettingInfos { get; set; }

        /// <summary>
        /// AppConfig配置表
        /// </summary>
        public DbSet<AppConfigSettingInfo> AppConfigSettingInfos { get; set; }
        /// <summary>
        /// 床位字典
        /// </summary>
        public DbSet<BedListInfo> BedListInfos { get; set; }
        /// <summary>
        /// 病区字典
        /// </summary>
        public DbSet<StationListInfo> StationListInfos { get; set; }
        /// <summary>
        /// 科室字典
        /// </summary>
        public DbSet<DepartmentListInfo> DepartmentListInfos { get; set; }
        /// <summary>
        /// 病区对科室关系
        /// </summary>
        public DbSet<StationToDepartmentInfo> StationToDepartmentInfos { get; set; }
        /// <summary>
        /// 住院患者信息
        /// </summary>
        public DbSet<InpatientDataInfo> InpatientDataInfos { get; set; }
        /// <summary>
        /// setting配置字典
        /// </summary>
        public DbSet<SettingDescriptionInfo> SettingDescriptions { get; set; }
        /// <summary>
        /// 患者信息
        /// </summary>
        public DbSet<PatientBasicDataInfo> PatientBasicDatas { get; set; }

        /// <summary>
        /// 人员基本信息
        /// </summary>
        public DbSet<EmployeeDataInfo> EmployeeDataInfos { get; set; }

    }
}
