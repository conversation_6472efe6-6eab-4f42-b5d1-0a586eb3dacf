using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Interface
{
    public interface ISettingDescriptionRepository: ICacheRepository
    {
        /// <summary>
        /// 获取配置  ExternalSystem 使用
        /// </summary>
        /// <param name="settingTypeCode">配置名称</param>
        /// <returns></returns>
        Task<List<SettingDescriptionInfo>> GetBySettingTypeCode(string settingTypeCode);
        /// <summary>
        /// 获取配置typeValue
        /// </summary>
        /// <param name="settingTypeCode">配置名称</param>
        /// <returns></returns>
        Task<string> GetTypeValue(string settingTypeCode);
        /// <summary>
        /// 查询所有
        /// </summary>
        /// <returns></returns>
        Task<List<SettingDescriptionInfo>> GetAsync();
    }
}
