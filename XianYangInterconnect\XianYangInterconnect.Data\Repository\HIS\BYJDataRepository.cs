﻿using Microsoft.EntityFrameworkCore;
using System.Linq;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models.HIS;

namespace XianYangInterconnect.Data.Repository
{
    public class BYJDataRepository : IBYJDataRepository
    {
        private HISDbContext _dbContext = null;

        public BYJDataRepository(HISDbContext db)
        {
            _dbContext = db;
        }

        public async Task<List<BYJReturnDataInfo>> GetAsync(int minute)
        {
            minute = -minute;
            return await _dbContext.BYJReturnDatas.Where(m => m.OPERTIME >= DateTime.Now.AddMinutes(minute)).ToListAsync();
        }

        public async Task<List<BYJReturnDataInfo>> GetDataByBarCodes(List<string> barCodes)
        {
            return await _dbContext.BYJReturnDatas.Where(m => barCodes.Contains(m.BARCODE)).ToListAsync();
        }

        public async Task<BYJDrugExec> GetByBarcode(string barcode)
        {
            var result = await _dbContext.BYJDrugExecs.Where(m => m.BARCODE == barcode).ToListAsync();
            if (result.Count <= 0)
            {
                return null;
            }
            return result[0];
        }

        public async Task<List<string>> GetBarcodes(int minute)
        {
            var datetime = DateTime.Now.AddMinutes(minute*-1);
            var result = await _dbContext.BYJDrugExecs.Where(m => m.USE_TIME >= datetime).Select(m => m.BARCODE).ToListAsync();
            if (result.Count <= 0)
            {
                return null;
            }
            return result;
        }
    }
}
