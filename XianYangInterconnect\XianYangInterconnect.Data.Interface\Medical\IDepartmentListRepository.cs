﻿using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Interface.Medical
{
    public interface IDepartmentListRepository
    {
        /// <summary>
        /// 最大ID
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<int>> GetDeptListMaxID(string hospitalID);
        /// <summary>
        /// 获取所有科室
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        Task<List<DepartmentListInfo>> GetDepartmentList(string hospitalID);
    }
}
