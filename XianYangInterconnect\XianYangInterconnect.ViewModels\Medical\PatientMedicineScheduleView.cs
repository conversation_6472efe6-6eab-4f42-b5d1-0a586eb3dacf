﻿namespace XianYangInterconnect.ViewModels.Medical
{
    /// <summary>
    /// 医嘱标签
    /// 排程执行
    /// 出入量
    /// 打印插件
    /// 移动端执行
    /// 医嘱执行
    /// 皮试记录
    /// 给药闭环
    /// MT模板
    /// 触发措施
    /// 给药巡视
    /// 给药医嘱执行率
    /// 给药医嘱执行统计
    /// </summary>
    public class PatientMedicineScheduleView
    {
        /// <summary>
        /// 数据来源，1、给药拆分（默认）、2、口服药包药机
        /// </summary>
        public int? SourceType { get; set; }
        /// <summary>
        /// <summary>
        /// 给药排程序号
        /// </summary>
        public string PatientMedicineScheduleID { get; set; }

        /// <summary>
        /// 医嘱序号
        /// </summary>
        public string PatientOrderMainID { get; set; }

        /// <summary>
        /// 医嘱序号
        /// </summary>
        public string PatientOrderDetailID { get; set; }

        /// <summary>
        /// 医疗院所代码
        /// </summary>
        public string HospitalID { get; set; }

        /// <summary>
        /// 病人住院序号
        /// </summary>
        public string InpatientID { get; set; }

        /// <summary>
        /// 病人序号
        /// </summary>
        public string PatientID { get; set; }

        /// <summary>
        /// 病区码
        /// </summary>
        public string StationCode { get; set; }

        /// <summary>
        /// 病区序号
        /// </summary>
        public int StationID { get; set; }

        /// <summary>
        /// 床位序号
        /// </summary>
        public int BedID { get; set; }

        /// <summary>
        /// 床号
        /// </summary>
        public string BedNumber { get; set; }

        /// <summary>
        ///  医嘱类别(非空)
        ///  0:临时
        ///  1:长期
        /// </summary>
        public string OrderType { get; set; }

        /// <summary>
        /// 药品类别(口服 外用 大量点滴  PUMP...)
        /// </summary>
        public string MedicineType { get; set; }

        /// <summary>
        /// 住院号
        /// </summary>
        public string CaseNumber { get; set; }

        /// <summary>
        /// 病历号
        /// </summary>
        public string ChartNo { get; set; }

        /// <summary>
        /// 住院次数
        /// </summary>
        public int? NumberOfAdmissions { get; set; }

        /// <summary>
        /// 医嘱内容
        /// </summary>
        public string OrderContent { get; set; }

        /// <summary>
        /// 医嘱说明
        /// </summary>
        public string OrderDescription { get; set; }

        /// <summary>
        /// 频次
        /// </summary>
        public string Frequency { get; set; }

        /// <summary>
        /// 单次使用剂量
        /// </summary>
        public decimal? OrderDose { get; set; }

        /// <summary>
        /// 服法/途径/姿势
        /// </summary>
        public string OrderRule { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 部位
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// 总剂量
        /// </summary>
        public decimal? TotalVolume { get; set; }

        /// <summary>
        /// 首日注记
        /// </summary>
        public string FirstDayFlag { get; set; }

        /// <summary>
        /// 群组码
        /// </summary>
        public string GroupID { get; set; }

        /// <summary>
        /// 预计给药日期
        /// </summary>
        public DateTime ScheduleDate { get; set; }

        /// <summary>
        /// 预计给药时间
        /// </summary>
        public TimeSpan ScheduleTime { get; set; }

        /// <summary>
        /// 备药日期
        /// </summary>
        public DateTime? PrepareDate { get; set; }

        /// <summary>
        /// 备药时间
        /// </summary>
        public TimeSpan? PrepareTime { get; set; }

        /// <summary>
        /// 备药人员
        /// </summary>
        public string PrepareEmployeeID { get; set; }

        /// <summary>
        /// 实际给药日期
        /// </summary>
        public DateTime? PerformDate { get; set; }

        /// <summary>
        /// 实际给药时间
        /// </summary>
        public TimeSpan? PerformTime { get; set; }

        /// <summary>
        /// 给药人员
        /// </summary>
        public string PerformEmployeeID { get; set; }

        /// <summary>
        /// 停止日期
        /// </summary>
        public DateTime? StopDate { get; set; }

        /// <summary>
        /// 停止时间
        /// </summary>
        public TimeSpan? StopTime { get; set; }

        /// <summary>
        /// 停止人员
        /// </summary>
        public string StopEmployeeID { get; set; }

        /// <summary>
        /// 新增日期
        /// </summary>
        public DateTime AddDate { get; set; }

        /// <summary>
        /// 开立时间
        /// </summary>
        public DateTime? StartDateTime { get; set; }

        /// <summary>
        /// 新增人员
        /// </summary>
        public string AddEmployeeID { get; set; }

        /// <summary>
        /// 取消日期
        /// </summary>
        public DateTime? CancelDate { get; set; }

        /// <summary>
        /// 取消时间
        /// </summary>
        public TimeSpan? CancelTime { get; set; }

        /// <summary>
        /// 取消人员
        /// </summary>
        public string CancelEmployeeID { get; set; }

        /// <summary>
        /// 删除日期
        /// </summary>
        public DateTime? DeleteDate { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        public TimeSpan? DeleteTime { get; set; }

        public string DeleteFlag { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        ///数据同步标志（Medical->Interconnect）
        /// </summary>
        public string DataPumpFlag { get; set; }

        ///<summary>
        /// 列印注记
        /// </summary>
        public string PrintFlag { get; set; }

        /// <summary>
        /// 药品外包装
        /// </summary>
        public string AmountText { get; set; }

        /// <summary>
        /// 高危注记
        /// </summary>
        public string HighRiskFlag { get; set; }

        /// <summary>
        /// 扫描标志
        /// </summary>
        public string ScanFlag { get; set; }

        /// <summary>
        /// HIS医嘱状态(1使用中/2有效/3停止/4作废)
        /// </summary>
        public int? OrderStatus { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int HISOrderSort { get; set; }

        /// <summary>
        /// 班别日期
        /// </summary>
        public DateTime? ShiftDate { get; set; }

        /// <summary>
        /// 班别序号
        /// </summary>
        public int? StationShiftID { get; set; }

        /// <summary>
        /// 移动装置执行注记
        /// </summary>
        public bool? PDAFlag { get; set; }

        /// <summary>
        /// 带交班
        /// </summary>
        public string BringToShift { get; set; }

        /// <summary>
        /// 带记录
        /// </summary>
        public string BringToNursingRecords { get; set; }

        /// <summary>
        /// 医嘱执行说明
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 执行说明
        /// </summary>
        public string PerformComment { get; set; }

        /// <summary>
        /// 延迟执行原因
        /// </summary>
        public string DelayPerformReason { get; set; }

        /// <summary>
        /// 延迟执行说明
        /// </summary>
        public string DelayPerformComment { get; set; }

        /// <summary>
        /// 不执行原因
        /// </summary>
        public string NotPerformReason { get; set; }

        /// <summary>
        /// 不执行说明
        /// </summary>
        public string NotPerformComment { get; set; }

        /// <summary>
        /// 药品禁忌内容
        /// </summary>
        public string DrugAttention { get; set; }

        /// <summary>
        /// 移动端执行标记
        /// </summary>
        public string ClientType { get; set; }

        /// <summary>
        /// 转换后剂量
        /// </summary>
        public decimal? ConvertedVolume { get; set; }

        /// <summary>
        /// 医嘱码
        /// </summary>
        public string OrderCode { get; set; }

        /// <summary>
        /// 核对人工号（移动端给药执行时高危药品双人核对使用）
        /// </summary>
        public string RecheckEmployeeID { get; set; }

        /// <summary>
        /// 计费标记，0-正常计费，1-不计费/自带药
        /// BillingAttribution = 1 [自]
        /// BillingAttribution = 4 [不摆药]
        /// </summary>
        public string BillingAttribution { get; set; }

        /// <summary>
        /// His医嘱组号
        /// </summary>
        public string HISOrderGroupNo { get; set; }

        /// <summary>
        /// 滴速/泵速
        /// </summary>
        public string Speed { get; set; }

        /// <summary>
        /// 给药对应的药品类型
        /// </summary>
        public string DrugType { get; set; }

        /// <summary>
        /// 药品规格
        /// 三查七对一注意
        /// </summary>
        public string DrugSpec { get; set; }
        /// <summary>
        /// 药嘱码
        /// 匹配DrugList用于药品入量转换
        /// 匹配DrugTriggerSetting用于逻辑触发
        /// </summary>
        public string DrugCode { get; set; }
        /// <summary>
        /// 药品用量,最小单位使用数量
        /// 如:10ml*5支/盒,需要用几支
        /// </summary>
        public decimal? Package { get; set; }
        /// <summary>
        /// 药品用量,最小单位
        /// 如:10ml*5支/盒,最小单位是支
        /// </summary>
        public string PackageUnit { get; set; }
    }
}