﻿namespace XianYangInterconnect.ViewModels
{
    public class PatientOperationView
    {
        /// <summary>
        /// 住院号
        /// </summary>       
        public string CaseNumber { get; set; }
        /// <summary>
        /// 病人病历号
        /// </summary> 
        public string ChartNo { get; set; }
        /// <summary>
        /// 手术名称
        /// </summary>
        public string OperationName { get; set; }
        /// <summary>
        /// 预约日期（择期手术的预约做手术时间）
        /// </summary>       
        public DateTime? ScheduledDateTime { get; set; }
        /// <summary>
        /// 手术日期（手术真实开始时间，与手术开始时间默认保持一致）
        /// </summary>       
        public DateTime? OperationDate { get; set; }
        /// <summary>
        /// 麻醉方式
        /// </summary>
        public string AnesthesiaMethod { get; set; }
        /// <summary>
        /// 手术取消时间
        /// </summary>
        public DateTime? CancelDateTime { get; set; }
        /// <summary>
        /// HIS手术主键
        /// </summary>
        public string HISOperationNo { get; set; }
        /// <summary>
        /// 异动人员
        /// </summary>
        public string ModifyPersonID { get; set; }
        /// <summary>
        /// 预术
        /// </summary>
        public bool PreOP { get; set; }
        /// <summary>
        /// 手术结束
        /// </summary>
        public bool PostOP { get; set; }
    }
}