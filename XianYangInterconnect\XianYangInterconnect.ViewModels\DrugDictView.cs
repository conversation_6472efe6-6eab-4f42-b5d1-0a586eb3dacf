﻿namespace XianYangInterconnect.ViewModels
{
    public class DrugDictView
    {
        public int DrugListID { get; set; }
        /// <summary>
        /// 药品编码
        /// </summary>
        public string DrugCode { get; set; }
        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalID { get; set; }
        /// <summary>
        /// 药品名称 HIS(DRUG_NAME)
        /// </summary>
        public string DrugName { get; set; }
        /// <summary>
        /// 药品最小规格 (DRUG_SPEC)
        /// </summary>
        public string DrugSpec { get; set; }
        /// <summary>
        /// 药品最小单位(UNITS)
        /// </summary>
        public string Units { get; set; }
        /// <summary>
        /// 药品分类（如普通药品、精神二类、麻醉药品等）,对应HIS(TOXI_PROPERTY)
        /// </summary>
        public string DrugType { get; set; }
        /// <summary>
        /// 药品剂型分类（溶液、片剂、针剂、胶囊等），对应HIS（DRUG_FORM）
        /// </summary>
        public string DrugForm { get; set; }
        /// <summary>
        /// 剂量单位(ml) HIS(DOSE_UNITS)
        /// </summary>
        public string DoseUnits { get; set; }
        /// <summary>
        /// 对应的剂量(ml) HIS(DOSE_PER_UNIT)
        /// </summary>
        public decimal? DosePreUnit { get; set; }
        /// <summary>
        /// 拼音码,对应HIS(INPUT_CODE)
        /// </summary>
        public string PinYinCode { get; set; }
        /// <summary>
        /// 高危药品标记,对应HIS(ALARM_PROPERTY)
        /// </summary>
        public string HighRiskFlag { get; set; }
        /// <summary>
        /// 换算标准体积（ml）
        /// </summary>
        public decimal? ConvertedVolume { get; set; }
        /// <summary>
        /// 药理码
        /// </summary>
        public string ChemicCode { get; set; }
    }
}
