﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.7.34018.315
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianYangInterconnect.API", "XianYangInterconnect.API\XianYangInterconnect.API.csproj", "{AB14ED96-3F7D-461E-8FBC-2FB2A3544B8E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianYangInterconnect.Common", "XianYangInterconnect.Common\XianYangInterconnect.Common.csproj", "{75A8161F-5AF8-49FA-B632-721A5D9E9797}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianYangInterconnect.Data", "XianYangInterconnect.Data\XianYangInterconnect.Data.csproj", "{FE516484-9CA2-48A7-A813-C18CA350F03E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianYangInterconnect.Data.Interface", "XianYangInterconnect.Data.Interface\XianYangInterconnect.Data.Interface.csproj", "{6EB594E3-D5C0-446D-A392-6251D2780249}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianYangInterconnect.Models", "XianYangInterconnect.Models\XianYangInterconnect.Models.csproj", "{C9D3501F-B170-431B-A42A-7D652E31C3F7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianYangInterconnect.Service.Interface", "XianYangInterconnect.Service.Interface\XianYangInterconnect.Service.Interface.csproj", "{1CEED923-551C-4533-9A43-758DCE401070}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianYangInterconnect.ViewModels", "XianYangInterconnect.ViewModels\XianYangInterconnect.ViewModels.csproj", "{FD49E3A9-C2D6-48AB-BBDD-6657B8EFBD06}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "XianYangInterconnect.Service", "XianYangInterconnect.Service\XianYangInterconnect.Service.csproj", "{9E9517BE-F335-424A-9411-9C102C237E0E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AB14ED96-3F7D-461E-8FBC-2FB2A3544B8E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB14ED96-3F7D-461E-8FBC-2FB2A3544B8E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB14ED96-3F7D-461E-8FBC-2FB2A3544B8E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB14ED96-3F7D-461E-8FBC-2FB2A3544B8E}.Release|Any CPU.Build.0 = Release|Any CPU
		{75A8161F-5AF8-49FA-B632-721A5D9E9797}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{75A8161F-5AF8-49FA-B632-721A5D9E9797}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{75A8161F-5AF8-49FA-B632-721A5D9E9797}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{75A8161F-5AF8-49FA-B632-721A5D9E9797}.Release|Any CPU.Build.0 = Release|Any CPU
		{FE516484-9CA2-48A7-A813-C18CA350F03E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE516484-9CA2-48A7-A813-C18CA350F03E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE516484-9CA2-48A7-A813-C18CA350F03E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE516484-9CA2-48A7-A813-C18CA350F03E}.Release|Any CPU.Build.0 = Release|Any CPU
		{6EB594E3-D5C0-446D-A392-6251D2780249}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6EB594E3-D5C0-446D-A392-6251D2780249}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6EB594E3-D5C0-446D-A392-6251D2780249}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6EB594E3-D5C0-446D-A392-6251D2780249}.Release|Any CPU.Build.0 = Release|Any CPU
		{C9D3501F-B170-431B-A42A-7D652E31C3F7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C9D3501F-B170-431B-A42A-7D652E31C3F7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C9D3501F-B170-431B-A42A-7D652E31C3F7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C9D3501F-B170-431B-A42A-7D652E31C3F7}.Release|Any CPU.Build.0 = Release|Any CPU
		{1CEED923-551C-4533-9A43-758DCE401070}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1CEED923-551C-4533-9A43-758DCE401070}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1CEED923-551C-4533-9A43-758DCE401070}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1CEED923-551C-4533-9A43-758DCE401070}.Release|Any CPU.Build.0 = Release|Any CPU
		{FD49E3A9-C2D6-48AB-BBDD-6657B8EFBD06}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD49E3A9-C2D6-48AB-BBDD-6657B8EFBD06}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD49E3A9-C2D6-48AB-BBDD-6657B8EFBD06}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD49E3A9-C2D6-48AB-BBDD-6657B8EFBD06}.Release|Any CPU.Build.0 = Release|Any CPU
		{9E9517BE-F335-424A-9411-9C102C237E0E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9E9517BE-F335-424A-9411-9C102C237E0E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9E9517BE-F335-424A-9411-9C102C237E0E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9E9517BE-F335-424A-9411-9C102C237E0E}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C3FB0B92-7B52-4079-8956-A90761B3B3E4}
	EndGlobalSection
EndGlobal
