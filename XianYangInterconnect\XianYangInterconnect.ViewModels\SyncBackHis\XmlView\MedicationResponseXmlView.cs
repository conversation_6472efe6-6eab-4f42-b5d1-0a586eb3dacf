﻿using System.Xml.Serialization;

namespace XianYangInterconnect.ViewModels
{

    [XmlRoot(ElementName = "Envelope", Namespace = "http://www.w3.org/2003/05/soap-envelope")]
public class MedicationResponseXmlView
{
    [XmlElement(ElementName = "Body", Namespace = "http://www.w3.org/2003/05/soap-envelope")]
    public Body Body { get; set; }
}

public class Body
{
    [XmlElement(ElementName = "MCCI_IN000002UV01", Namespace = "urn:hl7-org:v3")]
    public MCCI_IN000002UV01 Message { get; set; }
}

[XmlRoot(ElementName = "MCCI_IN000002UV01", Namespace = "urn:hl7-org:v3")]
public class MCCI_IN000002UV01
{
    [XmlAttribute(AttributeName = "ITSVersion")]
    public string ITSVersion { get; set; }

    [XmlElement(ElementName = "id", Namespace = "urn:hl7-org:v3")]
    public ID Id { get; set; }

    [XmlElement(ElementName = "creationTime", Namespace = "urn:hl7-org:v3")]
    public CreationTime CreationTime { get; set; }

    [XmlElement(ElementName = "interactionId", Namespace = "urn:hl7-org:v3")]
    public InteractionId InteractionId { get; set; }

    [XmlElement(ElementName = "processingCode", Namespace = "urn:hl7-org:v3")]
    public CodeElement ProcessingCode { get; set; }

    [XmlElement(ElementName = "processingModeCode", Namespace = "urn:hl7-org:v3")]
    public CodeElement ProcessingModeCode { get; set; }

    [XmlElement(ElementName = "acceptAckCode", Namespace = "urn:hl7-org:v3")]
    public CodeElement AcceptAckCode { get; set; }

    [XmlElement(ElementName = "receiver", Namespace = "urn:hl7-org:v3")]
    public Receiver Receiver { get; set; }

    [XmlElement(ElementName = "sender", Namespace = "urn:hl7-org:v3")]
    public Sender Sender { get; set; }

    [XmlElement(ElementName = "acknowledgement", Namespace = "urn:hl7-org:v3")]
    public Acknowledgement Acknowledgement { get; set; }
}

public class ID
{
    [XmlAttribute(AttributeName = "root")]
    public string Root { get; set; }

    [XmlAttribute(AttributeName = "extension")]
    public string Extension { get; set; }
}


public class CodeElement
{
    [XmlAttribute(AttributeName = "code")]
    public string Code { get; set; }
}


public class Acknowledgement
{
    [XmlAttribute(AttributeName = "typeCode")]
    public string TypeCode { get; set; }

    [XmlElement(ElementName = "targetMessage", Namespace = "urn:hl7-org:v3")]
    public TargetMessage TargetMessage { get; set; }

    [XmlElement(ElementName = "acknowledgementDetail", Namespace = "urn:hl7-org:v3")]
    public AcknowledgementDetail AcknowledgementDetail { get; set; }
}

public class TargetMessage
{
    [XmlElement(ElementName = "id", Namespace = "urn:hl7-org:v3")]
    public ID Id { get; set; }
}

public class AcknowledgementDetail
{
    [XmlElement(ElementName = "text", Namespace = "urn:hl7-org:v3")]
    public AcknowledgementText Text { get; set; }
}

public class AcknowledgementText
{
    [XmlAttribute(AttributeName = "value")]
    public string Value { get; set; }
}

}
