﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XianYangInterconnect.Models.Base;

namespace XianYangInterconnect.Models
{
    /// <summary>
    /// 护理单元与科室对应字典
    /// </summary>
    [Serializable]
    [Table("StationToDepartment")]
    public class StationToDepartmentInfo : ModifyInfo
    {
        /// <summary>
        /// 护理单元科室对应序号
        /// </summary>       
        [Key]
        [Column("StationToDepartmentID")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }
        /// <summary>
        /// 护士站代码
        /// </summary>       
        public int StationID { get; set; }
        /// <summary>
        /// 外部护士站代码
        /// </summary>       
        [Column(TypeName = "varchar(20)")]
        public String StationCode { get; set; }
        /// <summary>
        /// 科室代码
        /// </summary>      
        public int DepartmentListID { get; set; }
        /// <summary>
        /// 外部科室代码
        /// </summary>      
        [Column(TypeName = "varchar(20)")]
        public string DepartmentCode { get; set; }
        /// <summary>
        /// 医院代码
        /// </summary>      
        [Column(TypeName = "varchar(20)")]
        public string HospitalID { get; set; }
    }
}
