﻿namespace XianYangInterconnect.Service.Interface.SyncDataLog
{
    public interface ISyncLogService
    {
        /// <summary>
        /// 增加同步日志
        /// </summary>
        /// logLevel  日志级别 Error 1,Warn 2,Info 3,使用数值存储，提高写效能
        /// <param name="logGroupID">证明是一组日志，如同一个事务产生的日志，使用GuID区分，方便查询</param>
        /// <param name="logTypeCode">日志类别</param>
        /// <param name="logTypeName">日志类别名称</param>
        /// <param name="contents">日志描述</param>
        /// <param name="addPersonID">新增人员</param>
        /// <param name="commit">是否提交数据</param>
        /// <returns></returns>
        bool InsertSyncLog(byte logLevel, string logGroupID, string logTypeCode, string logTypeName, string contents, string addPersonID, bool commit);
    }
}