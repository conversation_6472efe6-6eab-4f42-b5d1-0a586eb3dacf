namespace XianYangInterconnect.ViewModels
{
    /// <summary>
    /// 输血监测数据输入视图模型
    /// </summary>
    public class InterconnectCoreBloodInspectInputView
    {
        /// <summary>
        /// 病案号
        /// </summary>
        public string CaseNumber { get; set; }

        /// <summary>
        /// 血袋号
        /// </summary>
        public string BloodNumber { get; set; }

        /// <summary>
        /// 输血监测数据唯一ID
        /// </summary>
        public string SourceUniqueID { get; set; }

        /// <summary>
        /// 执行人ID
        /// </summary>
        public string PerformEmployeeID { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public DateTime PerformDateTime { get; set; }

        /// <summary>
        /// 动态数据项（键值对）
        /// </summary>
        public Dictionary<int, string> KeyValueItems { get; set; } = new Dictionary<int, string>();

        /// <summary>
        /// 设置键值项，含字段转换逻辑
        /// </summary>
        public void SetKeyValue(string emrKey, object value)
        {
            if (value == null)
            {
                return;
            }
            var mappedKey = MapEmrKey(emrKey);
            if (mappedKey.HasValue)
            {
                KeyValueItems[mappedKey.Value] = value.ToString();
            }
        }

        /// <summary>
        /// EMR 字段名转业务键名（程序性写死的映射）
        /// </summary>
        private static int? MapEmrKey(string key)
        {
            return key switch
            {
                "EMRSource_72" => 72,//体温
                "EMRSource_74" => 74,//心率
                "EMRSource_163" => 163,//呼吸
                "EMRSource_164" => 164,//血压
                "EMRSource_168" => 168,//执行输血巡视时记录护理观察
                _ => null
            };
        }

        /// <summary>
        /// 从HIS血液检查数据转换为输入视图模型
        /// </summary>
        /// <param name="hisData">HIS血液检查数据</param>
        /// <returns>输入视图模型</returns>
        public static InterconnectCoreBloodInspectInputView FromHisData(HisBloodInspectView hisData)
        {
            if (hisData == null)
                return null;

            var inputView = new InterconnectCoreBloodInspectInputView
            {
                CaseNumber = hisData.CaseNumber,
                BloodNumber = hisData.BloodNumber,
                SourceUniqueID = hisData.SourceUniqueID,
                PerformEmployeeID = hisData.PerformEmployeeID,
                PerformDateTime = hisData.PerformDateTime
            };

            // 设置动态数据项
            inputView.SetKeyValue("EMRSource_72", hisData.Temperature);
            inputView.SetKeyValue("EMRSource_74", hisData.HeartRate);
            inputView.SetKeyValue("EMRSource_163", hisData.Breathing);
            inputView.SetKeyValue("EMRSource_164", hisData.BloodPressure);
            inputView.SetKeyValue("EMRSource_168", hisData.NursingObservation);

            return inputView;
        }

        /// <summary>
        /// 批量转换HIS数据为输入视图模型列表
        /// </summary>
        /// <param name="hisDataList">HIS血液检查数据列表</param>
        /// <returns>输入视图模型列表</returns>
        public static List<InterconnectCoreBloodInspectInputView> FromHisDataList(List<HisBloodInspectView> hisDataList)
        {
            if (hisDataList == null || !hisDataList.Any())
                return new List<InterconnectCoreBloodInspectInputView>();

            return hisDataList.Where(x => x != null)
                             .Select(FromHisData)
                             .Where(x => x != null)
                             .ToList();
        }
    }
}
