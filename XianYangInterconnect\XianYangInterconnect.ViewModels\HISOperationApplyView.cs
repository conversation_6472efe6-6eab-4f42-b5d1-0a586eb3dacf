﻿using System.ComponentModel.DataAnnotations;

namespace XianYangInterconnect.ViewModels
{
    public class HISOperationApplyResponse
    {
        public long code { get; set; }
        public List<HISOperationApplyView> data { get; set; }
        public string msg { get; set; }
    }
    public partial class HISOperationApplyView
    {
        /// <summary>
        /// 医院编码
        /// </summary>
        public string hospitalCode { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>
        public string hospitalAreaCode { get; set; }

        /// <summary>
        /// 患者档案号
        /// </summary>
        public string cardNo { get; set; }

        /// <summary>
        /// 门诊流水号
        /// </summary>
        public string clinicCode { get; set; }

        /// <summary>
        /// 住院号
        /// </summary>
        public string patientNo { get; set; }

        /// <summary>
        /// 住院流水号
        /// </summary>
        public string inpatientNo { get; set; }
        [Key]
        public string operationApplyNo { get; set; }

        /// <summary>
        /// 日间手术标识
        /// </summary>
        public string isOpsLessDay { get; set; }

        /// <summary>
        /// 手术类型
        /// </summary>
        public string operationType { get; set; }

        /// <summary>
        /// 手术等级编码
        /// </summary>
        public string operLevelCode { get; set; }

        /// <summary>
        /// ASA分级编码
        /// </summary>
        public string asaLevelCode { get; set; }

        /// <summary>
        /// 麻醉类型
        /// </summary>
        public string anesthesiaType { get; set; }

        /// <summary>
        /// 麻醉方法
        /// </summary>
        public string anesMethod { get; set; }

        /// <summary>
        /// 麻醉方法名称
        /// </summary>
        public string anesMethodName { get; set; }

        /// <summary>
        /// 申请科室编码
        /// </summary>
        public string applyDeptCode { get; set; }

        /// <summary>
        /// 申请科室名称
        /// </summary>
        public string applyDeptName { get; set; }

        /// <summary>
        /// 申请医生编码
        /// </summary>
        public string applyDoctorCode { get; set; }

        /// <summary>
        /// 申请医生姓名
        /// </summary>
        public string applyDoctorName { get; set; }

        /// <summary>
        /// 手术申请时间
        /// </summary>
        public string applyTime { get; set; }

        /// <summary>
        /// 执行科室编码
        /// </summary>
        public string executeDeptCode { get; set; }

        /// <summary>
        /// 执行科室名称
        /// </summary>
        public string executeDeptName { get; set; }

        /// <summary>
        /// 是否大病种
        /// </summary>
        public string isBigDisease { get; set; }

        /// <summary>
        /// 是否临床路径
        /// </summary>
        public string isClinicalPathway { get; set; }

        /// <summary>
        /// 是否会诊
        /// </summary>
        public string isConsultation { get; set; }

        /// <summary>
        /// 是否有传染疾病
        /// </summary>
        public string isInfectious { get; set; }

        /// <summary>
        /// 传染病描述
        /// </summary>
        public string diseaseMemo { get; set; }

        /// <summary>
        /// 是否单病种
        /// </summary>
        public string isSingleDisease { get; set; }

        /// <summary>
        /// 病种名称
        /// </summary>
        public string operationDiseaseName { get; set; }

        /// <summary>
        /// 预约手术台序
        /// </summary>
        public string operationTableOrder { get; set; }

        /// <summary>
        /// 预约手术时间
        /// </summary>
        public string operationTime { get; set; }

        /// <summary>
        /// 术前抗生素
        /// </summary>
        public string preopAntibiotics { get; set; }

        /// <summary>
        /// 术前用药
        /// </summary>
        public string preopDrug { get; set; }

        /// <summary>
        /// 植入物
        /// </summary>
        public string implants { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string remark { get; set; }

        /// <summary>
        /// 医保类型编码
        /// </summary>
        public string medicareTypeCode { get; set; }

        /// <summary>
        /// 医保类型名称
        /// </summary>
        public string medicareTypeName { get; set; }

        /// <summary>
        /// 腕带条码号
        /// </summary>
        public string barCode { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string name { get; set; }

        /// <summary>
        /// 姓名英文
        /// </summary>
        public string enName { get; set; }

        /// <summary>
        /// 民族
        /// </summary>
        public string nationality { get; set; }

        /// <summary>
        /// 性别编码
        /// </summary>
        public string sexCode { get; set; }

        /// <summary>
        /// 所属科室编码
        /// </summary>
        public string belongDeptCode { get; set; }

        /// <summary>
        /// 所属科室名称
        /// </summary>
        public string belongDeptName { get; set; }

        /// <summary>
        /// 病区编码
        /// </summary>
        public string inpatientAreaCode { get; set; }

        /// <summary>
        /// 病区名称
        /// </summary>
        public string inpatientAreaName { get; set; }

        /// <summary>
        /// 就诊类型
        /// </summary>
        public string visitType { get; set; }

        /// <summary>
        /// 身高（单位cm）
        /// </summary>
        public string height { get; set; }

        /// <summary>
        /// 体重（单位kg）
        /// </summary>
        public string weight { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public string age { get; set; }

        /// <summary>
        /// 床号
        /// </summary>
        public string bedNo { get; set; }

        /// <summary>
        /// 病房编码
        /// </summary>
        public string bedroomCode { get; set; }

        /// <summary>
        /// 病房名称
        /// </summary>
        public string bedroomName { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        public string birthday { get; set; }

        /// <summary>
        /// RH血型编码
        /// </summary>
        public string bloodRhTypeCode { get; set; }

        /// <summary>
        /// RH血型名称
        /// </summary>
        public string bloodRhTypeName { get; set; }

        /// <summary>
        /// 血型编码
        /// </summary>
        public string bloodTypeCode { get; set; }

        /// <summary>
        /// 血型名称
        /// </summary>
        public string bloodTypeName { get; set; }

        /// <summary>
        /// 证件号码
        /// </summary>
        public string idNo { get; set; }

        /// <summary>
        /// 证件类型编码
        /// </summary>
        public string cardTypeCode { get; set; }

        /// <summary>
        /// 证件类型名称
        /// </summary>
        public string cardTypeName { get; set; }

        /// <summary>
        /// 入院日期
        /// </summary>
        public string inpatientDate { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string phone { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string addr { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string linkMan { get; set; }

        /// <summary>
        /// 联系人电话
        /// </summary>
        public string linkTel { get; set; }

        /// <summary>
        /// Empi
        /// </summary>
        public string empi { get; set; }

        /// <summary>
        /// 就诊次数
        /// </summary>
        public string visitTime { get; set; }

        public List<OperTypeList> operTypeList { get; set; }
        public List<BeforeDiagList> beforeDiagList { get; set; }
        public List<OperDocList> operDocList { get; set; }
    }

    public class BeforeDiagList
    {
        /// <summary>
        /// 术前诊断编码
        /// </summary>
        public string diagnosisCode { get; set; }

        /// <summary>
        /// 术前诊断名称
        /// </summary>
        public string diagnosisName { get; set; }

        /// <summary>
        /// 诊断前缀名称
        /// </summary>
        public string prefixName { get; set; }

        /// <summary>
        /// 诊断后缀名称
        /// </summary>
        public string suffixName { get; set; }
    }

    public class OperDocList
    {
        /// <summary>
        /// 人员编码
        /// </summary>
        public string personCode { get; set; }

        /// <summary>
        /// 人员名称
        /// </summary>
        public string personName { get; set; }

        /// <summary>
        /// 人员类别编码
        /// </summary>
        public string personTypeCode { get; set; }

        /// <summary>
        /// 人员类别名称
        /// </summary>
        public string personTypeName { get; set; }
    }

    public class OperTypeList
    {
        /// <summary>
        /// 级别编码
        /// </summary>
        public string levelCode { get; set; }

        /// <summary>
        /// 级别名称
        /// </summary>
        public string levelName { get; set; }

        /// <summary>
        /// 手术编码
        /// </summary>
        public string operationCode { get; set; }

        /// <summary>
        /// 手术名称
        /// </summary>
        public string operationName { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public string sort { get; set; }

        /// <summary>
        /// 前缀名称
        /// </summary>
        public string prefixName { get; set; }

        /// <summary>
        /// 后缀名称
        /// </summary>
        public string suffixName { get; set; }
    }
}