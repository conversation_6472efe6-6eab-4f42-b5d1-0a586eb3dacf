﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Warn"
      internalLogFile="internal-nlog.txt">

  <!--define various log targets-->
  <targets>

    <!--write logs to file-->
 
    <target xsi:type="File" name="allfile" fileName="${basedir}/logs/all/nlog-all-${shortdate}.log"
                 layout="${threadid}|${longdate}|${logger}|${uppercase:${level}}|${message} ${exception}"
                  maxArchiveFiles="10"
                  archiveAboveSize="1048576"
                  archiveEvery="Day" />

     <target xsi:type="File" name="nlog-my" fileName="${basedir}/logs/my/nlog-my-${shortdate}.log"
                      layout="${threadid}|${longdate}|${logger}|${uppercase:${level}}|${message} ${exception}"
                     maxArchiveFiles="10"
                     archiveAboveSize="1048576"
                     archiveEvery="Day" />
     <target xsi:type="File" name="nlog-error" fileName="${basedir}/logs/nlog-error-${shortdate}.log"
                     layout="${threadid}|${longdate}|${logger}|${uppercase:${level}}|${message} ${exception}" 
                     maxArchiveFiles="10"
                     archiveAboveSize="1048576"
                     archiveEvery="Day" />
    
    <target xsi:type="File" name="nlog-warn" fileName="${basedir}/logs/nlog-warn-${shortdate}.log"
                     layout="${threadid}|${longdate}|${logger}|${uppercase:${level}}|${message} ${exception}" 
                     maxArchiveFiles="10"
                     archiveAboveSize="1048576"
                     archiveEvery="Day" />

  </targets>

  <rules>
    <!--All logs, including from Microsoft-->
     <logger name="*" minlevel="Warn"   writeTo="allfile" />

    <!--Skip Microsoft logs and so log only own logs-->
   
    <logger name="Medical.*" level="Info"  writeTo="nlog-my" />

    <logger name="Medical.*"  level="Error" writeTo="nlog-error" />

  </rules>
</nlog>