﻿using System.Xml.Serialization;

namespace XianYangInterconnect.ViewModels
{
    [XmlRoot("REPC_IN004014UV", Namespace = "urn:hl7-org:v3")]
    public class RiskAssessmentMessageView
    {
        [XmlAttribute("ITSVersion")]
        public string ITSVersion { get; set; } = "XML_1.0";

        [XmlAttribute(AttributeName = "schemaLocation", Namespace = "http://www.w3.org/2001/XMLSchema-instance")]
        public string SchemaLocation { get; set; } = "urn:hl7-org:v3 ../-multicacheschemas-/REPC_IN004014UV.xsd";

        [XmlElement("id")]
        public Id MessageId { get; set; }

        [XmlElement("creationTime")]
        public CreationTime CreationTime { get; set; }

        [XmlElement("interactionId")]
        public InteractionId InteractionId { get; set; }

        [XmlElement("processingCode")]
        public ProcessingCode ProcessingCode { get; set; }

        [XmlElement("processingModeCode")]
        public ProcessingModeCode ProcessingModeCode { get; set; }

        [XmlElement("acceptAckCode")]
        public AcceptAckCode AcceptAckCode { get; set; }

        [XmlElement("receiver")]
        public Receiver Receiver { get; set; }

        [XmlElement("sender")]
        public Sender Sender { get; set; }

        [XmlElement("controlActProcess")]
        public ControlActProcess ControlActProcess { get; set; }
    }

    public class Id
    {
        [XmlAttribute("root")]
        public string Root { get; set; }

        [XmlAttribute("extension")]
        public string Extension { get; set; }
    }

    public class CreationTime
    {
        [XmlAttribute("value")]
        public string Value { get; set; }
    }

    public class InteractionId
    {
        [XmlAttribute("root")]
        public string Root { get; set; }

        [XmlAttribute("extension")]
        public string Extension { get; set; }
    }

    public class ProcessingCode
    {
        [XmlAttribute("code")]
        public string Code { get; set; }
    }

    public class ProcessingModeCode
    {
        [XmlAttribute("code")]
        public string Code { get; set; }
    }

    public class AcceptAckCode
    {
        [XmlAttribute("code")]
        public string Code { get; set; }
    }

    public class Device
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("determinerCode")]
        public string DeterminerCode { get; set; }

        [XmlElement("id")]
        public IdContainer Id { get; set; }
    }

    public class IdContainer
    {
        [XmlElement("item")]
        public Id Item { get; set; }
    }

    public class Receiver
    {
        [XmlAttribute("typeCode")]
        public string TypeCode { get; set; }

        [XmlElement("device")]
        public Device Device { get; set; }
    }

    public class Sender
    {
        [XmlAttribute("typeCode")]
        public string TypeCode { get; set; }

        [XmlElement("device")]
        public Device Device { get; set; }
    }

    public class ControlActProcess
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("moodCode")]
        public string MoodCode { get; set; }

        [XmlElement("authorOrPerformer")]
        public AuthorOrPerformer AuthorOrPerformer { get; set; }

        [XmlElement("subject")]
        public Subject Subject { get; set; }
    }

    public class AuthorOrPerformer
    {
        [XmlAttribute("typeCode")]
        public string TypeCode { get; set; }

        [XmlElement("assignedDevice")]
        public AssignedDevice AssignedDevice { get; set; }
    }

    public class AssignedDevice
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlElement("assignedDevice")]
        public AssignedDeviceInner AssignedDeviceInner { get; set; }

        [XmlElement("representedOrganization")]
        public RepresentedOrganization RepresentedOrganization { get; set; }
    }

    public class AssignedDeviceInner
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("determinerCode")]
        public string DeterminerCode { get; set; }

        [XmlElement("asLocatedEntity")]
        public AsLocatedEntity AsLocatedEntity { get; set; }
    }

    public class AsLocatedEntity
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlElement("id")]
        public IdContainer Id { get; set; }

        [XmlElement("location")]
        public Location Location { get; set; }
    }

    public class Location
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("determinerCode")]
        public string DeterminerCode { get; set; }

        [XmlElement("name")]
        public Name Name { get; set; }
    }

    public class Name
    {
        [XmlAttribute(AttributeName = "type", Namespace = "http://www.w3.org/2001/XMLSchema-instance")]
        public string Type { get; set; }

        [XmlElement("item")]
        public NameItem Item { get; set; }
    }

    public class NameItem
    {
        [XmlElement("part")]
        public Part Part { get; set; }
    }

    public class Part
    {
        [XmlAttribute("value")]
        public string Value { get; set; }
    }

    public class RepresentedOrganization
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("determinerCode")]
        public string DeterminerCode { get; set; }

        [XmlElement("id")]
        public IdContainer Id { get; set; }

        [XmlElement("name")]
        public Name Name { get; set; }
    }

    public class Subject
    {
        [XmlAttribute("typeCode")]
        public string TypeCode { get; set; }

        [XmlElement("registrationEvent")]
        public RegistrationEvent RegistrationEvent { get; set; }
    }

    public class RegistrationEvent
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("moodCode")]
        public string MoodCode { get; set; }

        [XmlElement("statusCode")]
        public StatusCode StatusCode { get; set; }

        [XmlElement("custodian")]
        public Custodian Custodian { get; set; }

        [XmlElement("subject2")]
        public Subject2 Subject2 { get; set; }
    }

    public class StatusCode
    {
        [XmlAttribute("code")]
        public string Code { get; set; }
    }

    public class Subject2
    {
        [XmlAttribute("typeCode")]
        public string TypeCode { get; set; }

        [XmlElement("careProvisionEvent")]
        public CareProvisionEvent CareProvisionEvent { get; set; }
    }

    public class CareProvisionEvent
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("moodCode")]
        public string MoodCode { get; set; }

        [XmlElement("author")]
        public Author Author { get; set; }

        [XmlElement("pertinentInformation2")]
        public PertinentInformation2 PertinentInformation2 { get; set; }

        [XmlElement("component3")]
        public Component3 Component3 { get; set; }
    }

    public class Author
    {
        [XmlAttribute("typeCode")]
        public string TypeCode { get; set; }

        [XmlElement("noteText")]
        public NoteText NoteText { get; set; }

        [XmlElement("time")]
        public Time Time { get; set; }

        [XmlElement("assignedParty")]
        public AssignedParty AssignedParty { get; set; }
    }

    public class NoteText
    {
        [XmlAttribute("value")]
        public string Value { get; set; }
    }

    public class Time
    {
        [XmlAttribute("value")]
        public string Value { get; set; }
    }

    public class AssignedParty
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlElement("id")]
        public IdContainer Id { get; set; }

        [XmlElement("assignedPerson")]
        public AssignedPerson AssignedPerson { get; set; }

        [XmlElement("representedOrganization")]
        public RepresentedOrganization RepresentedOrganization { get; set; }
    }

    public class AssignedPerson
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("determinerCode")]
        public string DeterminerCode { get; set; }

        [XmlElement("name")]
        public Name Name { get; set; }
    }

    public class PertinentInformation2
    {
        [XmlAttribute("typeCode")]
        public string TypeCode { get; set; }

        [XmlElement("encounter")]
        public Encounter Encounter { get; set; }
    }

    public class Encounter
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("moodCode")]
        public string MoodCode { get; set; }

        [XmlElement("id")]
        public IdContainer Id { get; set; }

        [XmlElement("code")]
        public Code Code { get; set; }

        [XmlElement("lengthOfStayQuantity")]
        public LengthOfStayQuantity LengthOfStayQuantity { get; set; }

        [XmlElement("recordTarget")]
        public RecordTarget RecordTarget { get; set; }

        [XmlElement("location")]
        public EncounterLocation Location { get; set; }
    }

    public class Code
    {
        [XmlAttribute("codeSystem")]
        public string CodeSystem { get; set; }

        [XmlAttribute("code")]
        public string CodeValue { get; set; }

        [XmlAttribute("codeSystemName")]
        public string CodeSystemName { get; set; }

        [XmlElement("displayName")]
        public DisplayName DisplayName { get; set; }
    }

    public class DisplayName
    {
        [XmlAttribute("value")]
        public string Value { get; set; }
    }

    public class LengthOfStayQuantity
    {
        [XmlAttribute("unit")]
        public string Unit { get; set; }

        [XmlAttribute("value")]
        public string Value { get; set; }
    }

    public class RecordTarget
    {
        [XmlAttribute("typeCode")]
        public string TypeCode { get; set; }

        [XmlElement("patient")]
        public Patient Patient { get; set; }
    }

    public class Patient
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlElement("id")]
        public IdContainer Id { get; set; }

        [XmlElement("statusCode")]
        public StatusCode StatusCode { get; set; }

        [XmlElement("patientPerson")]
        public PatientPerson PatientPerson { get; set; }

        [XmlElement("providerOrganization")]
        public ProviderOrganization ProviderOrganization { get; set; }
    }

    public class PatientPerson
    {
        [XmlElement("name")]
        public Name Name { get; set; }
    }

    public class ProviderOrganization
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("determinerCode")]
        public string DeterminerCode { get; set; }

        [XmlElement("id")]
        public Id Id { get; set; }
    }

    public class EncounterLocation
    {
        [XmlAttribute("typeCode")]
        public string TypeCode { get; set; }

        [XmlElement("healthCareFacility")]
        public HealthCareFacility HealthCareFacility { get; set; }
    }

    public class HealthCareFacility
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlElement("id")]
        public Id Id { get; set; }
    }

    public class Component3
    {
        [XmlElement("statementCollectorActList")]
        public StatementCollectorActList StatementCollectorActList { get; set; }
    }

    public class StatementCollectorActList
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("moodCode")]
        public string MoodCode { get; set; }

        [XmlElement("component")]
        public List<Component> Components { get; set; }
    }

    public class Component
    {
        [XmlElement("organizer")]
        public Organizer Organizer { get; set; }
    }

    public class Organizer
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("moodCode")]
        public string MoodCode { get; set; }

        [XmlElement("component")]
        public List<OrganizerComponent> Components { get; set; }
    }

    public class OrganizerComponent
    {
        [XmlAttribute("typeCode")]
        public string TypeCode { get; set; }

        [XmlElement("observation")]
        public Observation Observation { get; set; }
    }

    public class Observation
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlAttribute("moodCode")]
        public string MoodCode { get; set; }

        [XmlElement("code")]
        public Code Code { get; set; }

        [XmlElement("value")]
        public Value Value { get; set; }
    }

    public class Value
    {
        [XmlAttribute(AttributeName = "type", Namespace = "http://www.w3.org/2001/XMLSchema-instance")]
        public string Type { get; set; }

        [XmlAttribute("value")]
        public string ValueContent { get; set; }
    }

    public class Custodian
    {
        [XmlAttribute("typeCode")]
        public string TypeCode { get; set; }

        [XmlElement("assignedEntity")]
        public AssignedEntity AssignedEntity { get; set; }
    }

    public class AssignedEntity
    {
        [XmlAttribute("classCode")]
        public string ClassCode { get; set; }

        [XmlElement("id")]
        public Id Id { get; set; }
    }
}