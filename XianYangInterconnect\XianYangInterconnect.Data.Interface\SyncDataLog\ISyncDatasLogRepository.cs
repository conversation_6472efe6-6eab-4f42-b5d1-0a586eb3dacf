﻿using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Interface
{
    public interface ISyncDatasLogRepository
    {
        /// <summary>
        /// 根据分类获取没有同步数据的集合
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        Task<List<int>> GetSyncDataByDataType(string hospitalID, string syncDataType);
        /// <summary>
        /// 根据分类和事件名获取没有同步数据的集合
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <param name="eventName"></param>
        /// <returns></returns>
        Task<List<int>> GetSyncDataByTypeAndEvent(string hospitalID, string syncDataType, List<string> eventName);

        /// <summary>
        /// 根据ID获取需要同步的Json数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<SyncDataLogInfo> GetSyncDataByID(int id);
        /// <summary>
        /// 根据syncDataType获取需要同步的Json数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        Task<List<SyncDataLogInfo>> GetSyncDataBySyncDataType(string hospitalID, string syncDataType);
        /// <summary>
        /// 获取多条需要同步的Json数据
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<Dictionary<int, string>> GetIDToSyncDataByIDs(List<int> ids);
        /// <summary>
        /// 根据事件类别集合获取数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataTypes"></param>
        /// <returns></returns>
        Task<List<int>> GetSyncDataByTypeList(string hospitalID, List<string> syncDataTypes);
        /// <summary>
        /// 根据数据类型获取数据
        /// </summary>
        /// <param name="hospitalID">医院编码</param>
        /// <param name="syncDataType">同步数据类型</param>
        /// <returns></returns>
        Task<List<SyncDataLogInfo>> GetSyncDatasByDataType(string hospitalID, string syncDataType);
        /// <summary>
        ///根据数据类型获取未抽取数据，或者新增时间在数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        Task<List<SyncDataLogInfo>> GetSyncDatasByDate(string hospitalID, string syncDataType);

        /// <summary>
        /// 获取ID(获取指定时间内的数据)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        Task<List<int>> GetIDsByDataType(string hospitalID, string syncDataType, int minute);
    }
}