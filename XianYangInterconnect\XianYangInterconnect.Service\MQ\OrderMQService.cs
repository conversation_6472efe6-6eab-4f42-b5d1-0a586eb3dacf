﻿using Microsoft.Extensions.Caching.Memory;
using Newtonsoft.Json;
using NLog;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    public class OrderMQService: IPatientOrderMQService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IHISPatientOrderRepository _hisPatientOrderRepository;
        private readonly IMemoryCache _memoryCache;
        private readonly MemoryCacheCommon _memoryCacheCommon;
        private readonly DataOutContext _dataOutContext;
        public OrderMQService(IMemoryCache memoryCache
            , MemoryCacheCommon memoryCacheCommon
            , IHISPatientOrderRepository hisPatientOrderRepository
            , DataOutContext dataOutContext)
        {
            _memoryCache = memoryCache;
            _memoryCacheCommon = memoryCacheCommon;
            _hisPatientOrderRepository = hisPatientOrderRepository;
            _dataOutContext = dataOutContext;
        }

        public async Task<bool> SetPatientOrderConfirmMQ(int minute)
        {
            var syncComboNos = new List<string>();
            var memoryCachKey = "Patient_ConfirmComboNos";
            var patientComboNoViews = await _hisPatientOrderRepository.GetByConfirmDateTime(minute);
            var comboNos = patientComboNoViews.Select(m => m.ComboNo).Distinct().ToList();
            if (comboNos.Count == 0)
            {
                _logger.Info($"SetPatientOrderConfirmMQ没有获取到医嘱审核数据");
                return true;
            }
            syncComboNos = GetSyncComboNo(memoryCachKey, comboNos);
            if (syncComboNos.Count <= 0)
            {
                return true;
            }
            patientComboNoViews = patientComboNoViews.Where(m => syncComboNos.Contains(m.ComboNo)).ToList();
            ///写MQ消息
            var resultFlag = await SetPatientOrderComboNoMQ(patientComboNoViews);
            if (!resultFlag)
            {
                return false;
            }
            _memoryCacheCommon.SetCacheData(memoryCachKey, comboNos, TimeSpan.FromMinutes(30));
            return true;
        }

        /// <summary>
        /// 发送医嘱审核MQ消息
        /// </summary>
        /// <param name="casenumber"></param>
        /// <returns></returns>
        private async Task<bool> SetPatientOrderComboNoMQ(List<HISPatientOrderNoView> hISPatientOrderNoViews)
        {
            foreach (var item in hISPatientOrderNoViews)
            {
                var syncData = ListToJson.ToJson(GetMessageView(item));
                var syncDataLogInfo = new SyncDataLogInfo
                {
                    HospitalID = "8",
                    SyncDataType = "PatientOrder",
                    EventName = "SHZHY_HLCCC_regOrderOpenReview",
                    SyncData = syncData,
                    CaseNumber = item.Casenumber,
                    AddDate = DateTime.Now,
                    ModifyDate = DateTime.Now,
                    ModifyPersonID = "Interconnect",
                    AddPersonID = "Interconnect",
                    DeleteFlag = "",
                    DataPumpFlag = "",
                    Counts = 0
                };
                await _dataOutContext.AddAsync(syncDataLogInfo);
            }
            try
            {
                //await _dataOutContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.Error("新增数据同步记录错误" + ex.ToString());
                return false;
            }
            return true;
        }

        /// <summary>
        /// 创建消息对象
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        private MQMessageView GetMessageView(HISPatientOrderNoView item)
        {
            return new MQMessageView()
            {
                MessageID = "",
                HospitalID = "8",
                CreationTime = DateTime.Now,
                MessageType = "PatientOrder",
                EventName = "SHZHY_HLCCC_regOrderOpenReview",
                CaseNumber = item.Casenumber,
                MessageData = new InPatientOrderView() { ComboNo = item.ComboNo, }
            };
        }

        /// <summary>
        /// 获取需要同步的 
        /// </summary>
        /// <param name="key"></param>
        /// <param name="Execsqn">执行单流水号</param>
        /// <returns></returns>
        private List<string> GetSyncComboNo(string key, List<string> comboNos)
        {
            //获取缓存中存储的Key
            var datas = _memoryCache.Get(key);
            if (datas == null)
            {
                return comboNos;
            }
            string json = JsonConvert.SerializeObject(datas);
            var cacheComboNos = ListToJson.ToList<List<string>>(json);
            comboNos = comboNos.Where(m => !cacheComboNos.Contains(m)).ToList();
            return comboNos;
        }
    }
}
