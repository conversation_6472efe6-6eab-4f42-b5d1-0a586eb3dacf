﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Models.HIS;

namespace XianYangInterconnect.Data
{
    public class HISDbContext : DbContext
    {
        public HISDbContext(DbContextOptions<HISDbContext> options)
         : base(options)
        { }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            builder.Entity<BYJReturnDataInfo>().<PERSON><PERSON><PERSON>(t => new { t.APPLY_NUMBER });
            builder.Entity<BYJDrugExec>().<PERSON><PERSON><PERSON>(t => new { t.EXEC_SQN });
            builder.Entity<ExecDrug>().<PERSON><PERSON><PERSON>(t => new { t.ExecSqn });
            builder.Entity<HISPatientOrderView>().<PERSON><PERSON><PERSON>(t => new { t.InpatientNo, t.OrderId });
        }

        /// <summary>
        /// HIS医嘱
        /// </summary>
        public DbSet<HISPatientOrderView>  HISPatientOrderViews { get; set; }

        /// <summary>
        /// 口服药摆药机
        /// </summary>
        public DbSet<BYJReturnDataInfo> BYJReturnDatas { get; set; }

        /// <summary>
        /// 口服药预执行记录
        /// </summary>
        public DbSet<BYJDrugExec> BYJDrugExecs { get; set; }

        /// <summary>
        /// 口服药预执行记录
        /// </summary>
        public DbSet<ExecDrug> ExecDrugs { get; set; }
    }
}