﻿namespace XianYangInterconnect.ViewModels
{
    public class PatientOrderView
    {
        /// <summary>
        /// 成功码
        /// </summary>
        public int code { get; set; }
        /// <summary>
        /// 信息
        /// </summary>
        public string msg { get; set; }
        /// <summary>
        /// 医嘱数据
        /// </summary>
        public List<PatientOrder> data { get; set; }
    }
    public class PatientOrder
    {
        /// <summary>
        /// 住院流水号
        /// </summary>
        public string inpatientNo { get; set; }
        
        /// <summary>
        /// 住院号
        /// </summary>
        public string patientNo { get; set; }
        /// <summary>
        /// 医嘱流水号
        /// </summary>
        public string orderId { get; set; }
        /// <summary>
        /// 医嘱名称
        /// </summary>
        public string orderName { get; set; }
       
        /// <summary>
        /// 医嘱组合号
        /// </summary>
        public string comboNo { get; set; }       
        /// <summary>
        /// 医嘱类型
        /// </summary>
        public string decmpsFlag { get; set; }
        /// <summary>
        /// 住院医嘱状态 10 待审核  20已提交 30 已接收 40已执行 50 已完成 90 停止作废
        /// </summary>
        public string moState { get; set; }

        /// <summary>
        /// 术语类型编码
        /// </summary>
        public string termClass { get; set; }
        /// <summary>
        /// 术语类型名称
        /// </summary>
        public string termClassName { get; set; }
        /// <summary>
        /// 术语编码/药品代码
        /// </summary>
        public string termId { get; set; }
        
       
        /// <summary>
        /// 开始时间
        /// </summary>
        public string beginDate { get; set; }
     
        /// <summary>
        /// 开立时间
        /// </summary>
        public string moDate { get; set; }
        /// <summary>
        /// 开立医师ID
        /// </summary>
        public string moDoc { get; set; }
        /// <summary>
        /// 开立科室ID
        /// </summary>
        public string moDept { get; set; }
       
        /// <summary>
        /// 每次量/剂量
        /// </summary>
        public decimal? doseOnce { get; set; }
        /// <summary>
        /// 每次量单位
        /// </summary>
        public string doseUnit { get; set; }
        /// <summary>
        /// 频次编码
        /// </summary>
        public string frequencyCode { get; set; }
       
        /// <summary>
        /// 给药途径名称
        /// </summary>
        public string usageName { get; set; }
       
        /// <summary>
        /// 作废医生编码
        /// </summary>
        public string cancelDoc { get; set; }
    
        /// <summary>
        /// 作废时间
        /// </summary>
        public string cancelDate { get; set; }
        /// <summary>
        /// 作废原因
        /// </summary>
        public string cancelReason { get; set; }
        /// <summary>
        /// 审核护士编码
        /// </summary>
        public string confirmNurseCod { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string memo { get; set; }
        ///// <summary>
        ///// 执行人编码
        ///// </summary>
        //public string execOperCode { get; set; }
        ///// <summary>
        ///// 执行时间
        ///// </summary>
        //public string execOperTime { get; set; }
        ///// <summary>
        ///// 医保待遇标识
        ///// </summary>
        //public string siFlag { get; set; }
        ///// <summary>
        ///// 药品性质编码
        ///// </summary>
        //public string drugQuality { get; set; }
        ///// <summary>
        ///// 药品性质名称
        ///// </summary>
        //public string drugQualityName { get; set; }
       
        ///// <summary>
        ///// 外部申请单条码号
        ///// </summary>
        //public string applyNo { get; set; }
        ///// <summary>
        ///// 规格
        ///// </summary>
        //public string specs { get; set; }
        ///// <summary>
        ///// 付数/天数
        ///// </summary>
        //public int? days { get; set; }
        ///// <summary>
        ///// 总量
        ///// </summary>
        //public decimal? totQty { get; set; }
        ///// <summary>
        ///// 总量单位等级
        ///// </summary>
        //public string totUnitLv { get; set; }
        ///// <summary>
        ///// 取药药房编码
        ///// </summary>
        //public string drugstore { get; set; }
        ///// <summary>
        ///// 取药药房编码
        ///// </summary>
        //public string drugstoreName { get; set; }
        ///// <summary>
        ///// 不规则频次
        ///// </summary>
        //public string execTimes { get; set; }
        ///// <summary>
        ///// 不均等给药量
        ///// </summary>
        //public string execDoses { get; set; }
        ///// <summary>
        ///// 是否需皮试
        ///// </summary>
        //public string needHypoFlag { get; set; }
        ///// <summary>
        ///// 皮试结果
        ///// </summary>
        //public string hypoResult { get; set; }
        ///// <summary>
        ///// 抗生素使用类型
        ///// </summary>
        //public string antibioticType { get; set; }
        ///// <summary>
        ///// 院注次数
        ///// </summary>
        //public string injectHos { get; set; }
        ///// <summary>
        ///// 草药特殊煎制法编码
        ///// </summary>
        //public string herbProcess { get; set; }
        ///// <summary>
        ///// 特殊煎制法名称
        ///// </summary>
        //public string herbProcessName { get; set; }
        ///// <summary>
        ///// 草药药方名称
        ///// </summary>
        //public string herbRecipeName { get; set; }
        ///// <summary>
        ///// 检查部位编码
        ///// </summary>
        //public string examPart { get; set; }
        ///// <summary>
        ///// 检查部位名称
        ///// </summary>
        //public string examPartName { get; set; }
        ///// <summary>
        ///// 是否加急
        ///// </summary>
        //public string emcFlag { get; set; }
        ///// <summary>
        ///// 检验送检样品编码
        ///// </summary>
        //public string labSpecimen { get; set; }
        ///// <summary>
        ///// 送检样本名称
        ///// </summary>
        //public string labSpecimenName { get; set; }
        ///// <summary>
        ///// 检验采样部位编码
        ///// </summary>
        //public string labPart { get; set; }
        ///// <summary>
        ///// 采样部位名称
        ///// </summary>
        //public string labPartName { get; set; }
        ///// <summary>
        ///// 组使用序号
        ///// </summary>
        //public string comboExecSort { get; set; }
        ///// <summary>
        ///// 组内顺序号
        ///// </summary>
        //public int? comboSeq { get; set; }
        ///// <summary>
        ///// 顺序号
        ///// </summary>
        //public string sortNo { get; set; }
        ///// <summary>
        ///// 患者ID patientid/卡号cardNo/病人ID
        ///// </summary>
        //public string inPatientId { get; set; }
        ///// <summary>
        ///// 每次量单位等级
        ///// </summary>
        //public string doseUnitLv { get; set; }
        ///// <summary>
        ///// 自定义每次量
        ///// </summary>
        //public string customDose { get; set; }

        ///// <summary>
        ///// 频次名称
        ///// </summary>
        //public string frequencyName { get; set; }
        ///// <summary>
        ///// 给药途径编码
        ///// </summary>
        //public string usageCode { get; set; }
        ///// <summary>
        ///// 作废人名称
        ///// </summary>
        //public string cancelDocName { get; set; }
        ///// <summary>
        ///// 开立医生ID
        ///// </summary>
        //public string moDocName { get; set; }

        ///// <summary>
        ///// 开立科室名称
        ///// </summary>
        //public string moDeptName { get; set; }
        ///// <summary>
        ///// 首日量
        ///// </summary>
        //public string firstAdds { get; set; }
        ///// <summary>
        ///// 执行科室名称
        ///// </summary>
        //public string execDeptName { get; set; }
        ///// <summary>
        ///// 参考金额
        ///// </summary>
        //public decimal? costRef { get; set; }
        ///// <summary>
        ///// 是否嘱托类医嘱
        ///// </summary>
        //public string discribeFlag { get; set; }
        ///// <summary>
        ///// 术语名称/药品名称
        ///// </summary>
        //public string termName { get; set; }
        ///// <summary>
        ///// 执行科室ID
        ///// </summary>
        //public string execDept { get; set; }
        ///// <summary>
        ///// 项目类型
        ///// </summary>
        //public string itemType { get; set; }
    }
}
