﻿using XianYangInterconnect.Models.Base;

namespace XianYangInterconnect.ViewModels
{
    public class AppConfigSettingView : ModifyInfo
    {

        /// <summary>
        /// 医疗院所代码
        /// </summary>[
        public string HospitalID { get; set; }

        /// <summary>
        /// 配置类型
        /// </summary>
        public string SettingType { get; set; }

        /// <summary>
        /// 配置码
        /// </summary>
        public string SettingCode { get; set; }

        /// <summary>
        /// 配置值
        /// </summary>
        public string SettingValue { get; set; }

        /// <summary>
        /// API属于那个系统。Medical、External
        /// </summary>
        public string SystemType { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 负责人，填写负责人及联系方式
        /// </summary>
        public string PersonInCharge { get; set; }
        /// <summary>
        ///删除标记
        /// </summary>
        public bool SwitchCode { get; set; }

        /// <summary>
        ///配置ID
        /// </summary>
        public int AppConfigSettingID { get; set; }
    }
}
