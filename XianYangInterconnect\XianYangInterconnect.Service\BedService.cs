﻿using Microsoft.Extensions.Options;
using NLog;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    public class BedService : IBedService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IBedRepository _bedRepository;
        private readonly IOptions<SystemConfig> _config;

        public BedService(IBedRepository bedRepository
            , IOptions<SystemConfig> config)
        {
            _bedRepository = bedRepository;
            _config = config;
        }
        /// <summary>
        ///
        /// </summary>
        /// <returns></returns>
        public async Task<List<BedInfo>> GetAllAsync()
        {
            return await _bedRepository.GetAllAsync();
        }
        /// <summary>
        /// 根据病区代码获取床位数据
        /// </summary>
        /// <param name="stationCode"></param>
        /// <returns></returns>
        public async Task<List<BedInfo>> GetBedListByStationCode(string stationCode)
        {
            if (string.IsNullOrEmpty(stationCode))
            {
                return [];
            }
            var allBedList = await _bedRepository.GetAllAsync();
            var bedList = allBedList.Where(m => m.StationCode == stationCode).ToList();
            if (bedList.Count <= 0)
            {
                return [];
            }
            return bedList;
        }
    }
}