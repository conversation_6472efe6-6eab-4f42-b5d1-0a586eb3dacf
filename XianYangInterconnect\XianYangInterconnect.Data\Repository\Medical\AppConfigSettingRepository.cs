﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using XianYangInterconnect.Common;
using XianYangInterconnect.Common.SessionCommon;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Repository
{
    public class AppConfigSettingRepository : IAppConfigSettingRepository
    {
        private readonly MedicalContext _medicalContext = null;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly GetCacheService _getCacheService;
        public AppConfigSettingRepository(MedicalContext medicalContext, IMemoryCache memoryCache, SessionCommonServer sessionCommonServer, GetCacheService getCacheService)
        {
            _medicalContext = medicalContext;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
            _memoryCache = memoryCache;
            _getCacheService = getCacheService;
        }

        public async Task<string> GetConfigSettingValue(string settingType, string settingCode)
        {
            var settings = (List<AppConfigSettingInfo>)await GetCacheAsync();
            if (settings.Count == 0)
            {
                return "";
            }

            var returnData = settings.Find(m => m.SettingType == settingType && m.SettingCode == settingCode);

            if (returnData == null)
            {
                return "";
            }
            return returnData.SettingValue;
        }


        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<AppConfigSettingInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            dict.TryGetValue("hospitalID", out var hospitalID);
            object data = null;
            if (hospitalID == null)
            {
                return data;
            }
            return await _medicalContext.AppConfigSettingInfos.Where(m => m.HospitalID == hospitalID.ToString() && m.DeleteFlag != "*").ToListAsync();
        }
        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.AppConfigSetting.GetKey(_sessionCommonServer);
        }
    }
}
