﻿namespace XianYangInterconnect.Service.Interface
{
    public interface IPatientOperationService
    {
        /// <summary>
        /// 同步患者手术信息(MQ调用)
        /// </summary>
        /// <returns></returns>
        Task<bool> SyncPatientOperation();

        /// <summary>
        /// 同步患者手术信息
        /// </summary>
        /// <param name="applyNo"></param>
        /// <param name="serviceType"></param>
        /// <param name="modifyPersonID"></param>
        /// <param name="modifyDate"></param>
        /// <returns></returns>
        Task<bool> SyncPatientOperationByApplyNo(string caseNumber, string applyNo, string serviceType, string modifyPersonID, string modifyDate);
    }
}