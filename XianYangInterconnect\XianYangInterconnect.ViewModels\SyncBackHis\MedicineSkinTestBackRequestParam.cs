﻿namespace XianYangInterconnect.ViewModels
{
    public class MedicineSkinTestBackRequestParam
    {
        /// <summary>
        /// 皮试药批次号
        /// </summary>
        public string batchNo {  get; set; }
        /// <summary>
        /// 组合号
        /// </summary>
        public string comboNo { get; set; }
        /// <summary>
        /// 执行单流水号
        /// </summary>
        public string execSqnDrug { get; set; }
        /// <summary>
        /// 执行次数  1.第一次执行；2第二次执行
        /// </summary>
        public string execTimes { get; set; }
        /// <summary>
        /// 皮试等级
        /// </summary>
        public string hypoTestLv { get; set; }
        /// <summary>
        /// 住院流水号
        /// </summary>
        public string inpatientNo { get; set; }
        /// <summary>
        /// 项目类型
        /// </summary>
        public string itemType { get; set; }
        /// <summary>
        /// 执行时间
        /// </summary>
        public string operExecTime { get; set; }
        /// <summary>
        /// 操作护士编码
        /// </summary>
        public string operNurCode { get; set; }
        /// <summary>
        /// 操作护士姓名
        /// </summary>
        public string operNurName { get; set; }
        /// <summary>
        /// 操作类型 1.执行（签名）；2取消
        /// </summary>
        public string operType { get; set; }
        /// <summary>
        /// 皮试结果，6阳性、5阴性
        /// </summary>
        public string psResult { get; set; }
    }
}
