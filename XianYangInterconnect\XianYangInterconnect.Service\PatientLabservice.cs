﻿using Microsoft.Extensions.Options;
using NLog;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Data.Interface.Medical;
using XianYangInterconnect.Models;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Services.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{

    public class PatientLabservice : IPatientLabservice
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IStationListRepository _stationListRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IInpatientDataRepository _inpatientDataRepository;
        private readonly IBedListRepository _bedListRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IRequestApiService _requestApiService;
        private readonly IOptions<SystemConfig> _config;
        private readonly IAPISettingRepository _aPISettingRepository;
        private readonly ISyncDatasLogRepository _syncDatasLogRepository;
        private readonly ISyncDatasLogServices _syncDatasLogServices;
        private readonly ISettingDescriptionRepository _settingDescriptionRepository;
        private readonly IStationGroupListRepository _stationGroupListRepository;
        private readonly IPatientBasicDataRepository _patientBasicDataRepository;

        public PatientLabservice(IStationListRepository stationListRepository
          , IAppConfigSettingRepository appConfigSettingRepository
          , IInpatientDataRepository inpatientDataRepository
          , IRequestApiService requestApiService
          , IOptions<SystemConfig> config
          , IAPISettingRepository aPISettingRepository
          , IDepartmentListRepository departmentListRepository
          , IBedListRepository bedListRepository
          , ISyncDatasLogRepository syncDatasLogRepository
          , ISyncDatasLogServices syncDatasLogServices
          , ISettingDescriptionRepository settingDescriptionRepository
          , IStationGroupListRepository stationGroupListRepository
          , IPatientBasicDataRepository patientBasicDataRepository
          )
        {
            _stationListRepository = stationListRepository;
            _departmentListRepository = departmentListRepository;
            _bedListRepository = bedListRepository;
            _inpatientDataRepository = inpatientDataRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _requestApiService = requestApiService;
            _config = config;
            _aPISettingRepository = aPISettingRepository;
            _syncDatasLogRepository = syncDatasLogRepository;
            _syncDatasLogServices = syncDatasLogServices;
            _settingDescriptionRepository = settingDescriptionRepository;
            _stationGroupListRepository = stationGroupListRepository;
            _patientBasicDataRepository = patientBasicDataRepository;
        }

        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";

        /// <summary>
        /// 同步患者门诊检验数据
        /// </summary>
        /// <param name="outPatientID"></param>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientTest(string outPatientID, string caseNumber)
        {
            if (string.IsNullOrEmpty(outPatientID) && string.IsNullOrEmpty(caseNumber))
            {
                _logger.Warn($"获取单个患者检验信息参数错误, outPatientID = {outPatientID},caseNumber = {caseNumber}");
                return false;
            }

            var patientTestReportList = await GetPatientTestReportList(caseNumber, outPatientID);
            if (patientTestReportList == null || patientTestReportList.Count == 0)
            {
                return patientTestReportList == null ? false : true;
            }

            var patientTestResultList = await GetPatientTestResults(caseNumber, patientTestReportList);
            if (patientTestResultList == null)
            {
                return false;
            }

            await _requestApiService.RequestAPI("SyncPatientTestResult", ListToJson.ToJson(patientTestResultList), null);
            return true;
        }

        /// <summary>
        /// 同步检验基本信息(MQ消息队列保存调用)
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <param name="syncDataType"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientLab(string hospitalID)
        {
            SyncDataLogInfo logInfo;
            MessageView messageView;
            bool result = false;
            var logIDs = await _syncDatasLogRepository.GetIDsByDataType(hospitalID, "PatientLab",4);
            foreach (var logID in logIDs)
            {
                logInfo = await _syncDatasLogRepository.GetSyncDataByID(logID);
                if (logInfo == null)
                {
                    await _syncDatasLogServices.ModifySyncDataLog(logID, result);
                    continue;
                }
                if (!logInfo.CaseNumber.Contains("ZY"))
                {
                    //门诊数据不处理（门诊检验数据走新患者同步时调用逻辑）
                    await _syncDatasLogServices.ModifySyncDataLog(logID, true);
                    continue;
                }
                messageView = ListToJson.ToList<MessageView>(logInfo.SyncData);
                if (messageView == null || messageView.InpatientView == null)
                {
                    await _syncDatasLogServices.ModifySyncDataLog(logID, result);
                    continue;
                }
                _logger.Info("MQ发来消息,现在开始同步住院号为:" + messageView.InpatientView.CaseNumber + "的住院信息");
                var patientTestReportView = messageView.PatientTestReportView;
                string reportNo = patientTestReportView?.ReportNo ?? string.Empty;
                if (string.IsNullOrEmpty(reportNo))
                {
                    continue;
                }
                result = await SyncPatientTestByTestCode(reportNo);
                await _syncDatasLogServices.ModifySyncDataLog(logID, result);
            }
            return true;
        }
        /// <summary>
        /// 根据检验编码获取检验信息
        /// </summary>
        /// <param name="testCode"></param>
        /// <returns></returns>
        private async Task<bool> SyncPatientTestByTestCode(string testCode)
        {
            var hisPatientTestResultList = await GetTestResultDetail(testCode);

            if (hisPatientTestResultList == null || hisPatientTestResultList.Count == 0)
            {
                _logger.Warn($"未获取到检验结果详情，testCode: {testCode}");
                return false;
            }
            var inpatient = await _inpatientDataRepository.GetInpatientDataInfo(hisPatientTestResultList[0].visitedId);
            if (inpatient == null)
            {
                _logger.Warn($"未获取到患者信息，visitedId: {hisPatientTestResultList[0].visitedId}");
                return false;
            }
            var patientTestResultList = CreatePatientTestResultList(hisPatientTestResultList);

            if (patientTestResultList.Count > 0)
            {
                try
                {
                    await _requestApiService.RequestAPI("SyncPatientTestResult", ListToJson.ToJson(patientTestResultList), null);
                }
                catch (Exception ex)
                {
                    _logger.Error($"同步患者检验结果失败，testCode: {testCode}, 错误信息: {ex.Message}", ex);
                    return false;
                }
            }

            return true;
        }
        /// <summary>
        /// 创建患者检验结果集合
        /// </summary>
        /// <param name="hisPatientTestResultList"></param>
        /// <returns></returns>
        private static List<PatientTestResultView> CreatePatientTestResultList(List<HISPatientTestResult> hisPatientTestResultList)
        {
            List<PatientTestResultView> patientTestResultList =
            [
                .. hisPatientTestResultList.Select(detailItem => new PatientTestResultView
                {
                    CaseNumber = detailItem.visitedId,
                    Specimen = detailItem.sampleType,
                    SpecimenCode = detailItem.sampleType,
                    TestNo = detailItem.testCode,
                    TestCode = detailItem.itemCode,
                    TestItem = detailItem.itemName,
                    TestValue = detailItem.inspectionResult,
                    Unit = detailItem.inspectionResultUnit,
                    NormalRange = detailItem.inspectionResultRange,
                    TestGroupName = detailItem.comItemName,
                    TestGroupCode = detailItem.comItemCode,
                    NormalAbnormal = detailItem.resultStateClassName switch
                    {
                        "高" => "H",
                        "低" => "L",
                        _ => ""
                    },
                    TestDate = detailItem.reportDate.Value,
                }),
            ];
            return patientTestResultList;
        }

        /// <summary>
        /// 组装检验信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="patientTestReportList"></param>
        /// <returns></returns>
        private async Task<List<PatientTestResultView>> GetPatientTestResults(string caseNumber, List<HISPatientTestReport> patientTestReportList)
        {
            var patientTestResultList = new List<PatientTestResultView>();
            foreach (var item in patientTestReportList)
            {
                var hisPatientTestResultList = await GetTestResultDetail(item.testCode);
                if (hisPatientTestResultList == null || hisPatientTestResultList.Count <= 0) continue;

                var patientTestResults = CreatePatientTestResultList(hisPatientTestResultList);
                if (patientTestResults != null && patientTestResults.Count > 0)
                {
                    patientTestResultList.AddRange(patientTestResults);
                }
            }
            return patientTestResultList;
        }

        /// <summary>
        /// 获取医院检验信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="outPatientID"></param>
        /// <returns></returns>
        private async Task<List<HISPatientTestReport>> GetPatientTestReportList(string caseNumber, string outPatientID)
        {
            var api = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetPatientTestReportAPI");
            var requestParams = CreatePattientTestRequestParameters(caseNumber, outPatientID);

            var requestApi = api + (requestParams.Count > 0 ? "?" + string.Join("&", requestParams) : string.Empty);

            try
            {
                var result = await HttpHelper.HttpGetAsync(requestApi, "application/json", null, false, caseNumber);
                var apiResponse = ListToJson.ToList<ResponseResult>(result);
                if (apiResponse?.Data == null)
                {
                    _logger.Warn($"同步病案号: {outPatientID}, 病历号: {caseNumber} 检验信息数据失败，未获取到相关数据");
                    return new List<HISPatientTestReport>();
                }

                return ListToJson.ToList<List<HISPatientTestReport>>(apiResponse.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error($"同步患者检验信息时发生异常: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 创建检验请求参数
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="outPatientID"></param>
        /// <returns></returns>
        private List<string> CreatePattientTestRequestParameters(string caseNumber, string outPatientID)
        {
            var requestParams = new List<string>();

            if (!string.IsNullOrEmpty(outPatientID))
            {
                requestParams.Add($"patientId={outPatientID}");
            }
            else if (!string.IsNullOrEmpty(caseNumber))
            {
                requestParams.Add($"visitedId={caseNumber}");
            }

            return requestParams;
        }

        /// <summary>
        /// 获取检验明细数据
        /// </summary>
        /// <param name="testCode"></param>
        /// <returns></returns>
        private async Task<List<HISPatientTestResult>> GetTestResultDetail(string testCode)
        {
            try
            {
                var detailApi = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetPatientTestResultAPI");
                var detailResult = await GetDetailResult(detailApi, testCode);
                var detailApiResponse = ListToJson.ToList<ResponseResult>(detailResult.ToString());
                return ListToJson.ToList<List<HISPatientTestResult>>(detailApiResponse.Data.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error($"获取检验详情时发生异常: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 同步患者检验明细信息
        /// </summary>
        /// <param name="detailApi"></param>
        /// <param name="testCode"></param>
        /// <returns></returns>
        private async Task<string> GetDetailResult(string detailApi, string testCode)
        {
            var dict = new Dictionary<string, string> { { "testCode", testCode } };
            var headers = new Dictionary<string, string>
            {
                { "rootId", Guid.NewGuid().ToString("N") },
                { "domain", "SHZHY_HLCCC" },
                { "businessTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                { "operationTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                { "key", "1c09a5c4-d452-4ae1-9f62-7f5a1265fdaa"}
            };
            return await HttpHelper.HttpPostAsync(detailApi, ListToJson.ToJson(dict), "application/json", 30, headers);
        }
    }
}
