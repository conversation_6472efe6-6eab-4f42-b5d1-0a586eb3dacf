﻿using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Interface
{
    public interface IStationGroupListRepository
    {
        /// <summary>
        /// 根据分组编号，获取病区分组字典
        /// </summary>
        /// <param name="GroupID"></param>
        /// <returns></returns>
        Task<List<StationGroupListInfo>> GetStationGroupListByGroupID(int GroupID);

        /// <summary>
        /// 获取所有病区数据
        /// </summary>
        /// <returns></returns>
        Task<List<StationGroupListInfo>> GetStationGroupListAll();
    }
}