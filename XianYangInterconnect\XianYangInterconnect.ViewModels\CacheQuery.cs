﻿using XianYangInterconnect.Common;

namespace XianYangInterconnect.ViewModels
{
    public class CacheQuery
    {
        /// <summary>
        /// 类别
        /// </summary>
        public CacheType Type { get; set; }

        /// <summary>
        /// 查询条件
        /// </summary>
        public dynamic Query { get; set; }

        /// <summary>
        /// 医院代码
        /// </summary>
        public string HospitalID { get; set; }
    }
}