<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
    <DocumentationFile>obj\Debug\net8.0\XianYangInterconnect.API.xml</DocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
    <PackageReference Include="Hangfire" Version="1.8.20" />
    <PackageReference Include="IBMMQDotnetClient" Version="9.4.1" />
    <PackageReference Include="IBMXMSDotnetClient" Version="9.4.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.16" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.10" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.16">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.10" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog" Version="5.2.7" />
    <PackageReference Include="Oracle.EntityFrameworkCore" Version="8.21.140" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\XianYangInterconnect.Common\XianYangInterconnect.Common.csproj" />
    <ProjectReference Include="..\XianYangInterconnect.Data.Interface\XianYangInterconnect.Data.Interface.csproj" />
    <ProjectReference Include="..\XianYangInterconnect.Data\XianYangInterconnect.Data.csproj" />
    <ProjectReference Include="..\XianYangInterconnect.Models\XianYangInterconnect.Models.csproj" />
    <ProjectReference Include="..\XianYangInterconnect.Service.Interface\XianYangInterconnect.Service.Interface.csproj" />
    <ProjectReference Include="..\XianYangInterconnect.Service\XianYangInterconnect.Service.csproj" />
    <ProjectReference Include="..\XianYangInterconnect.ViewModels\XianYangInterconnect.ViewModels.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Com.Neusoft.Nhip.MqProxy">
      <HintPath>..\MqProxy\Com.Neusoft.Nhip.MqProxy.dll</HintPath>
    </Reference>
    <Reference Include="IBM.XMS">
      <HintPath>C:\Windows\assembly\GAC_MSIL\IBM.XMS\9.0.4.0__d2666ab12fca862b\IBM.XMS.dll</HintPath>
    </Reference>
    <Reference Include="IBM.XMS.Admin.Objects">
      <HintPath>..\MqProxy\IBM.XMS.Admin.Objects\9.0.4.0__d2666ab12fca862b\IBM.XMS.Admin.Objects.dll</HintPath>
    </Reference>
    <Reference Include="IBM.XMS.Client.Impl">
      <HintPath>C:\Windows\assembly\GAC_MSIL\IBM.XMS.Client.Impl\9.0.4.0__d2666ab12fca862b\IBM.XMS.Client.Impl.dll</HintPath>
    </Reference>
    <Reference Include="IBM.XMS.Client.WMQ">
      <HintPath>..\MqProxy\IBM.XMS.Client.WMQ\9.0.4.0__d2666ab12fca862b\IBM.XMS.Client.WMQ.dll</HintPath>
    </Reference>
    <Reference Include="IBM.XMS.NLS">
      <HintPath>..\MqProxy\IBM.XMS.NLS\9.0.4.0__d2666ab12fca862b\IBM.XMS.NLS.dll</HintPath>
    </Reference>
    <Reference Include="IBM.XMS.NLS.resources">
      <HintPath>..\MqProxy\IBM.XMS.NLS.resources\9.0.4.0_es_d2666ab12fca862b\IBM.XMS.NLS.resources.dll</HintPath>
    </Reference>
    <Reference Include="IBM.XMS.Provider">
      <HintPath>..\MqProxy\IBM.XMS.Provider\9.0.4.0__d2666ab12fca862b\IBM.XMS.Provider.dll</HintPath>
    </Reference>
    <Reference Include="IBM.XMS.Util">
      <HintPath>..\MqProxy\IBM.XMS.Util\9.0.4.0__d2666ab12fca862b\IBM.XMS.Util.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ProjectExtensions><VisualStudio><UserProperties appsettings_1json__JsonSchema="" /></VisualStudio></ProjectExtensions>
</Project>