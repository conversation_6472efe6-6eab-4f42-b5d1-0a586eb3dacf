﻿using System.ComponentModel.DataAnnotations.Schema;


namespace XianYangInterconnect.Models.HIS
{
    [Serializable]
    [Table("VIEW_HL3C_QUERYORDER", Schema = "XYZXHIS")]
    public partial class HISPatientOrderView
    {
        /// <summary>
        /// 患者住院流水号
        /// </summary>
        [Column("INPATIENTNO", TypeName = "VARCHAR2(20)")]
        public string InpatientNo { get; set; }

        /// <summary>
        /// 病历号
        /// </summary>
        [Column("PATIENTNO", TypeName = "VARCHAR2(20)")]
        public string PatientNo { get; set; }

        /// <summary>
        /// 开立科室ID
        /// </summary>
        [Column("MODEPT", TypeName = "VARCHAR2(20)")]
        public string MoDept { get; set; }
        

        /// <summary>
        /// 医嘱流水号
        /// </summary>
        [Column("ORDERID", TypeName = "VARCHAR2(20)")]
        public string OrderId { get; set; }

        /// <summary>
        /// 医嘱组号
        /// </summary>
        [Column("COMBONO", TypeName = "VARCHAR2(20)")]
        public string ComboNo { get; set; }

        /// <summary>
        /// 医嘱组号
        /// </summary>
        [Column("MOSTATION", TypeName = "VARCHAR2(20)")]
        public string MoStation { get; set; }

        /// <summary>
        /// 医嘱开始日期
        /// </summary>
        [Column("BEGINDATE", TypeName = "DATE")]
        public DateTime BeginDate { get; set; }
        /// <summary>
        /// 医嘱结束日期
        /// </summary>
        [Column("ENDDATE", TypeName = "DATE")]
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 医嘱开立人员工号
        /// </summary>
        [Column("MODOC", TypeName = "VARCHAR2(20)")]
        public string MoDoc { get; set; }

        /// <summary>
        /// 医嘱开立时间
        /// </summary>
        [Column("MODATE", TypeName = "DATE")]
        public DateTime? MoDate { get; set; }

        /// <summary>
        /// 医嘱确认人员工号
        /// </summary>
        [Column("CONFIRMNURSECOD", TypeName = "VARCHAR2(20)")]
        public string ConfirmNurseCod { get; set; }

        /// <summary>
        /// 医嘱确认时间
        /// </summary>
        [Column("CONFIRMDATE", TypeName = "DATE")]
        public DateTime ConfirmDate { get; set; }

        /// <summary>
        /// 医嘱取消人员
        /// </summary>
        [Column("CANCALPERSONID", TypeName = "VARCHAR2(20)")]
        public string CancalPersonID { get; set; }

        /// <summary>
        ///作废原因
        /// </summary>
        [Column("CANCELREASON", TypeName = "VARCHAR2(50)")]
        public string CancelReason { get; set; }
        
        /// <summary>
        /// 医嘱取消时间
        /// </summary>
        [Column("CANCALDATE", TypeName = "DATE")]
        public DateTime? CancalDate { get; set; }

        /// <summary>
        /// 医嘱状态
        /// </summary>
        [Column("MOSTATE", TypeName = "VARCHAR2(4)")]
        public string MoState { get; set; }

        /// <summary>
        /// 医嘱频次
        /// </summary>
        [Column("FREQUENCYCODE", TypeName = "VARCHAR2(100)")]
        public string FrequencyCode { get; set; }

        /// <summary>
        /// 医嘱类别
        /// </summary>
        [Column("TERMCLASS", TypeName = "VARCHAR2(20)")]
        public string TermClass { get; set; }

        /// <summary>
        /// 医嘱类别
        /// </summary>
        [Column("TERMCLASSNAME", TypeName = "VARCHAR2(50)")]
        public string TermClassName { get; set; } 

        /// <summary>
        /// 医嘱在组内的排序
        /// </summary>
        [Column("COMBOSEQ", TypeName = "NUMBER(4)")]
        public int? ComboSeq { get; set; }

        /// <summary>
        /// 医嘱编码
        /// </summary>
        [Column("TERMID", TypeName = "VARCHAR2(20)")]
        public string TermId { get; set; }

        /// <summary>
        /// 医嘱内容
        /// </summary>
        [Column("ORDERNAME", TypeName = "VARCHAR2(500)")]
        public string OrderName { get; set; }

        /// <summary>
        /// 每次执行量/剂量
        /// </summary>
        [Column("DOSEONCE", TypeName = "VARCHAR2(10)")]
        public string DoseOnce { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Column("DOSEUNIT", TypeName = "VARCHAR2(20)")]
        public string DoseUnit { get; set; }

        /// <summary>
        /// 总剂量
        /// </summary>
        [Column("QTYTOT", TypeName = "VARCHAR2(20)")]
        public string QtyTot { get; set; }

        /// <summary>
        /// 使用方法
        /// </summary>
        [Column("USAGENAME", TypeName = "VARCHAR2(50)")]
        public string UsageName { get; set; }

        /// <summary>
        /// 医嘱类型
        /// </summary>
        [Column("DECMPSFLAG", TypeName = "VARCHAR2(10)")]
        public string DecmpsFlag { get; set; }

        /// <summary>
        /// 医嘱说明
        /// </summary>
        [Column("ORDERDESCRIPTION", TypeName = "VARCHAR2(500)")]
        public string OrderDescription { get; set; }

        /// <summary>
        /// 药品禁忌
        /// </summary>
        [Column("DRUGATTENTION", TypeName = "VARCHAR2(100)")]
        public string DrugAttention { get; set; }

        /// <summary>
        /// 药品规格
        /// </summary>
        [Column("SPECS", TypeName = "VARCHAR2(50)")]
        public string Specs { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        [Column("TIMESTAMP", TypeName = "DATE")]
        public DateTime TimeStamp { get; set; }




    }
}
