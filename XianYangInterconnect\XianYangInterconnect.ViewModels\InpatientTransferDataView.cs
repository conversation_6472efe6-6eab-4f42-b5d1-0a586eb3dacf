﻿namespace XianYangInterconnect.ViewModels
{
    public class InpatientTransferDataView
    {
        /// <summary>
        /// 患者住院流水号
        /// </summary>
        public string CaseNumber { get; set; }

        /// <summary>
        /// 患者住院ID
        /// </summary>
        public string InpatientID { get; set; }
        /// <summary>
        /// 转出
        /// </summary>
        public InpatientTransferOutDataView inpatientTransferOutDataView { get; set; }
        /// <summary>
        /// 转入
        /// </summary>
        public InpatientTransferInDataView inpatientTransferInDataView { get; set; }


    }
    public class InpatientTransferInDataView
    {
        /// <summary>
        /// 转入病区ID
        /// </summary>
        public int TransferInStationID { get; set; }
        /// <summary>
        /// 转入科室ID
        /// </summary>
        public int TransferInDepartmentID { get; set; }

        /// <summary>
        /// 转入时间
        /// </summary>
        public DateTime TransferInDatatime { get; set; }
        /// <summary>
        /// 转入床位ID
        /// </summary>
        public int TransferInBedID { get; set; }
        /// <summary>
        /// 转入床号
        /// </summary>
        public string TransferInBedNumber { get; set; }
        /// <summary>
        /// 转入操作员编码
        /// </summary>
        public string TransferInEmployeeID { get; set; }
    }
    public class InpatientTransferOutDataView 
    {
        /// <summary>
        /// 转出病区ID
        /// </summary>
        public int TransferOutStationID { get; set; }

        /// <summary>
        /// 转出科室ID
        /// </summary>
        public int TransferOutDepartmentID { get; set; }

        /// <summary>
        /// 转出时间
        /// </summary>
        public DateTime TransferOutDatatime { get; set; }
        /// <summary>
        /// 转出床位ID
        /// </summary>
        public int TransferOutBedID { get; set; }

        /// <summary>
        /// 转出操作员编码
        /// </summary>
        public string TransferOutEmployeeID { get; set; }


        /// <summary>
        /// 转出床号
        /// </summary>
        public string TransferOutBedNumber { get; set; }
    }

}