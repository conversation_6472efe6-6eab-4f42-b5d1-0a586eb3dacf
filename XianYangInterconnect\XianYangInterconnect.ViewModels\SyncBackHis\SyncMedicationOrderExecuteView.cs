﻿using System.ComponentModel.DataAnnotations;

namespace XianYangInterconnect.ViewModels
{
    public class SyncMedicationOrderExecuteView
    {

        /// <summary>
        /// 医院编码
        /// </summary>
        public string HospitalCode { get; set; }

        /// <summary>
        /// 医院名称
        /// </summary>
        public string HospitalName { get; set; }

        /// <summary>
        /// 院区编码
        /// </summary>
        public string HpAreaCode { get; set; }

        /// <summary>
        /// 院区名称
        /// </summary>
        public string HpAreaName { get; set; }

        /// <summary>
        /// 操作日期（格式：yyyyMMdd）
        /// </summary>
        public string OperateDate { get; set; }

        /// <summary>
        /// 操作人编码
        /// </summary>
        public string OperatorCode { get; set; }

        /// <summary>
        /// 操作人姓名
        /// </summary>
        public string OperatorName { get; set; }

        /// <summary>
        /// 医嘱执行科室编码
        /// </summary>
        public string ExecDeptCode { get; set; }

        /// <summary>
        /// 医嘱执行科室名称
        /// </summary>
        public string ExecDeptName { get; set; }

        /// <summary>
        /// 医嘱号（业务主键）
        /// </summary>
        [Required]
        public string OrderNo { get; set; }

        /// <summary>
        /// 申请单号
        /// </summary>
        public string RequestNo { get; set; }

        /// <summary>
        /// 医嘱项目类型编码（字典：HIRP019）
        /// </summary>
        public string OrderItemTypeCode { get; set; }

        /// <summary>
        /// 医嘱项目类型名称（字典：HIRP019）
        /// </summary>
        public string OrderItemTypeName { get; set; }

        /// <summary>
        /// 标本条码号
        /// </summary>
        public string BarNo { get; set; }

        /// <summary>
        /// 采集日期（格式：yyyyMMdd）
        /// </summary>
        public DateTime? CollectedDateTime { get; set; }

        /// <summary>
        /// 采集人ID
        /// </summary>
        public string CollectorCode { get; set; }

        /// <summary>
        /// 采集人姓名
        /// </summary>
        public string CollectorName { get; set; }

        /// <summary>
        /// 医嘱撤销原因描述
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 医嘱执行状态编码（字典：HIRP020）
        /// </summary>
        [Required]
        public string OrderExecStatusCode { get; set; }

        /// <summary>
        /// 医嘱执行状态名称（字典：HIRP020）
        /// </summary>
        [Required]
        public string OrderExecStatusName { get; set; }

        /// <summary>
        /// 就诊次数
        /// </summary>
        public int? VisitTimes { get; set; }

        /// <summary>
        /// 患者EMPI号
        /// </summary>
        public string EmpiId { get; set; }

        /// <summary>
        /// 患者本地ID
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// 就诊流水号
        /// </summary>
        public string VisitSqNo { get; set; }

        /// <summary>
        /// 就诊类型编码（字典：HIRP001）
        /// </summary>
        public string VisitTypeCode { get; set; }

        /// <summary>
        /// 就诊类型名称（字典：HIRP001）
        /// </summary>
        public string VisitTypeName { get; set; }

        /// <summary>
        /// 执行单号
        /// </summary>
        public string ExecuteFormNo { get; set; }

        /// <summary>
        /// his医嘱号
        /// </summary>
        public string HisOrderNo { get; set; }

        /// <summary>
        /// 皮试结果（1：阳性；0：阴性；空：无皮试）
        /// </summary>
        public string SkinTestResult { get; set; }

        /// <summary>
        /// 组合号
        /// </summary>
        public string ComNo { get; set; }

        /// <summary>
        /// 其他操作人编码（双签名）
        /// </summary>
        public string OtherOperCode { get; set; }

        /// <summary>
        /// 其他操作人名称
        /// </summary>
        public string OtherOperName { get; set; }

        /// <summary>
        /// 其他操作时间
        /// </summary>
        public DateTime? OtherOperTime { get; set; }

        /// <summary>
        /// 其他操作科室编码
        /// </summary>
        public string OtherDeptCode { get; set; }

        /// <summary>
        /// 其他操作科室名称
        /// </summary>
        public string OtherDeptName { get; set; }
    }
}