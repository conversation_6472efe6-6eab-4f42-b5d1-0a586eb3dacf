﻿using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Data.SqlClient;
using NLog;
using System.Net;
using XianYangInterconnect.Common;

namespace XianYangInterconnect.API.Filters
{
    /// <summary>
    /// 全局异常错误日志
    /// </summary>
    /// <remarks>
    ///
    /// </remarks>
    /// <param name="env"></param>
    public class GlobalExceptionsFilter(IWebHostEnvironment env) : IExceptionFilter
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 发生异常时调用
        /// </summary>
        /// <param name="context"></param>
        public void OnException(ExceptionContext context)
        {
            var json = new ResponseResult();
            json.Error("系统处理中，请稍后重试！");
            _logger.Error(context.Exception.ToString());

            var errorAudit = "Unable to resolve service for";
            if (!string.IsNullOrEmpty(context.Exception.Message) && context.Exception.Message.Contains(errorAudit))
            {
                json.Message = "若添加新服务，请重新生成项目！";
            }
            else
            {
                json.Message += $"[{GetExceptionCode(context)}]";
            }


            if (env.EnvironmentName.ToString().Equals("Development"))
            {
                json.MessageDev = $"发生异常：{context.Exception}";
            }

            context.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            context.Result = json.ToJson();
        }
        /// <summary>
        /// 获取自定义异常代码
        /// </summary>
        /// <param name="context"></param>
        /// <remarks>
        /// 1000~1999为程序异常；
        /// 2000~2999为网络异常；
        /// 3000~3999为数据库异常；
        /// 4000为未知异常；
        /// 5000~5999为业务异常；
        /// </remarks>
        /// <returns></returns>
        private static int GetExceptionCode(ExceptionContext context)
        {
            return context.Exception switch
            {
                var x when x is NullReferenceException => 1000,
                var x when x is TimeoutException => 1001,
                var x when x is InvalidOperationException => 1002,
                var x when x is ArgumentNullException => 1003,
                var x when x is ArgumentOutOfRangeException => 1004,
                var x when x is ArgumentException => 1005,
                var x when x is WebException => 2000,
                var x when x is SqlException => 3000,
                _ => 4040
            };
        }
    }
}
