﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.API.DataTransfer;
using XianYangInterconnect.API.Extensions;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Service.Interface;

namespace XianYangInterconnect.API.Controllers
{
    /// <summary>
    /// 患者业务数据同步
    /// </summary>
    [Produces("application/json")]
    [Route("api/SyncInpatinet")]
    [EnableCors("any")]
    public class PatientController : ControllerBase
    {
        private readonly IInpatientService _inpatientService;
        private readonly IPatientOperationService _patientOperationService;
        private readonly IPatientOrderService _patientOrderService;
        private readonly IPatientMedicineScheduleService _patientMedicineScheduleService;
        private readonly SyncCoreService _syncCoreService;
        private readonly IPatientMachineDataService _patientMachineDataService;
        private readonly IPatientLabservice  _patientLabservice;
        private readonly MedicalContext _medicalContext = null;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="inpatientService"></param>
        /// <param name="patientOperationService"></param>
        /// <param name="patientMedicineScheduleService"></param>
        /// <param name="patientOrderService"></param>
        /// <param name="syncCoreService"></param>
        /// <param name="patientMachineDataService"></param>
        /// <param name="patientLabservice"></param>
        public PatientController(IInpatientService inpatientService
            , IPatientOperationService patientOperationService
            , IPatientMedicineScheduleService patientMedicineScheduleService
            , IPatientOrderService patientOrderService
            , SyncCoreService syncCoreService
            , IPatientMachineDataService patientMachineDataService
            , IPatientLabservice patientLabservice
            , MedicalContext medicalContext
            )
        {
            _inpatientService = inpatientService;
            _patientOperationService = patientOperationService;
            _patientMedicineScheduleService = patientMedicineScheduleService;
            _patientOrderService = patientOrderService;
            _syncCoreService = syncCoreService;
            _patientMedicineScheduleService = patientMedicineScheduleService;
            _patientMachineDataService = patientMachineDataService;
            _patientLabservice= patientLabservice;
            _medicalContext = medicalContext;
        }

        /// <summary>
        /// 根据病区Code同步病人信息
        /// </summary>
        /// <param name="stationCode">不传默认获取所有病区患者</param>
        /// <param name="patientState">患者状态，R-住院登记，I-病房接诊，B-出院登记，O-出院结算，P-预约出院，N-无费退院</param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncInPatientByStatioCode")]
        [DistributedLockFilter("SyncInPatientByStatioCode", LockType.Redis)]
        public async Task<IActionResult> SyncInPatientByStatioCode(string stationCode, string patientState, DateTime? startDate = null, DateTime? endDate = null)
        {
            var resultSrt = await _inpatientService.SyncInPatientByStationCode(stationCode, patientState, startDate, endDate);

            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 根据单患者信息
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="caseNumber"></param>
        /// <param name="patientState">患者状态，R-住院登记，I-病房接诊，B-出院登记，O-出院结算，P-预约出院，N-无费退院</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncInPatientByChartNo")]
        [DistributedLockFilter("SyncInPatientByChartNo", LockType.Redis)]
        public async Task<IActionResult> SyncInPatientByChartNo(string chartNo, string caseNumber, string patientState)
        {
            var resultSrt = await _inpatientService.SyncInPatientByChartNo(chartNo, caseNumber, patientState);

            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步当前表中在院患者出院信息
        /// </summary>
        /// <param name="chartNo"></param>
        /// <param name="localCaseNumber"></param>
        /// <param name="caseNumber"></param>
        /// <param name="patientState">患者状态，R-住院登记，I-病房接诊，B-出院登记，O-出院结算，P-预约出院，N-无费退院</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncDischargeInPatientByCaseNumber")]
        public async Task<IActionResult> SyncDischargeInPatientByCaseNumber(string localCaseNumber, string chartNo, string caseNumber, string patientState = "O")
        {
            var resultSrt = await _inpatientService.SyncDischargeInPatientByCaseNumber(localCaseNumber, chartNo, caseNumber, patientState);

            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 获取单个患者主诉信息
        /// </summary>
        /// <param name="visitNo">用户流水号(门诊挂号流水号、住院流水号),示例：4148444、ZY010005374857</param>
        /// <param name="visitType">门诊住院标识,1门诊 2住院</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetSingleInpatientChiefComplaint")]
        public async Task<IActionResult> GetSingleInpatientChiefComplaint(string visitNo, string visitType)
        {
            var resultSrt = await _inpatientService.GetSingleInpatientChiefComplaintAsync(visitNo, visitType);
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步手术信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="ApplyNo"></param>
        /// <param name="serviceType"></param>
        /// <param name="modifyPersonID"></param>
        /// <param name="modifyDate"></param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncPatientOperationByApplyNo")]
        public async Task<IActionResult> SyncPatientOperationByApplyNo(string caseNumber, string ApplyNo, string serviceType, string modifyPersonID, string modifyDate)
        {
            var data = await _patientOperationService.SyncPatientOperationByApplyNo(caseNumber, ApplyNo, serviceType, modifyPersonID, modifyDate);
            var result = new ResponseResult
            {
                Data = data
            };
            if (data) result.Sucess();
            return result.ToJson();
        }
        /// <summary>
        /// 同步手术信息（MQ调用）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncPatientOperation")]
        public async Task<IActionResult> SyncPatientOperation()
        {
            var data = await _patientOperationService.SyncPatientOperation();
            var result = new ResponseResult
            {
                Data = data
            };
            if (data) result.Sucess();
            return result.ToJson();
        }
        /// <summary>
        /// 同步单个患者医嘱信息
        /// </summary>
        /// <param name="caseNumber">患者流水号,对方的InpatientNo</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("GetSingleInpatientOrder")]
        [DistributedLockFilter("GetSingleInpatientOrder", LockType.Redis)]
        public async Task<IActionResult> GetSingleInpatientOrder(string caseNumber)
        {
            var query = await _patientOrderService.SyncPatientOrdersAsync(caseNumber);
            var result = new ResponseResult
            {
                Data = query
            };
          
            if (query)
            {
                result.Sucess();
            }
            return result.ToJson();
        }
        /// <summary>
        /// 同步患者过敏史信息
        /// </summary>
        /// <param name="chartNo">ChartNo</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncPatientAllergic")]
        public async Task<IActionResult> SyncPatientAllergic(string chartNo)
        {
            var resultSrt = await _inpatientService.SyncPatientAllergic(chartNo);
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }
        /// <summary>
        /// 根据住院号获取患者诊断信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncPatientDiagnosis")]
        public async Task<IActionResult> SyncPatientDiagnosis(string caseNumber)
        {
            var resultSrt = await _inpatientService.SyncPatientDiagnosis(caseNumber);
            var result = new ResponseResult
            {
                Data = resultSrt,
                Code = 1
            };
            return result.ToJson();
        }

        /// <summary>
        /// 根据病区同步患者用药信息
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncPatientMedicineScheduleByStationID")]
        public async Task<IActionResult> SyncPatientMedicineScheduleByStationID(int? stationID, DateTime? beginTime, DateTime? endTime, string caseNumber)
        {
            var result = new ResponseResult
            {
                Data = await _patientMedicineScheduleService.SyncPatientMedicineScheduleByStationID(stationID, beginTime, endTime, caseNumber)
            };
            return result.ToJson();
        }

        /// <summary>
        /// 根据LogID同步患者用药信息
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncPatientMedicineByLogID")]
        public async Task<IActionResult> SyncPatientMedicineByLogID(string hospitalID)
        {
            var result = new ResponseResult
            {
                Data = await _patientMedicineScheduleService.SyncPatientMedicine(hospitalID)
            };
            return result.ToJson();
        }

        /// <summary>
        /// 根据病区组ID同步患者信息
        /// </summary>
        /// <param name="stationGroupID"></param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncInPatientByStationGroup")]
        public async Task<IActionResult> SyncInPatientByStationGroup(int stationGroupID)
        {
            var result = new ResponseResult
            {
                Data = await _syncCoreService.SyncInpatientDataByStationGroup(stationGroupID)
            };
            return result.ToJson();
        }

        /// <summary>
        /// 同步新生儿信息
        /// </summary>
        /// <param name="parentCaseNumber">母亲住院流水号</param>
        /// <param name="userID">操作人</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncNewBornInfo")]
        public async Task<IActionResult> SyncNewBornData(string parentCaseNumber,string userID)
        {
            var result = new ResponseResult
            {
                Data = await _inpatientService.GetNewBornData(parentCaseNumber,userID)
            };
            return result.ToJson();
        }
        /// <summary>
        /// 同步患者住院检验信息（手动执行）
        /// </summary>
        /// <param name="caseNumber">住院流水号</param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("SyncPatientTestData")]
        public async Task<IActionResult> SyncPatientTestData(string caseNumber)
        {
            var result = new ResponseResult
            {
                Data = await _patientLabservice.SyncPatientTest(null, caseNumber)
            };
            return result.ToJson();
        }
        /// <summary>
        /// 同步患者仪器数据（手动执行）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [NoAuthorization]
        [Route("SyncPatientMachineDataData")]
        public async Task<IActionResult> SyncPatientMachineDataData(string  hospitalID)
        {
            var result = new ResponseResult
            {
                Data = await _patientMachineDataService.AutoSyncPatientMachineData(hospitalID)
            };
            return result.ToJson();
        }
        /// <summary>
        /// 处理历史医嘱未停止排程数据(使用后删除)
        /// </summary>
        /// <param name="stationID"></param>
        /// <returns></returns>
        [HttpGet]
        [NoAuthorization]
        [Route("DealHistoryOrderSchedule")]
        public async Task<IActionResult> GetSingleInpatientOrder(int stationID)
        {
            var caseNumbers = await _medicalContext.InpatientDataInfos
                .Where(m => m.StationID == stationID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1) && m.DischargeDate == null && m.DeleteFlag != "*")
                .Select(m => m.CaseNumber).ToListAsync();
            foreach (var caseNumber in caseNumbers)
            {
                await GetSingleInpatientOrder(caseNumber);
            }
            return new ResponseResult().ToJson();
        }
    }
}