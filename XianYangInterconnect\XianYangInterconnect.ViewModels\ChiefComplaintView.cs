﻿namespace XianYangInterconnect.ViewModels
{
    public class ChiefComplaintView
    {
        /// <summary>
        /// 成功码
        /// </summary>
        public int code { get; set; }
        /// <summary>
        /// 信息
        /// </summary>
        public string msg { get; set; }
        /// <summary>
        /// 床位数据
        /// </summary>
        public List<ChiefComplaint> data { get; set; }
    }
    public class ChiefComplaint
    {
        /// <summary>
        /// 病历ID
        /// </summary>
        public string recordId { get; set; }
        /// <summary>
        /// 病历名称
        /// </summary>
        public string recordName { get; set; }
        /// <summary>
        /// 主诉内容
        /// </summary>
        public string chiefValue { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string creatTime { get; set; }
        /// <summary>
        /// 科室名称
        /// </summary>
        public string deptName { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string operName { get; set; }
        /// <summary>
        /// 患者id
        /// </summary>
        public string patientId { get; set; }
        /// <summary>
        /// 现病史
        /// </summary>
        public string hpi { get; set; }
        /// <summary>
        /// 既往史
        public string ph { get; set; }
        /// <summary>
        /// 个人史
        /// </summary>
        public string psh { get; set; }
        /// <summary>
        /// 婚育史
        /// </summary>
        public string mh { get; set; }
        /// <summary>
        /// 家族史
        /// </summary>
        public string fh { get; set; }
        /// <summary>
        /// 过敏史
        /// </summary>
        public string allergyHistory { get; set; }
    }
}
