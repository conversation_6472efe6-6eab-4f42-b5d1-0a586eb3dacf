﻿using Microsoft.Extensions.Options;
using NLog;
using XianYangInterconnect.Common;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    public class JobLogService : IJobLogService
    {
        public static List<SyncJob> SyncJobList = new List<SyncJob>();
        /// <summary>
        /// 同步任务清理是否正在进行
        /// </summary>
        public static bool SyncJobListClearFlag = false;
        private static Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOptions<SystemConfig> _config;


        public JobLogService(
              IOptions<SystemConfig> options
            )
        {
            _config = options;
        }

        //判断作业状态
        public bool GetJobStatus(string jobId, string jobName, string subJobId)
        {
            if (SyncJobList == null)
            {
                SyncJobList = new List<SyncJob>();
            }
            if (string.IsNullOrEmpty(jobId))
            {
                _logger.Error("判断作业状态,jobId为空，返回");
                return false;
            }
            if (!SyncJobListClearFlag)
            {
                SyncJobListClearFlag = true;
                var syncJobListTemp = SyncJobList.Where(m => m == null || m.JobStatus == 0).ToList();
                foreach (var item in syncJobListTemp)
                {
                    SyncJobList.Remove(item);
                }
                SyncJobListClearFlag = false;
            }
            //从配置档中获取数据
            int jobRunTime = 10;
            var syncJob = SyncJobList.Where(m => m.JobId == jobId.Trim() && m.JobName == jobName.Trim()
            && m.SubJobID == subJobId.Trim()).FirstOrDefault();
            if (syncJob == null)
            {
                var t = new SyncJob()
                {
                    JobId = jobId,
                    JobName = jobName,
                    SubJobID = subJobId,
                    ModifyDateTime = DateTime.Now,
                    JobStatus = 1
                };
                SyncJobList.Add(t);
                return true;
            }
            if (syncJob.JobStatus == 0)
            {
                syncJob.ModifyDateTime = DateTime.Now;
                syncJob.JobStatus = 1;
                return true;
            }
            //作业启动时间过长，清除正在执行的作业，重新开始一个作业          
            if (syncJob.ModifyDateTime.AddMinutes(jobRunTime) < DateTime.Now)
            {
                _logger.Error("作业超时,重新启动||" + ListToJson.ToJson(syncJob));
                RemoveJob(syncJob.JobId, syncJob.JobName, syncJob.SubJobID);
                return true;
            }
            else
            {
                _logger.Info("作业正在执行！" + ListToJson.ToJson(syncJob));
                return false;
            }
        }
        //移除作业
        public void RemoveJob(string jobId, string jobName, string subJobID)
        {
            if (SyncJobList == null)
            {
                SyncJobList = new List<SyncJob>();
            }
            var syncJob = SyncJobList.Where(m => m.JobId == jobId && m.JobName == jobName && m.SubJobID == subJobID).ToList();

            //修改作业状态为停止
            foreach (var item in syncJob)
            {
                item.JobStatus = 0;
                item.ModifyDateTime = DateTime.Now;
            }
        }

        public List<SyncJob> GetJobByID(string jobId, string jobName, string subJobId)
        {
            if (SyncJobList == null)
            {
                SyncJobList = new List<SyncJob>();
            }
            if (jobId == "all")
            {
                return SyncJobList.OrderBy(m => m.ModifyDateTime).ToList();
            }
            var syncJobList = SyncJobList.Where(m => m.JobId == jobId.Trim() && m.JobName == jobName.Trim() && m.SubJobID == subJobId.Trim()).OrderBy(m => m.ModifyDateTime).ToList();
            return syncJobList;
        }
    }
}
