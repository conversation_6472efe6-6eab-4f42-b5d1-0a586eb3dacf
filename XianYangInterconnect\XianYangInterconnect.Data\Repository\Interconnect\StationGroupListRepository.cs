﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Repository
{
    public class StationGroupListRepository : IStationGroupListRepository
    {
        private DataOutContext _dataOutConnection = null;
        public StationGroupListRepository(DataOutContext db)
        {
            _dataOutConnection = db;
        }
        public async Task<List<StationGroupListInfo>> GetStationGroupListByGroupID(int GroupID)
        {
            return await _dataOutConnection.StationGroupListInfos.Where(m => m.StationGroup == GroupID).ToListAsync();
        }

        public async Task<List<StationGroupListInfo>> GetStationGroupListAll()
        {
            return await _dataOutConnection.StationGroupListInfos.ToListAsync();
        }
    }
}