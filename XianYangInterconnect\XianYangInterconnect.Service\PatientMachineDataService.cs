﻿using NLog;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    public class PatientMachineDataService : IPatientMachineDataService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAPISettingRepository _aPISettingRepository;
        private readonly ISyncDatasLogRepository _syncDatasLogRepository;
        private readonly ISyncDatasLogServices _syncDatasLogServices;

        /// <summary>
        /// 体温
        /// </summary>
        private const int ASSESSLISTID_1295 = 1295;
        /// <summary>
        /// 脉搏
        /// </summary>
        private const int ASSESSLISTID_1338 = 1338;
        /// <summary>
        /// 心率
        /// </summary>
        private const int ASSESSLISTID_1943 = 1943;
        /// <summary>
        /// 呼吸
        /// </summary>
        private const int ASSESSLISTID_1296 = 1296;
        /// <summary>
        /// 收缩压
        /// </summary>
        private const int ASSESSLISTID_1297 = 1297;
        /// <summary>
        /// 舒张压
        /// </summary>
        private const int ASSESSLISTID_1298 = 1298;
        /// <summary>
        /// 血氧
        /// </summary>
        private const int ASSESSLISTID_1344 = 1344;

        public PatientMachineDataService(
            IAPISettingRepository aPISettingRepository
            , ISyncDatasLogRepository syncDatasLogRepository
            , ISyncDatasLogServices syncDatasLogServices
            )
        {
            _aPISettingRepository = aPISettingRepository;
            _syncDatasLogRepository = syncDatasLogRepository;
            _syncDatasLogServices = syncDatasLogServices;
        }
        /// <summary>
        /// 同步患者机器数据
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <returns></returns>
        public async Task<bool> AutoSyncPatientMachineData(string hospitalID)
        {

            SyncDataLogInfo logInfo;
            MQMessageView messageView;
            bool result = false;
            var logIDs = await _syncDatasLogRepository.GetSyncDataByDataType(hospitalID, "BodySurface");
            Dictionary<string, (int AssessListID, string Unit)> fieldConfigs = CreateFieldConfigDictionary(new PatientMachineDataView());
            foreach (var logID in logIDs)
            {
                logInfo = await _syncDatasLogRepository.GetSyncDataByID(logID);
                if (logInfo == null)
                {
                    await _syncDatasLogServices.ModifySyncDataLog(logID, result);
                    continue;
                }
                messageView = ListToJson.ToList<MQMessageView>(logInfo.SyncData);
                if (messageView == null)
                {
                    await _syncDatasLogServices.ModifySyncDataLog(logID, result);
                    continue;
                }
                result = await SyncPatientMachineData(messageView, fieldConfigs);
                await _syncDatasLogServices.ModifySyncDataLog(logID, result);
            }
            return true;
            //Dictionary<string, (int AssessListID, string Unit)> fieldConfigs = CreateFieldConfigDictionary(new PatientMachineDataView());
            //string hisesult = ReadFile.ReadTxt(@"C:\Users\<USER>\Desktop\json\xianyangMachine.txt");
            //var messageView = ListToJson.ToList<MQMessageView>(hisesult);
            //await SyncPatientMachineData(messageView, fieldConfigs);
            //return true;
        }

        /// <summary>
        /// 同步患者机器数据到核心系统
        /// </summary>
        /// <param name="messageView"></param>
        /// <param name="fieldConfigs"></param>
        /// <returns></returns>
        public async Task<bool> SyncPatientMachineData(MQMessageView messageView, Dictionary<string, (int AssessListID, string Unit)> fieldConfigs)
        {
            var patientMachineDataView = ListToJson.ToList<PatientMachineDataView>(messageView.MessageData.ToString());
            if (patientMachineDataView == null)
            {
                _logger.Warn($"同步患者机检数据转换失败,SettingCode = SyncPatientMachineData");
                return false;
            }

            var responseUrlView = await _aPISettingRepository.GetAPIAddress("SyncPatientMachineData");
            if (responseUrlView == null || string.IsNullOrEmpty(responseUrlView.ApiUrl))
            {
                _logger.Error($"表aPISetting获取链接配置失败,SettingCode = SyncPatientMachineData");
                return false;
            }

            // 创建请求参数列表
            var requestParams = new List<ClinicDataView>();
            // 遍历配置字段
            foreach (var config in fieldConfigs)
            {
                // 通过反射获取字段值
                var value = (string)typeof(PatientMachineDataView).GetProperty(config.Key)?.GetValue(patientMachineDataView);

                // 如果字段值不为空，则添加到请求参数
                if (!string.IsNullOrWhiteSpace(value) && decimal.TryParse(value, out var _))
                {
                    requestParams.Add(new ClinicDataView
                    {
                        CaseNumber = messageView.CaseNumber,
                        AssessListID = config.Value.AssessListID,
                        DataValue = value,
                        DataDate = patientMachineDataView.MeasureTime.Date,
                        DataTime = patientMachineDataView.MeasureTime.TimeOfDay,
                        Unit = config.Value.Unit
                    });
                }
            }
            try
            {
                if (requestParams.Count > 0)
                {
                    HttpHelper.SendObjectAsJsonInBody(responseUrlView.ApiUrl, requestParams);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"发送数据失败，失败原因：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取同步数据
        /// </summary>
        /// <returns></returns>
        private static Dictionary<string, (int AssessListID, string Unit)> CreateFieldConfigDictionary(PatientMachineDataView patientMachineDataView)
        {
            return new Dictionary<string, (int AssessListID, string Unit)>
            {
                { nameof(patientMachineDataView.Temperature), (ASSESSLISTID_1295, "℃") },
                { nameof(patientMachineDataView.Pulse), (ASSESSLISTID_1338, "次/分") },
                { nameof(patientMachineDataView.HeartRate), (ASSESSLISTID_1943, "次/分") },
                { nameof(patientMachineDataView.Breathing), (ASSESSLISTID_1296, "次/分") },
                { nameof(patientMachineDataView.SystolicPressure), (ASSESSLISTID_1297, "mmHg") },
                { nameof(patientMachineDataView.DiastolicPressure), (ASSESSLISTID_1298, "mmHg") },
                { nameof(patientMachineDataView.SPO2), (ASSESSLISTID_1344, "%") }
            };
        }
    }
}
