﻿using System.ComponentModel.DataAnnotations.Schema;

namespace XianYangInterconnect.Models.HIS
{
    [Serializable]
    [Table("VIEW_BYJ_HL3C", Schema = "XYZXHIS") ]
    public partial class BYJDrugExec
    {
        /// <summary>
        /// 主键
        /// </summary>
        [Column("EXEC_SQN")]
        public string EXEC_SQN { get; set; }

        /// <summary>
        /// 药品分组码
        /// </summary>
        [Column("BARCODE")]
        public string BARCODE { get; set; }

        /// <summary>
        /// 预计执行时间
        /// </summary>
        [Column("USE_TIME", TypeName = "DATE")]
        public DateTime USE_TIME { get; set; }
        /// <summary>
        /// 开嘱医师
        /// </summary>
        [Column("DOC_CODE")]
        public string DOC_CODE { get; set; }
    }
}
