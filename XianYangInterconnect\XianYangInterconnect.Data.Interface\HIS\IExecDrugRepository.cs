﻿using XianYangInterconnect.Models.HIS;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Data.Interface
{
    public interface IExecDrugRepository
    {
        /// <summary>
        /// 根据时间差、CaseNumber、用药的开始结束时间，获取单患者药品执行信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="lastDateTime"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        Task<List<ExecDrug>> GetByTimeDiff(string caseNumber, DateTime lastDateTime, DateTime beginTime, DateTime endTime);

        /// <summary>
        /// 获取一定时间内的标签清单
        /// </summary>
        /// <param name="minute"></param>
        /// <returns></returns>
        Task<List<PatientExecsqnView>> GetByUseTime(int minute);

        /// <summary>
        /// 根据患者号、用药的开始结束时间，获取单患者药品执行信息
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        Task<List<ExecDrug>> GetByUseTime(string caseNumber, DateTime beginTime, DateTime endTime);
    }
}