﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Repository
{
    public class InpatientDataRepository : IInpatientDataRepository
    {
        private readonly MedicalContext _medicalContext = null;

        public InpatientDataRepository(MedicalContext medicalContext)
        {
            _medicalContext = medicalContext;
        }

        /// <summary>
        /// 获取当前在院患者住院号
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetCaseNumbers(string hospitalID)
        {
            return await _medicalContext.InpatientDataInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*" && (m.InHospitalStatus == 40 || m.InHospitalStatus == 30)).Select(m => m.CaseNumber).Distinct().ToListAsync();
        }

        /// <summary>
        /// 根据病区ID获取当前在院患者住院号
        /// </summary>
        /// <param name="stationID"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<string>> GetCaseNumberListByStationID(int stationID, string hospitalID)
        {
            return await _medicalContext.InpatientDataInfos.AsNoTracking().Where(m => m.StationID == stationID && m.HospitalID == hospitalID && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)
            && m.DeleteFlag != "*").Select(m => m.CaseNumber).ToListAsync();
        }

        public async Task<List<InpatientDataInfo>> GetAllInpatientDataInfos(string hospitalID)
        {
            return await _medicalContext.InpatientDataInfos
                .AsNoTracking()
                .Where(m => m.HospitalID == hospitalID
                             && m.DeleteFlag != "*"
                             && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1))
                .Select(m => new InpatientDataInfo
                {
                    ID = m.ID,
                    CaseNumber = m.CaseNumber,
                    HospitalID = m.HospitalID,
                    ChartNo = m.ChartNo,
                    LocalCaseNumber = m.LocalCaseNumber,
                })
                .ToListAsync();
        }
        /// <summary>
        /// 获取所有未出院患者数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetAllInpatientDataAsync(string hospitalID)
        {
            return await _medicalContext.InpatientDataInfos
                .AsNoTracking()
                .Where(m => m.HospitalID == hospitalID
                             && m.DeleteFlag != "*"
                             && m.InHospitalStatus<60)
                .Select(m => new InpatientDataInfo
                {
                    ID = m.ID,
                    CaseNumber = m.CaseNumber,
                    HospitalID = m.HospitalID,
                    ChartNo = m.ChartNo,
                    LocalCaseNumber = m.LocalCaseNumber,
                })
                .ToListAsync();
        }

        public async Task<InpatientDataInfo> GetInpatientDataInfo(string caseNumber)
        {
            return await _medicalContext.InpatientDataInfos.Where(m => m.CaseNumber == caseNumber
                 && m.DeleteFlag != "*"
                 && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).Select(m => new InpatientDataInfo
                 {
                     ID = m.ID,
                     CaseNumber = m.CaseNumber,
                     HospitalID = m.HospitalID,
                     ChartNo = m.ChartNo,
                     LocalCaseNumber = m.LocalCaseNumber,
                     PatientID = m.PatientID,
                     StationID = m.StationID,
                     DepartmentListID = m.DepartmentListID,
                 }).FirstOrDefaultAsync();
        }

        public async Task<InpatientDataInfo> GetInpatientDataView(string caseNumber)
        {
            return await _medicalContext.InpatientDataInfos.Where(m => m.CaseNumber == caseNumber
                 && m.DeleteFlag != "*"
                 && InHospitalStatus.INHOSPITALLIST.Contains(m.InHospitalStatus ?? -1)).Select(m => new InpatientDataInfo
                 {
                     CaseNumber = m.CaseNumber,                    
                     StationID = m.StationID
                 }).FirstOrDefaultAsync();
        }

        public async Task<List<InpatientDataInfo>> GetInpatientDatas(List<string> caseNumbers)
        {
            //var sql = _medicalContext.InpatientDataInfos.AsNoTracking().Where(m => caseNumbers.Contains(m.CaseNumber)
            //     && m.DeleteFlag != "*"
            //     ).Select(m => new InpatientDataInfo
            //     {
            //         ID = m.ID,
            //         CaseNumber = m.CaseNumber,
            //         HospitalID = m.HospitalID,
            //         ChartNo = m.ChartNo,
            //         LocalCaseNumber = m.LocalCaseNumber,
            //         PatientID = m.PatientID,
            //         StationID = m.StationID,
            //         DepartmentListID = m.DepartmentListID,
            //     }).ToQueryString();         
            return await _medicalContext.InpatientDataInfos.AsNoTracking().Where(m => caseNumbers.Contains(m.CaseNumber)
                 && m.DeleteFlag != "*"
                 ).Select(m => new InpatientDataInfo
                 {
                     ID = m.ID,
                     CaseNumber = m.CaseNumber,
                     HospitalID = m.HospitalID,
                     ChartNo = m.ChartNo,
                     LocalCaseNumber = m.LocalCaseNumber,
                     PatientID = m.PatientID,
                     StationID = m.StationID,
                     DepartmentListID = m.DepartmentListID,
                 }).ToListAsync();
         
        }
        /// <summary>
        /// 获取患者信息(包含出院患者)
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetInpatientDataInfoAsync(string caseNumber)
        {
            return await _medicalContext.InpatientDataInfos.AsNoTracking().Where(m => m.CaseNumber == caseNumber && m.DeleteFlag != "*").FirstOrDefaultAsync();
        }

        /// <summary>
        /// 通过caseNumberList获取患者数据（含出院）
        /// </summary>
        /// <param name="caseNumberList"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientListByCaseNumberListAsync(List<string> caseNumberList)
        {
            var InpatientDataList = new List<InpatientDataInfo>();
            foreach (var item in caseNumberList)
            {
                var tempList = await _medicalContext.InpatientDataInfos.Where(m => m.CaseNumber == item && m.DeleteFlag != "*").ToListAsync();
                InpatientDataList = InpatientDataList.Union(tempList).ToList();
            }
            return InpatientDataList;
        }
        /// <summary>
        /// 获取患者（包含出院）
        /// </summary>
        /// <param name="chartNo"></param>
        /// <returns></returns>
        public async Task<List<InpatientDataInfo>> GetInpatientDataInfoByChartNoAsync(string chartNo)
        {
            return await _medicalContext.InpatientDataInfos.AsNoTracking().Where(m => m.ChartNo == chartNo && m.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 获取同步的患者流水号
        /// </summary>
        /// <param name="caseNumbers">患者流水号集合</param>
        /// <returns></returns>
        public async Task<List<string>> GetByCaseNumberListAsync(List<string> caseNumbers)
        {
            return await _medicalContext.InpatientDataInfos.Where(m => caseNumbers.Contains(m.CaseNumber) && m.DeleteFlag != "*")
            .Select(m => m.CaseNumber).ToListAsync();
        }
        /// <summary>
        ///  根据CaseNumber获取数据(包含已出院)
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        public async Task<InpatientDataInfo> GetInpatientAllDataView(string caseNumber)
        {
            return await _medicalContext.InpatientDataInfos.Where(m => m.CaseNumber == caseNumber
                 && m.DeleteFlag != "*").Select(m => new InpatientDataInfo
                 {
                     CaseNumber = m.CaseNumber,
                     StationID = m.StationID
                 }).FirstOrDefaultAsync();
        }
    }
}