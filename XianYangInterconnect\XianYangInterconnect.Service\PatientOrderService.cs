﻿using NLog;
using InterconnectCore.ViewModels;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.ViewModels;
using XianYangInterconnect.Models.HIS;
using System.Data;
namespace XianYangInterconnect.Service
{
    public class PatientOrderService : IPatientOrderService
    {
        #region 引用
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IAPISettingRepository _aPISettingRepository;
        private readonly IStationToDeptInfoRepository _stationToDeptInfoRepository;
        private readonly ISyncDatasLogRepository _syncDatasLogRepository;
        private readonly IHISPatientOrderRepository _hISPatientOrderRepository;
        private readonly ISyncDatasLogServices _syncDatasLogServices;
        #endregion

        #region 常量
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// 
        /// </summary>
        private const string STRING_EMPTY = "";
        /// <summary>
        /// 停止医嘱状态
        /// </summary>
        private const string STOP_ORDER_STATUS_STOP = "正常停止";
        /// <summary>
        /// 作废医嘱状态
        /// </summary>
        private const string STOP_ORDER_STATUS_CANCEL = "作废";
        /// <summary>
        /// 西药
        /// </summary>
        private const string ORDERPATTERN_143 = "143";
        /// <summary>
        /// 144 中药 145 中草药
        /// </summary>
        private static readonly List<string> ORDERPATTERNLIST = ["144", "145"];
        #endregion

        #region 构造器
        public PatientOrderService(
             IAppConfigSettingRepository appConfigSettingRepository
            , IAPISettingRepository aPISettingRepository
            , IStationToDeptInfoRepository stationToDeptInfoRepository
            , ISyncDatasLogRepository syncDatasLogRepository
            , IHISPatientOrderRepository hISPatientOrderRepository
            , ISyncDatasLogServices syncDatasLogServices
            )
        {
            _appConfigSettingRepository = appConfigSettingRepository;
            _aPISettingRepository = aPISettingRepository;
            _stationToDeptInfoRepository = stationToDeptInfoRepository;
            _syncDatasLogRepository = syncDatasLogRepository;
            _hISPatientOrderRepository = hISPatientOrderRepository;
            _syncDatasLogServices = syncDatasLogServices;
        }
        #endregion

        /// <summary>
        /// 同步患者医嘱数据
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<bool> AutoSyncPatientOrderSync(string hospitalID)
        {
            var responseUrlView = await _aPISettingRepository.GetAPIAddress("SyncPatientOrders");
            if (responseUrlView == null || string.IsNullOrEmpty(responseUrlView.ApiUrl))
            {
                _logger.Error($"表aPISetting获取链接配置失败,SettingCode = SyncPatientOrders");
                return false;
            }
            //responseUrlView.ApiUrl = "https://localhost:62288/api/SyncInpatient/SyncInpatientOrder";
            var logIds = await _syncDatasLogRepository.GetSyncDataByDataType(hospitalID, "PatientOrder");
            foreach (var logId in logIds)
            {
                var syncInfo = await _syncDatasLogRepository.GetSyncDataByID(logId);
                if (syncInfo == null || string.IsNullOrWhiteSpace(syncInfo.SyncData))
                {
                    continue;
                }
                var result = await SyncPatientOrdersAsyncNew(responseUrlView.ApiUrl, syncInfo.SyncData, syncInfo.EventName);
                await _syncDatasLogServices.ModifySyncDataLog(logId, result);
            }
            return true;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="coreUrl"></param>
        /// <param name="syncData"></param>
        /// <returns></returns>
        private async Task<bool> SyncPatientOrdersAsyncNew(string coreUrl, string syncData,string eventName)
        {
            try
            {
                var mQMessageView = ListToJson.ToList<MQMessageView>(syncData);
                var inPatientOrderView = ListToJson.ToList<InPatientOrderView>(mQMessageView.MessageData.ToString());
                if (inPatientOrderView == null)
                {
                    return false;
                }
                if (eventName != "SHZHY_HLCCC_regOrderOpenReview" || string.IsNullOrWhiteSpace(inPatientOrderView.ComboNo))
                {
                    return await SyncPatientOrderByAPI(coreUrl, inPatientOrderView.OrderID, mQMessageView.CaseNumber);
                }
                var hisPatientOrderView = await _hISPatientOrderRepository.GetByCasenumberAndComboNo(mQMessageView.CaseNumber, inPatientOrderView.ComboNo);
                var commonOrderViews = FormatPatientOrderByView(hisPatientOrderView);
                if (commonOrderViews == null || commonOrderViews.Count <= 0)
                {
                    return true;
                }
                // 发送数据
                //coreUrl = "https://localhost:62288/api/SyncInpatient/SyncInpatientOrder";
                HttpHelper.SendObjectAsJsonInBody(coreUrl, commonOrderViews);
            }
            catch (Exception ex)
            {
                _logger.Error($"方法:SyncPatientOrdersAsyncNew异常,syncData = {syncData},异常=>{ex}");
                return false;
            }
            return true;
        }
        /// <summary>
        /// 获取接口数据
        /// </summary>
        /// <param name="caseNumber"></param>
        /// <param name="comboNo"></param>
        /// <returns></returns>

        private async Task<bool> SyncPatientOrderByAPI(string coreUrl,string orderID, string caseNumber)
        {
            try
            {
               
                var (isSuccess, url, stationToDepts) = await GetcCallHisMethodsArg(caseNumber);
                if (!isSuccess)
                {
                    return false;
                }
                var url1 = $"{url}?inpatientNo={caseNumber}";
                string hisesult1 = await HttpHelper.HttpGetAsync(url1);
                if (string.IsNullOrWhiteSpace(hisesult1))
                {
                    _logger.Error($"方法SyncPatientOrderByAPI根据OrderID获取数据失败,OrderID为{orderID}");
                    return false;
                }
                var result = ListToJson.ToList<PatientOrderView>(hisesult1);
                if (result == null || result.data == null || result.data.Count == 0 ||  string.IsNullOrWhiteSpace(result.data[0].comboNo))
                {
                    _logger.Error($"方法SyncPatientOrderByAPI根据comboNo获取数据失败,comboNo为{orderID}");
                    return false;
                }
                var url2 = $"{url}?inpatientNo={caseNumber}&comboNo={result.data[0].comboNo}";
                string hisesult = await HttpHelper.HttpGetAsync(url2);
                var result2 = ListToJson.ToList<PatientOrderView>(hisesult);
                if (result2 == null || result2.data.Count == 0)
                {
                    _logger.Error($"序列化患者医嘱失败,患者无医嘱数据或医嘱数据为空,获取结果hisesult为:{hisesult}");
                    return false;
                }
                // 清洗数据
                var formatData = FormatPatientOrderByAPI(result.data, stationToDepts);
                //coreUrl = "https://localhost:62288/api/SyncInpatient/SyncInpatientOrder";
                HttpHelper.SendObjectAsJsonInBody(coreUrl, formatData);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error($"方法SyncPatientOrderByAPI同步数据失败,{ex}");
                return false;
            }
        }

        /// <summary>
        /// 获取同步患者医嘱数据参数
        /// </summary>
        /// <param name="caseNumber">患者流水号</param>
        /// <returns></returns>
        private async Task<(bool, string, List<StationToDepartmentInfo>)> GetcCallHisMethodsArg(string caseNumber)
        {
            var result = (false, string.Empty, new List<StationToDepartmentInfo>());
            if (string.IsNullOrWhiteSpace(caseNumber))
            {
                _logger.Error($"方法:[SyncPatientOrdersAsync]同步患者医嘱数据参数错误,参数caseNumber不可为空");
                return result;
            }
            string url = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, "GetPatientOrdersAdress");
            if (string.IsNullOrEmpty(url))
            {
                _logger.Error($"表appConfigSetting获取链接配置失败,SettingCode = GetPatientOrdersAdress");
                return result;
            }

            var stationToDepts = await _stationToDeptInfoRepository.GetAsync();
            if (stationToDepts.Count == 0)
            {
                _logger.Error("方法:[SyncPatientOrdersAsync]未获取到病区科室对照表信息");
                return result;
            }
            return (true, url, stationToDepts);
        }

        /// <summary>
        /// 根据视图进行数据清洗
        /// </summary>
        /// <param name="hISPatientOrderViews">HIS获取到的数据</param>
        /// <returns></returns>
        private static List<CommonOrderView> FormatPatientOrderByView(List<HISPatientOrderView> hISPatientOrderViews)
        {

            hISPatientOrderViews = hISPatientOrderViews.Where(m => m.MoState != "10").ToList();
            var commonOrderViews = new List<CommonOrderView>();
            foreach (var patientOrder in hISPatientOrderViews)
            {
                var orderView = new CommonOrderView
                {
                    CaseNumber = patientOrder.InpatientNo,
                    ChartNo = patientOrder.PatientNo,
                    OrderID = patientOrder.ComboNo,
                    OrderCode = patientOrder.TermId,
                    OrderContent = patientOrder.OrderName,
                    Frequency = patientOrder.FrequencyCode,
                    Unit = patientOrder.DoseUnit,
                    OrderDose = patientOrder.DoseOnce,
                    TotalVolume = patientOrder.QtyTot,
                    OrderRule = patientOrder.UsageName,
                    Location = STRING_EMPTY,
                    MethodCategory = STRING_EMPTY,
                    NumberOfExecution = STRING_EMPTY,
                    StartDate = patientOrder.BeginDate.ToString(),
                    EndDate = patientOrder.EndDate.ToString(),
                    AddEmployeeID = patientOrder.MoDoc,
                    AddDate = patientOrder.MoDate.ToString(),
                    ConfirmPersonID = patientOrder.ConfirmNurseCod,
                    CancalPersonID = patientOrder.CancalPersonID,
                    CancalDate = patientOrder.CancalDate.ToString(),
                    OrderPatternCode = patientOrder.TermClass,
                    OrderPattern = patientOrder.TermClassName,
                    OrderType = patientOrder.DecmpsFlag,
                    Note = patientOrder.OrderDescription?.Trim(),
                    StationCode = patientOrder.MoStation,
                    DeptCode = patientOrder.MoDept,
                    OrderGroupID = patientOrder.ComboNo,
                    HISOrderID = patientOrder.OrderId,
                    ConfirmDate = patientOrder.ConfirmDate.ToString()
                };
                if (patientOrder.TermClass == ORDERPATTERN_143)
                {
                    orderView.OrderPattern = "A";
                }
                if (ORDERPATTERNLIST.Contains(patientOrder.TermClass))
                {
                    orderView.OrderPattern = "B";
                }
                (orderView.OredrStatus, orderView.OredrStatusName) = GetOrderStatusAndName(patientOrder.MoState, patientOrder.CancelReason);
                commonOrderViews.Add(orderView);
            }
            return commonOrderViews.Where(m => !string.IsNullOrWhiteSpace(m.EndDate)).ToList();
        }

        /// <summary>
        /// 格式化医嘱数据
        /// </summary>
        /// <param name="patientOrders">医嘱集合</param>
        /// <param name="stationToDepts">病区对科室集合</param>
        /// <returns></returns>
        private static List<CommonOrderView> FormatPatientOrderByAPI(List<PatientOrder> patientOrders, List<StationToDepartmentInfo> stationToDepts)
        {
            patientOrders = patientOrders.Where(m => m.moState != "10").ToList();
            var commonOrderViews = new List<CommonOrderView>();
            foreach (var patientOrder in patientOrders)
            {
                var orderView = new CommonOrderView
                {
                    CaseNumber = patientOrder.inpatientNo,
                    ChartNo = patientOrder.patientNo,
                    OrderID = patientOrder.comboNo,
                    OrderCode = patientOrder.termId,
                    OrderContent = patientOrder.orderName,
                    Frequency = patientOrder.frequencyCode,
                    Unit = patientOrder.doseUnit,
                    OrderDose = patientOrder.doseOnce.ToString(),
                    TotalVolume = patientOrder.doseOnce.ToString(),
                    OrderRule = patientOrder.usageName,
                    Location = STRING_EMPTY,
                    MethodCategory = STRING_EMPTY,
                    NumberOfExecution = STRING_EMPTY,
                    StartDate = patientOrder.beginDate,
                    EndDate = patientOrder.cancelDate,
                    AddEmployeeID = patientOrder.moDoc,
                    AddDate = patientOrder.moDate,
                    ConfirmPersonID = patientOrder.confirmNurseCod,
                    CancalPersonID = patientOrder.cancelDoc,
                    CancalDate = patientOrder.cancelDate,
                    OrderPatternCode = patientOrder.termClass,
                    OrderPattern = patientOrder.termClassName,
                    OrderType = patientOrder.decmpsFlag,
                    Note = patientOrder.memo?.Trim(),
                    StationCode = stationToDepts.Find(m => m.DepartmentCode == patientOrder.moDept)?.StationCode,
                    DeptCode = patientOrder.moDept,
                    OrderGroupID = patientOrder.comboNo,
                    HISOrderID = patientOrder.orderId
                };
                if (patientOrder.termClass == ORDERPATTERN_143)
                {
                    orderView.OrderPattern = "A";
                }
                if (ORDERPATTERNLIST.Contains(patientOrder.termClass))
                {
                    orderView.OrderPattern = "B";
                }
                (orderView.OredrStatus, orderView.OredrStatusName) = GetOrderStatusAndName(patientOrder.moState, patientOrder.cancelReason);
                commonOrderViews.Add(orderView);
            }
            return commonOrderViews;
        }

        /// <summary>
        /// 根据HIS获取医嘱装药
        /// </summary>
        /// <param name="moState">医嘱状态</param>
        /// <param name="cancelReason">取消理由</param>
        /// <returns></returns>
        private static (int, string) GetOrderStatusAndName(string moState, string cancelReason)
        {
            Dictionary<string, string> orderStatus = new()
            {
                { "20", "已提交" },
                { "30", "已接收" },
                { "40", "已执行" },
                { "50", "已完成" },
            };
            if (moState == "90" && cancelReason == STOP_ORDER_STATUS_STOP)
            {
                return (3, "停止");
            }
            if (moState == "90" && cancelReason == STOP_ORDER_STATUS_CANCEL)
            {
                return (4, "作废");
            }
            return (2, orderStatus.TryGetValue(moState, out string value) ? value : "");
        }

        /// <summary>
        /// 同步患者医嘱数据
        /// </summary>
        /// <param name="caseNumber">患者流水号</param>
        /// <returns></returns>
        public async Task<bool> SyncPatientOrdersAsync(string caseNumber)
        {
            var responseUrlView = await _aPISettingRepository.GetAPIAddress("SyncPatientOrders");
            if (responseUrlView == null || string.IsNullOrEmpty(responseUrlView.ApiUrl))
            {
                _logger.Error($"表aPISetting获取链接配置失败,SettingCode = SyncPatientOrders");
                return false;
            }
            var (isSuccess, url, stationToDepts) = await GetcCallHisMethodsArg(caseNumber);
            if (!isSuccess)
            {
                return false;
            }
            url = $"{url}?inpatientNo={caseNumber}";
            string hisesult = await HttpHelper.HttpGetAsync(url);
            if (string.IsNullOrWhiteSpace(hisesult))
            {
                _logger.Error($"方法SyncPatientOrdersAsync根据OrderID获取数据失败,OrderID为{caseNumber}");
                return false;
            }
            var result = ListToJson.ToList<PatientOrderView>(hisesult);
            if (result == null || result.data == null || result.data.Count == 0 || string.IsNullOrWhiteSpace(result.data[0].comboNo))
            {
                _logger.Error($"方法SyncPatientOrdersAsync根据comboNo获取数据失败,caseNumber{caseNumber}");
                return false;
            }
            // 清洗数据
            var formatData = FormatPatientOrderByAPI(result.data, stationToDepts);
            //coreUrl = "https://localhost:62288/api/SyncInpatient/SyncInpatientOrder";
            HttpHelper.SendObjectAsJsonInBody(responseUrlView.ApiUrl, formatData);
            return true;
        }
    }
}
