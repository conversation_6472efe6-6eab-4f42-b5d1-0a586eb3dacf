﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using XianYangInterconnect.Common;
using XianYangInterconnect.Common.SessionCommon;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Data.Repository
{
    public class BedRepository : IBedRepository
    {
        private readonly DataOutContext _dataOutContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IOptions<SystemConfig> _config;
        private readonly GetCacheService _getCacheService;
        private readonly IMemoryCache _memoryCache;

        public BedRepository(DataOutContext dataOutContext
            , SessionCommonServer sessionCommonServer
            , IOptions<SystemConfig> config
            , GetCacheService getCacheService
            , IMemoryCache memoryCache)
        {
            _dataOutContext = dataOutContext;
            _sessionCommonServer = sessionCommonServer;
            _config = config;
            _getCacheService = getCacheService;
            _memoryCache = memoryCache;
        }

        public async Task<List<BedInfo>> GetAllAsync()
        {
            return await GetCacheAsync() as List<BedInfo>;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            return await _getCacheService.GetCacheMain<List<BedInfo>>(key, GetDataBaseListData);
        }
        private async Task<object> GetDataBaseListData(Dictionary<string, object> dict)
        {
            return await _dataOutContext.BedInfos.Where(m => m.DeleteFlag != "*").ToListAsync();
        }
        public string GetCacheType()
        {
            return CacheType.BedInfo.GetKey(_sessionCommonServer);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }
    }
}