﻿namespace MockPlatform.Models
{
    public class EmployeeCommonView
    {
        /// <summary>
        /// 员工姓名
        /// </summary>
        public string EmplCode { get; set; }
        /// <summary>
        /// 员工姓名
        /// </summary>
        public string EmplName { get; set; }
        /// <summary>
        /// 拼音码
        /// </summary>
        public string SpellCode { get; set; }
        /// <summary>
        /// 五笔
        /// </summary>
        public string WbCode { get; set; }
        /// <summary>
        /// 性别代码
        /// </summary>
        public string SexCode { get; set; }
        /// <summary>
        /// 姓别
        /// </summary>
        public string SexName { get; set; }
        /// <summary>
        /// 出生日期
        /// </summary>
        public string Birthday { get; set; }
        /// <summary>
        /// 职务代号代码
        /// </summary>
        public string PosiCode { get; set; }
        /// <summary>
        /// 职级代号
        /// </summary>
        public string LevlCode { get; set; }
        /// <summary>
        /// 身份证号
        /// </summary>
        public string Idenno { get; set; }
        /// <summary>
        /// 所属护理站
        /// </summary>
        public string NurseCellCode { get; set; }
        /// <summary>
        /// 人员类型代号
        /// </summary>
        public string EmplType { get; set; }
        /// <summary>
        /// 是否专家
        /// </summary>
        public string ExpertFlag { get; set; }
        /// <summary>
        /// 操作日期
        /// </summary>
        public DateTime OperDate { get; set; }
        /// <summary>
        /// 有效性标志 1 有效 0 停用 2 废弃
        /// </summary>
        public string ValidState { get; set; }
        /// <summary>
        /// 研究生名称
        /// </summary>
        public string GradName { get; set; }
        /// <summary>
        /// 教育编码
        /// </summary>
        public string EduCode { get; set; }
        /// <summary>
        /// 教育代码
        /// </summary>
        public string EducationCode { get; set; }
        /// <summary>
        /// 学历名称
        /// </summary>
        public string EduName { get; set; }
        /// <summary>
        /// 电话号码
        /// </summary>
        public string Tel { get; set; }
        /// <summary>
        /// 电子邮箱
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// 雇员证书号
        /// </summary>
        public string EmplicNo { get; set; }
        /// <summary>
        /// 操作员
        /// </summary>
        public string OperCode { get; set; }
        /// <summary>
        /// 操作名称
        /// </summary>
        public string OperName { get; set; }
        /// <summary>
        /// 家庭地址
        /// </summary>
        public string HomeAddr { get; set; }
        /// <summary>
        /// 工资ID
        /// </summary>
        public string SalaryId { get; set; }
    }
}
