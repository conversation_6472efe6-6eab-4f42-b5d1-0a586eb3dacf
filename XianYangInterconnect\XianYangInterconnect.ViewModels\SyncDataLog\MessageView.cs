﻿namespace XianYangInterconnect.ViewModels
{
    public class MessageView
    {
        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalId { get; set; }

        /// <summary>
        /// 消息流水号
        /// </summary>
        public string MessageID { get; set; }

        /// <summary>
        /// 获取消息时间
        /// </summary>
        public DateTime CreationTime { get; set; }
        /// <summary>
        /// 消息类别（患者信息、医嘱、手术、给药等）
        /// </summary>
        public string MessageType { get; set; }

        /// <summary>
        /// 事件名称
        /// </summary>
        public string EventName { get; set; }

        /// <summary>
        /// 消息信息
        /// </summary>
        public InpatientView InpatientView { get; set; }

        /// <summary>
        /// 医嘱数据
        /// </summary>
        public InPatientOrderView PatientOrderView { get; set; }
        /// <summary>
        /// 检验数据
        /// </summary>
        public PatientTestReportView PatientTestReportView { get; set; }
        /// <summary>
        /// 诊断数据
        /// </summary>
        public PatientDiagnosticView PatientDiagnosticView { get; set; }

        /// <summary>
        /// 过敏数据
        /// </summary>
        public PatientAllergyView patientAllergyView { get; set; }
        /// <summary>
        /// 患者评量表数据
        /// </summary>
        public ScoreInFo ScoreInFo { get; set; }
        /// <summary>
        /// 手术数据
        /// </summary>
        public OperationView PatientOperationView { get; set; }
        /// <summary>
        /// 获取转科信息
        /// </summary>
        public InpatientTransferView InpatientTransferView { get; set; }

        /// <summary>
        /// 获取医嘱执行信息
        /// </summary>
        public PatientOrderExecuteView PatientOrderExecuteView { get; set; }    

        /// <summary>
        /// 初始化
        /// </summary>
        public MessageView()
        {
            MessageID = "";
            MessageType = "";
            EventName = "";
        }
    }
    /// <summary>
    /// 诊断信息
    /// </summary>
    public class PatientDiagnosticView
    {
        /// <summary>
        /// 诊断id
        /// </summary>
        public string DiagnosticId { get; set; }
        /// <summary>
        /// 诊断描述
        /// </summary>
        public string diagnosticRecord { get; set; }
    }

    /// <summary>
    /// 过敏信息 
    /// </summary>
    public class PatientAllergyView
    {
        /// <summary>
        /// 过敏类型编码，过敏大类
        /// </summary>
        public string AllergyTypeCode { get; set; }
        /// <summary>
        /// 过敏类型名称，过敏大类
        /// </summary>
        public string AllergyTypeName { get; set; }
    }

    /// <summary>
    /// 医嘱信息
    /// </summary>
    public class InPatientOrderView
    {
        /// <summary>
        /// 医嘱ID、医嘱号 
        /// </summary>
        public string OrderID { get; set; }
        /// <summary>
        /// 组合号
        /// </summary>
        public string ComboNo { get; set; }
        /// <summary>
        /// 医嘱停止时间
        /// </summary>
        public string StopDateTime { get; set; }

        /// <summary>
        /// 医嘱预计停止时间
        /// </summary>
        public string ExpectStopDateTime { get; set; }

        /// <summary>
        /// 医嘱项目编码
        /// </summary>
        public string OrderCode { get; set; }
        /// <summary>
        /// 医嘱项目名称（术语）
        /// </summary>
        public string OrderName { get; set; }
        /// <summary>
        /// 医嘱项目名称
        /// </summary>

        public string OrderContent { get; set; }
        /// <summary>
        /// 医嘱大分类（OrderCodeSystem）药品、非药品编码
        /// </summary>
        public string OrderType { get; set; }
    }

    public class PatientTestReportView
    {
        /// <summary>
        /// 申请单号（realone医嘱号，多个用^分隔）
        /// </summary>
        public string RequestNo { get; set; }
        /// <summary>
        /// 报告编号
        /// </summary>
        public string ReportNo { get; set; }
    }

    /// <summary>
    /// 患者信息
    /// </summary>
    public class InpatientView
    {
        /// <summary>
        /// 住院唯一号
        /// </summary>
        public string CaseNumber { get; set; }

        /// <summary>
        /// 患者唯一号
        /// </summary>
        public string ChartNumber { get; set; }

        /// <summary>
        /// 患者门诊信息一号（历次门诊相同）
        /// </summary>
        public string OutpatientID { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime? OperationDateTime { get; set; }

        /// <summary>
        /// 操作者
        /// </summary>
        public string OperationEmployeeID { get; set; }
        /// <summary>
        /// 医院ID
        /// </summary>
        public string HospitalId { get; set; }
        
    }

    /// <summary>
    /// 患者评量表数据
    /// </summary>
    public class ScoreInFo
    {
        /// <summary>
        /// 住院唯一号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 评量表ID
        /// </summary>
        public string RecordId { get; set; }
        /// <summary>
        /// 评量表名称
        /// </summary>
        public string RecordName { get; set; }
        /// <summary>
        /// 评量表得分
        /// </summary>
        public string Point { get; set; }
    }
    public class OperationView
    {
        /// <summary>
        /// 住院唯一号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 手术申请
        /// </summary>
        public string OperationApplyNo { get; set; }
        /// <summary>
        /// 操作人员
        /// </summary>
        public string ModifyPersonID { get; set; }
        /// <summary>
        /// 操作时间
        /// </summary>
        public string ModifyDate { get; set; }
    }

    /// <summary>
    /// 患者转科信息
    /// </summary>
    public class InpatientTransferView
    {
        /// <summary>
        /// 转入病区Code
        /// </summary>
        public string TransferInStationCode { get; set; }
        /// <summary>
        ///转入病区名称
        /// </summary>
        public string TransferInStationName { get; set; }

        /// <summary>
        /// 转入科室Code
        /// </summary>
        public string TransferInDepartmentCode { get; set; }
        /// <summary>
        ///转入科室名称
        /// </summary>
        public string TransferInDepartmentName { get; set; }

        /// <summary>
        /// 转入病床Code
        /// </summary>
        public string TransferInBedCode { get; set; }

        /// <summary>
        /// 转入病床名称
        /// </summary>
        public string TransferInBedName { get; set; }

        /// <summary>
        /// 转入时间
        /// </summary>
        public DateTime? TransferInDatatime { get; set; }

        /// <summary>
        /// 转出病区Code
        /// </summary>
        public string TransferOutStationCode { get; set; }
        /// <summary>
        ///转出病区名称
        /// </summary>
        public string TransferOutStationName { get; set; }

        /// <summary>
        /// 转出科室Code
        /// </summary>
        public string TransferOutDepartmentCode { get; set; }
        /// <summary>
        ///转出科室名称
        /// </summary>
        public string TransferOutDepartmentName { get; set; }

        /// <summary>
        /// 转出病床Code
        /// </summary>
        public string TransferOutBedCode { get; set; }

        /// <summary>
        /// 转出病床名称
        /// </summary>
        public string TransferOutBedName { get; set; }

        /// <summary>
        /// 转出时间
        /// </summary>
        public DateTime? TransferOutDatatime { get; set; }

        /// <summary>
        /// 转出操作员编码
        /// </summary>
        public string TransferOutEmployeeID { get; set; }

        /// <summary>
        /// 转入操作员编码
        /// </summary>
        public string TransferInEmployeeID { get; set; }

    }
    /// <summary>
    /// 医嘱执行信息
    /// </summary>
    public class PatientOrderExecuteView
    {
        /// <summary>
        /// 住院唯一号
        /// </summary>
        public string CaseNumber { get; set; }
        /// <summary>
        /// 执行单号
        /// </summary>
        public string ExecuteFormNo { get; set; }

        /// <summary>
        /// 执行单流水号
        /// </summary>
        public string Execsqn { get; set; }
        /// <summary>
        /// 医嘱号
        /// </summary>
        public string OrderNo { get; set; }
        /// <summary>
        /// 医嘱组合号comboNo
        /// </summary>
        public string OrderGroupNo { get; set; }

        /// <summary>
        /// 应执行时间
        /// </summary>
        public string ExecuteTime { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public string OperationTime { get; set; }

        /// <summary>
        /// 操作员编码
        /// </summary>
        public string OperatorCode { get; set; }
        /// <summary>
        /// 医嘱开始时间 
        /// </summary>
        public string OrderStartDate { get; set; }
    }

}