﻿namespace XianYangInterconnect.ViewModels
{
    public class HISPatientMedicineScheduleView
    {
        /// <summary>
        ///
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 操作成功！
        /// </summary>
        public string Msg { get; set; }

        /// <summary>
        ///
        /// </summary>
        public List<HISPatientMedicineSchedule> Data { get; set; }
    }

    public class HISPatientMedicineSchedule
    {
        /// <summary>
        /// 是否婴儿医嘱
        /// </summary>
        public string BabyFlag { get; set; }

        /// <summary>
        /// 退费数量（触发器使用）
        /// </summary>
        public object BackNum { get; set; }

        /// <summary>
        /// 药品基本剂量
        /// </summary>
        public decimal BaseDose { get; set; }

        /// <summary>
        /// 作废原因
        /// </summary>
        public object CancelReson { get; set; }

        /// <summary>
        /// 记账时间
        /// </summary>
        public string ChargeDate { get; set; }

        /// <summary>
        /// 记账科室代码
        /// </summary>
        public string ChargeDeptcd { get; set; }

        /// <summary>
        /// 记账科室名称
        /// </summary>
        public string ChargeDeptName { get; set; }

        /// <summary>
        /// 记账标记
        /// </summary>
        public string ChargeFlag { get; set; }

        /// <summary>
        /// 收费发送单打印人;
        /// </summary>
        public object ChargePrintCode { get; set; }

        /// <summary>
        /// 收费发送单打印时间;
        /// </summary>
        public object ChargePrintDate { get; set; }

        /// <summary>
        /// 收费发送单打印标记;
        /// </summary>
        public string ChargePrintFlag { get; set; }

        /// <summary>
        /// 是否计费
        /// </summary>
        public string ChargeState { get; set; }

        /// <summary>
        /// 记账人代码
        /// </summary>
        public string ChargeUsercd { get; set; }

        /// <summary>
        /// 记账人名称
        /// </summary>
        public string ChargeUserName { get; set; }

        /// <summary>
        /// 巡回卡打印标记
        /// </summary>
        public string CircultPrnflag { get; set; }

        /// <summary>
        /// 组合序号
        /// </summary>
        public string CombNo { get; set; }

        /// <summary>
        /// 配液时间
        /// </summary>
        public object CompoundDate { get; set; }

        /// <summary>
        /// 配液科室
        /// </summary>
        public object CompoundDept { get; set; }

        /// <summary>
        /// 是否配液已执行 1 是 0 否
        /// </summary>
        public string CompoundExec { get; set; }

        /// <summary>
        /// 是否需配液 ‘1’ 是 0 否
        /// </summary>
        public string CompoundFlag { get; set; }

        /// <summary>
        /// 配液执行人
        /// </summary>
        public object CompoundOper { get; set; }

        /// <summary>
        /// 医嘱是否分解
        /// </summary>
        public string DecmpsState { get; set; }

        /// <summary>
        /// 分解时间
        /// </summary>
        public string DecoDate { get; set; }

        /// <summary>
        /// 住院科室代码
        /// </summary>
        public string DeptCode { get; set; }

        /// <summary>
        /// 医嘱医师代号
        /// </summary>
        public string DocCode { get; set; }

        /// <summary>
        /// 医嘱医师姓名
        /// </summary>
        public string DocName { get; set; }

        /// <summary>
        /// 剂型代码
        /// </summary>
        public string DoseModelCode { get; set; }

        /// <summary>
        /// 每次剂量
        /// </summary>
        public decimal DoseOnce { get; set; }

        /// <summary>
        /// 剂量单位
        /// </summary>
        public string DoseUnit { get; set; }

        /// <summary>
        /// 药品编码
        /// </summary>
        public string DrugCode { get; set; }

        /// <summary>
        /// 配药时间
        /// </summary>
        public string DrugedDate { get; set; }

        /// <summary>
        /// 配药科室代码
        /// </summary>
        public string DrugedDeptcd { get; set; }

        /// <summary>
        /// 配药科室名称
        /// </summary>
        public string DrugedDeptName { get; set; }

        /// <summary>
        /// 0不需发送/1集中发送/2分散发送/3已配药
        /// </summary>
        public string DrugedFlag { get; set; }

        /// <summary>
        /// 配药人员代码
        /// </summary>
        public string DrugedUsercd { get; set; }

        /// <summary>
        /// 配药人员名称
        /// </summary>
        public string DrugedUserName { get; set; }

        /// <summary>
        /// 药品名称
        /// </summary>
        public string DrugName { get; set; }

        /// <summary>
        /// 药品性质
        /// </summary>
        public string DrugQuality { get; set; }

        /// <summary>
        /// 1西药/2中成/3草
        /// </summary>
        public string DrugType { get; set; }

        /// <summary>
        /// 用法英文缩写
        /// </summary>
        public object EnglishAb { get; set; }

        /// <summary>
        /// 执行时间
        /// </summary>
        public string ExecDate { get; set; }

        /// <summary>
        /// 执行科室代码
        /// </summary>
        public string ExecDeptcd { get; set; }

        /// <summary>
        /// 执行科室名称
        /// </summary>
        public string ExecDeptName { get; set; }

        /// <summary>
        /// 执行科室
        /// </summary>
        public string ExecDpcd { get; set; }

        /// <summary>
        /// 执行科室名称
        /// </summary>
        public string ExecDpName { get; set; }

        /// <summary>
        /// 0待执行/1已
        /// </summary>
        public string ExecFlag { get; set; }

        public object ExecPrintCode { get; set; }
        public object ExecPrintDate { get; set; }
        public string ExecPrintFlag { get; set; }

        /// <summary>
        /// 执行单打印人员名称
        /// </summary>
        public object ExecPrintName { get; set; }

        /// <summary>
        /// 执行单流水号
        /// </summary>
        public string ExecSqn { get; set; }

        /// <summary>
        /// 执行护士代码
        /// </summary>
        public string ExecUsercd { get; set; }

        /// <summary>
        /// 执行护士名称
        /// </summary>
        public string ExecUserName { get; set; }

        /// <summary>
        /// 频次代码
        /// </summary>
        public string FrequencyCode { get; set; }

        /// <summary>
        /// 频次名称
        /// </summary>
        public string FrequencyName { get; set; }

        /// <summary>
        /// 婴儿序号
        /// </summary>
        public long HappenNo { get; set; }

        /// <summary>
        /// 住院流水号
        /// </summary>
        public string InpatientNo { get; set; }

        public object IsOwn { get; set; }

        /// <summary>
        /// 零售价
        /// </summary>
        public long ItemPrice { get; set; }

        /// <summary>
        /// 主药标记
        /// </summary>
        public string MainDrug { get; set; }

        /// <summary>
        /// 最小单位
        /// </summary>
        public string MinUnit { get; set; }

        /// <summary>
        /// 医嘱日期
        /// </summary>
        public string MoDate { get; set; }

        /// <summary>
        /// 开立科室代码
        /// </summary>
        public string MoDeptCode { get; set; }

        /// <summary>
        /// 开立科室名称
        /// </summary>
        public string MoDeptName { get; set; }

        /// <summary>
        /// 医嘱说明
        /// </summary>
        public object MoNote1 { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string MoNote2 { get; set; }

        /// <summary>
        /// 医嘱流水号
        /// </summary>
        public string MoOrder { get; set; }

        /// <summary>
        /// 是否需要确认
        /// </summary>
        public string NeedConfirm { get; set; }

        /// <summary>
        /// 药房是否配药
        /// </summary>
        public string NeedDrug { get; set; }

        /// <summary>
        /// 医嘱护理站代码
        /// </summary>
        public string NurseCellCode { get; set; }

        /// <summary>
        /// 包装数量
        /// </summary>
        public long PackQty { get; set; }

        /// <summary>
        /// 住院病历号
        /// </summary>
        public string PatientNo { get; set; }

        /// <summary>
        /// 取药药房
        /// </summary>
        public string PharmacyCode { get; set; }

        /// <summary>
        /// 取药药房名称
        /// </summary>
        public object PharmacyName { get; set; }

        /// <summary>
        /// 计价单位
        /// </summary>
        public string PriceUnit { get; set; }

        /// <summary>
        /// 打印日期
        /// </summary>
        public object PrnDate { get; set; }

        /// <summary>
        /// 打印科室代码
        /// </summary>
        public object PrnDeptcd { get; set; }

        /// <summary>
        /// 打印执行单
        /// </summary>
        public string PrnExelist { get; set; }

        /// <summary>
        /// 0未打印/1已打印
        /// </summary>
        public string PrnFlag { get; set; }

        /// <summary>
        /// 是否打印医嘱单
        /// </summary>
        public object PrnMorlist { get; set; }

        /// <summary>
        /// 打印人员代码
        /// </summary>
        public object PrnUsercd { get; set; }

        /// <summary>
        /// 医保：1、自费标识：0
        /// </summary>
        public object PubFlag { get; set; }

        /// <summary>
        /// 药品用量
        /// </summary>
        public decimal QtyTot { get; set; }

        /// <summary>
        /// 处方流水号
        /// </summary>
        public object RecipeNo { get; set; }

        /// <summary>
        /// 处方内流水号
        /// </summary>
        public long SequenceNo { get; set; }

        /// <summary>
        /// 配药单组别代码
        /// </summary>
        public object SetCode { get; set; }

        /// <summary>
        /// 项目属性
        /// </summary>
        public string SetItmattr { get; set; }

        /// <summary>
        /// 配药单号
        /// </summary>
        public object SetSeqn { get; set; }

        /// <summary>
        /// 是否包含附材
        /// </summary>
        public string SetSubtbl { get; set; }

        /// <summary>
        /// 双签第一个护士签名时间
        /// </summary>
        public object SignfirstDate { get; set; }

        /// <summary>
        /// 双签第一个护士编码
        /// </summary>
        public object SignfirstUserid { get; set; }

        /// <summary>
        /// 双签第一个护士名称
        /// </summary>
        public object SignfirstUsername { get; set; }

        /// <summary>
        /// 双签第二个护士签名时间
        /// </summary>
        public object SignsecondDate { get; set; }

        /// <summary>
        /// 双签第二个护士编码
        /// </summary>
        public object SignsecondUserid { get; set; }

        /// <summary>
        /// 双签第二个护士名称
        /// </summary>
        public object SignsecondUsername { get; set; }

        /// <summary>
        /// 排列序号
        /// </summary>
        public object SortId { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string Specs { get; set; }

        /// <summary>
        /// 1护士站常备/2扣药房
        /// </summary>
        public string StockMin { get; set; }

        /// <summary>
        /// 组合内的顺序号
        /// </summary>
        public object SubSortId { get; set; }

        /// <summary>
        /// 医嘱类别代码
        /// </summary>
        public string TypeCode { get; set; }

        /// <summary>
        /// 医嘱类别名称 BL:补录医嘱 ZL：嘱托临嘱 CZ：长期医嘱 ZC：嘱托长嘱  LZ：临时医嘱  SH：术后医嘱 CD:出院带药
        /// </summary>
        public string TypeName { get; set; }

        /// <summary>
        /// 用法代码
        /// </summary>
        public string UsageCode { get; set; }

        /// <summary>
        /// 付数
        /// </summary>
        public long UseDays { get; set; }

        /// <summary>
        /// 用法名称
        /// </summary>
        public string UseName { get; set; }

        /// <summary>
        /// 要求执行时间
        /// </summary>
        public string UseTime { get; set; }

        /// <summary>
        /// 作废时间
        /// </summary>
        public string ValidDate { get; set; }

        /// <summary>
        /// 1有效/0作废
        /// </summary>
        public string ValidFlag { get; set; }

        /// <summary>
        /// 作废人代码
        /// </summary>
        public string ValidUsercd { get; set; }

        /// <summary>
        /// 作废人名称
        /// </summary>
        public string ValidUserName { get; set; }
    }
}