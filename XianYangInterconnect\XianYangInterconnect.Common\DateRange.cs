﻿namespace XianYangInterconnect.Common
{
    public class DateRange
    {
        /// <summary>
        /// 取得日期范围
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <returns></returns>
        public static List<string> GetDateRange(DateTime startDate, DateTime endDate)
        {
            //资料区间
            List<string> dataRange = new List<string>();
            //計算時間區間
            TimeSpan timeSpan = endDate - startDate;
            int range = timeSpan.Days + 1;
            for (int i = 0; i < range; i++)
            {
                dataRange.Add(startDate.AddDays(i).ToString("yyyy-MM-dd"));
            }
            return dataRange;
        }
    }
}