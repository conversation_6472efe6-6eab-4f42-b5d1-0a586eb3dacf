﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using System.Reflection;
using System.Xml;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service.SyncBackHis
{
    public class SyncBackMedicationOrderExecuteService : SyncBackCommon, ISyncBackMedicationOrderExecuteService
    {

        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork<DataOutContext> _unitOfWorkOut;
        private readonly IInterconnectAppConfigSettingRepository _interconnectAppConfigSettingRepository;
        private readonly ISynchronizeLogService _synchronizeLogService;

        public SyncBackMedicationOrderExecuteService(
            IUnitOfWork<DataOutContext> unitOfWorkOut,
            IInterconnectAppConfigSettingRepository interconnectAppConfigSettingRepository,
            ISynchronizeLogService synchronizeLogService)
        {
            _unitOfWorkOut = unitOfWorkOut;
            _interconnectAppConfigSettingRepository = interconnectAppConfigSettingRepository;
            _synchronizeLogService = synchronizeLogService;
        }
        /// <summary>
        /// 回传路由配置SettingCode
        /// </summary>
        private const string REQUEST_SETTING_CODE = "SyncBackMedicationOrderExecute";
        /// <summary>
        /// 回传xml示例SettingCode
        /// </summary>
        private const string XML_DOCUMENT_SETTING_CODE = "SyncBackMedicationOrderExecuteXML";
        /// <summary>
        /// 缓存字段属性
        /// </summary>
        private static readonly PropertyInfo[] CachedProperties = typeof(SyncMedicationOrderExecuteView).GetProperties();
        /// <summary>
        /// 同步回传 注册医嘱执行事件
        /// </summary>
        /// <param name="orderExecuteView">医嘱执行记录信息</param>
        /// <returns></returns>
        public async Task<bool> SyncBackData(List<MedicationOrderExecuteView> orderExecuteViews)
        {
            var successFlag = true;
            foreach (var orderExecuteView in orderExecuteViews)
            {
                // 转换InterconnectCore中的数据转换为回传数据model类
                var syncOrderExecuteView = ConvertDataView(orderExecuteView);
                // 根据回传model类信息 构造xml格式的请求信息
                string message = await CreateRequestMessageAsync(syncOrderExecuteView);
                // 发送请求
                successFlag |= await SendRequest(message, syncOrderExecuteView, orderExecuteView.InpatientID, orderExecuteView.CaseNumber);
            }
            return successFlag;
        }
        /// <summary>
        /// 将InterconnectCore中的数据转换为回传数据model类
        /// </summary>
        /// <param name="orderExecuteView"></param>
        /// <returns></returns>
        private SyncMedicationOrderExecuteView ConvertDataView(MedicationOrderExecuteView orderExecuteView)
        {
            var (execStatusCode, execStatusName) = ConvertOrderStatusCode(orderExecuteView.StatusCode);
            var (operateDate, operatorCode) = GetOperationInfo(orderExecuteView, execStatusCode);
            return new SyncMedicationOrderExecuteView
            {
                HospitalCode = orderExecuteView.HospitalCode,
                HospitalName = orderExecuteView.HospitalName,
                ExecDeptCode = orderExecuteView.DepartmentCode,
                ExecDeptName = orderExecuteView.DepartmentName,
                ExecuteFormNo = orderExecuteView.PatientOrderDetailID,
                ComNo = orderExecuteView.PatientOrderMainID,
                Reason = orderExecuteView.CancelReason,
                VisitTypeCode = VISIT_TYPE_CODE,
                VisitTypeName = VISIT_TYPE_NAME,
                VisitSqNo = orderExecuteView.CaseNumber,
                VisitTimes = orderExecuteView.NumberOfAdmissions,
                OrderExecStatusCode = execStatusCode,
                OrderExecStatusName = execStatusName,
                RequestNo = orderExecuteView.PatientOrderDetailID,
                OperateDate = operateDate,
                OperatorCode = operatorCode,
            };
        }
        /// <summary>
        /// 获取操作人信息
        /// </summary>
        /// <param name="orderExecuteView">医嘱执行信息</param>
        /// <param name="execStatusCode">执行状态码</param>
        /// <returns></returns>
        private (string , string ) GetOperationInfo(MedicationOrderExecuteView orderExecuteView, string execStatusCode)
        {
            DateTime operationDate = orderExecuteView.AddDateTime;
            string operateEmployeeID = orderExecuteView.AddEmployeeID;
            if (execStatusCode == "65")
            {
                operationDate = orderExecuteView.PerformDate.HasValue ? orderExecuteView.PerformDate.Value : orderExecuteView.AddDateTime;
                if (!string.IsNullOrEmpty(orderExecuteView.PerformEmployeeID))
                {
                    operateEmployeeID = orderExecuteView.PerformEmployeeID;
                }
            }
            if (execStatusCode == "14")
            {
                operationDate = orderExecuteView.PrepareDate.HasValue ? orderExecuteView.PrepareDate.Value : orderExecuteView.AddDateTime;
                if (!string.IsNullOrEmpty(orderExecuteView.PrepareEmployeeID))
                {
                    operateEmployeeID = orderExecuteView.PrepareEmployeeID;
                }
            }
            return (operationDate.ToString("yyyyMMdd"), operateEmployeeID);
        }

        /// <summary>
        /// 药品医嘱执行状态码转换
        /// </summary>
        /// <param name="statusCode">CCC药品执行状态码</param>
        /// <returns></returns>
        private (string execStatusCode, string execStatusName) ConvertOrderStatusCode(string statusCode)
        {
            return statusCode switch
            {
                "40" => ("16", "药品送出"),
                "45" => ("31", "药品摆药"),
                "50" => ("32", "药品核收"),
                "55" => ("14", "配液"),
                "65" => ("22", "医嘱执行"),
                "70" => ("17", "输液巡回"),
                "80" => ("18", "输液结束"),
                _ => ("",""),
            };
        }

        /// <summary>
        /// 创建发送的请求信息
        /// </summary>
        /// <param name="syncOrderExecuteView"></param>
        /// <returns></returns>
        private async Task<string> CreateRequestMessageAsync(SyncMedicationOrderExecuteView syncOrderExecuteView)
        {
            var xmlMessage = await _interconnectAppConfigSettingRepository.
                GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, XML_DOCUMENT_SETTING_CODE);
            if (string.IsNullOrEmpty(xmlMessage))
            {
                _logger.Error($"回传患者生命体征数据的时候，发现xml示例为空，SettingCode=【{XML_DOCUMENT_SETTING_CODE}】");
                return null;
            }
            var (xmlDoc, xmlNamespaceManager) = XmlUtiles.LoadXml(xmlMessage, namespaceDict);
            xmlDoc = InitXmlHeader(xmlDoc, xmlNamespaceManager);
            var fieldValues = GetFieldValueDict(syncOrderExecuteView);
            // 查找业务节点 并替换值
            ReplaceBusinessNodes(xmlDoc, fieldValues, xmlNamespaceManager);
            var message = WebServiceClient.SerializeXmlDocument(xmlDoc);
            return message;
        }

        /// <summary>
        /// 初始化头信息
        /// </summary>
        /// <param name="xmlDoc">xml文档对象</param>
        /// <param name="xmlNamespaceManager">xml命名空间管理对象</param>
        /// <returns></returns>
        private XmlDocument InitXmlHeader(XmlDocument xmlDoc, XmlNamespaceManager xmlNamespaceManager)
        {
            string xpath = XmlUtiles.AddNamespacePrefixToXPath("POOR_IN200901UV/creationTime/@value", NAMESPACE_PREFIX);
            bool success = XmlUtiles.ReplaceSingleNodeValue(xmlDoc, xpath, DateTime.Now.ToString(SYNC_DATETIME_FORMAT), xmlNamespaceManager);
            if (!success)
            {
                _logger.Error("查找xml中creationTime节点失败，无法保存消息创建时间");
            }
            xpath = XmlUtiles.AddNamespacePrefixToXPath("POOR_IN200901UV/sender/device/id/item/@extension", NAMESPACE_PREFIX);
            success = XmlUtiles.ReplaceSingleNodeValue(xmlDoc, xpath, SENDER, xmlNamespaceManager);
            if (!success)
            {
                _logger.Error("查找xml中发送人信息节点失败，无法设置消息发送人");
            }
            xpath = XmlUtiles.AddNamespacePrefixToXPath("POOR_IN200901UV/receiver/device/id/item/@extension", NAMESPACE_PREFIX);
            success = XmlUtiles.ReplaceSingleNodeValue(xmlDoc, xpath, RECEIVER, xmlNamespaceManager);
            if (!success)
            {
                _logger.Error("查找xml中接受人信息节点失败，无法设置消息接受人");
            }
            return xmlDoc;
        }

        /// <summary>
        /// 发送请求，回传数据给his
        /// </summary>
        /// <param name="message">发送的消息</param>
        /// <param name="syncBackView">his需要的数据view</param>
        /// <param name="inpatientID">病人ID</param>
        /// <param name="caseNumber">住院流水号</param>
        /// <returns></returns>
        private async Task<bool> SendRequest(string message, SyncMedicationOrderExecuteView syncBackView, string inpatientID, string caseNumber)
        {
            var url = await _interconnectAppConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, REQUEST_SETTING_CODE);
            if (url == null)
            {
                _logger.Error($"回传his数据失败，请求地址没有配置，SettingCode ={REQUEST_SETTING_CODE}");
                return false;
            }
            //新增调用日志
            var syncLog = await _synchronizeLogService.CreateSynchronizeLogInfo(url, message, inpatientID, caseNumber, true);
            _unitOfWorkOut.SaveChanges();
            var result = await WebServiceClient.SendRequestMessageAsync(message, url, "createOrderExecuteEvent");
            if (result == null)
            {
                return false;
            }
            try
            {
                var medicationResponseXmlView = WebServiceClient.DeserializationXml<MedicationResponseXmlView>(result);
                if (medicationResponseXmlView.Body.Message.Acknowledgement.TypeCode == "AA"
                    || medicationResponseXmlView.Body.Message.Acknowledgement.AcknowledgementDetail.Text.Value == "成功" )
                {
                    syncLog.SuccessFlag = "*";
                    syncLog.ModifyDateTime = DateTime.Now;
                    syncLog.ModifyEmployeeID = "Interconnect";
                    syncLog.ResponseResult = result;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"反序列化返回结果失败,返回结果{result}，异常为：{ex}");
                return false;
            }
            return _unitOfWorkOut.SaveChanges() > 0;
        }

        /// <summary>
        /// 根据字段名和值批量替换对应的XML节点
        /// </summary>
        /// <param name="xmlDoc">XML文档对象</param>
        /// <param name="fieldValues">字段名 => 替换值</param>
        /// <param name="nsmgr">xml命名空间管理对象</param>
        /// <returns>替换失败结果字典：字段名 => 值</returns>
        public Dictionary<string, string> ReplaceBusinessNodes(XmlDocument xmlDoc, Dictionary<string, string> fieldValues, XmlNamespaceManager nsmgr)
        {
            var results = new Dictionary<string, string>();
            var fieldToXmlNode = GetFieldToXmlDict();
            foreach (var kvp in fieldValues)
            {
                string field = kvp.Key;
                string newValue = kvp.Value;
                if (fieldToXmlNode.TryGetValue(field, out string rawXPath))
                {
                    // 给节点路径中补充命名空间
                    string xpath = XmlUtiles.AddNamespacePrefixToXPath(rawXPath, NAMESPACE_PREFIX);
                    if (XmlUtiles.ReplaceSingleNodeValue(xmlDoc, xpath, newValue, nsmgr))
                    {
                        continue;
                    }
                }
                results[field] = newValue;
            }
            if (results.Count > 0)
            {
                _logger.Error($"XML 文件字段值替换失败变量：{ListToJson.ToJson(results)}");
            }
            return results;
        }
        #region 字段和xml节点对照表
        private Dictionary<string, string> GetFieldToXmlDict()
        {
            return new Dictionary<string, string>
            {
                { "HospitalCode", "POOR_IN200901UV/controlActProcess/authorOrPerformer/assignedDevice/representedOrganization/id/item/@extension" },
                { "HospitalName", "POOR_IN200901UV/controlActProcess/authorOrPerformer/assignedDevice/representedOrganization/name/item/part/@value" },
                { "HpAreaCode", "POOR_IN200901UV/controlActProcess/authorOrPerformer/assignedDevice/assignedDevice/asLocatedEntity/id/item/@extension" },
                { "HpAreaName", "POOR_IN200901UV/controlActProcess/authorOrPerformer/assignedDevice/assignedDevice/asLocatedEntity/location/name/item/part/@value" },
                { "OperateDate", "POOR_IN200901UV/controlActProcess/subject/placerGroup/transcriber/time/any/@value" },
                { "OperatorCode", "POOR_IN200901UV/controlActProcess/subject/placerGroup/transcriber/assignedEntity/id/item/@extension" },
                { "OperatorName", "POOR_IN200901UV/controlActProcess/subject/placerGroup/transcriber/assignedEntity/assignedPerson/name/item/part/@value" },
                { "ExecDeptCode", "POOR_IN200901UV/controlActProcess/subject/placerGroup/location/serviceDeliveryLocation/serviceProviderOrganization/id/item/@extension" },
                { "ExecDeptName", "POOR_IN200901UV/controlActProcess/subject/placerGroup/location/serviceDeliveryLocation/serviceProviderOrganization/name/item/part/@value" },
                { "OrderNo", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/id/item[@root='2.16.156.10011.2.5.1.31']/@extension" },
                { "RequestNo", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/id/item[@root='2.16.156.10011.1.24']/@extension" },
                { "OrderItemTypeCode", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/code/@code" },
                { "OrderItemTypeName", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/code/displayName/@value" },
                { "BarNo", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/specimen/specimen/id/@value" },
                { "CollectedDateTime", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/specimen/specimen/subjectOf1/specimenProcessStep/effectiveTime/any/@value" },
                { "CollectorCode", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/specimen/specimen/subjectOf1/specimenProcessStep/performer/assignedEntity/id/item/@extension" },
                { "CollectorName", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/specimen/specimen/subjectOf1/specimenProcessStep/performer/assignedEntity/assignedPerson/name/item/part/@value" },
                { "Reason", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/reason/observation/value/@value" },
                { "OrderExecStatusCode", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/component1/processStep/code/@code" },
                { "OrderExecStatusName", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/component1/processStep/code/displayName/@value" },
                { "VisitTimes", "POOR_IN200901UV/controlActProcess/subject/placerGroup/componentOf1/encounter/id/item[@root='2.16.156.10011.2.5.1.8']/@extension" },
                { "EmpiId", "POOR_IN200901UV/controlActProcess/subject/placerGroup/componentOf1/encounter/subject/patient/id/item[@root='2.16.156.10011.2.5.1.5']/@extension" },
                { "CardNo", "POOR_IN200901UV/controlActProcess/subject/placerGroup/componentOf1/encounter/subject/patient/id/item[@root='2.16.156.10011.2.5.1.4']/@extension" },
                { "VisitSqNo", "POOR_IN200901UV/controlActProcess/subject/placerGroup/componentOf1/encounter/id/item[@root='2.16.156.10011.2.5.1.9']/@extension" },
                { "VisitTypeCode", "POOR_IN200901UV/controlActProcess/subject/placerGroup/componentOf1/encounter/code/@code" },
                { "VisitTypeName", "POOR_IN200901UV/controlActProcess/subject/placerGroup/componentOf1/encounter/code/displayName/@value" },
                { "ExecuteFormNo", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/id/item[@root='2.16.156.10011.0.9.1.66']/@extension" },
                { "HisOrderNo", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/id/item[@root='2.16.156.10011.0.9.1.111']/@extension" },
                { "SkinTestResult", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/substanceAdministrationRequest/precondition/observationEventCriterion/value/@value" },
                { "ComNo", "POOR_IN200901UV/controlActProcess/subject/placerGroup/component2/observationRequest/id/item[@root='2.16.156.10011.0.9.1.51']/@extension" },
                { "OtherOperCode", "POOR_IN200901UV/controlActProcess/subject/placerGroup/verifier/assignedEntity/id/item/@extension" },
                { "OtherOperName", "POOR_IN200901UV/controlActProcess/subject/placerGroup/verifier/assignedEntity/assignedPerson/name/item/part/@value" },
                { "OtherOperTime", "POOR_IN200901UV/controlActProcess/subject/placerGroup/verifier/time/@value" },
                { "OtherDeptCode", "POOR_IN200901UV/controlActProcess/subject/placerGroup/verifier/assignedEntity/representedOrganization/id/item/@extension" },
                { "OtherDeptName", "POOR_IN200901UV/controlActProcess/subject/placerGroup/verifier/assignedEntity/representedOrganization/name/item/part/@value" }
            };
        }
        #endregion
        /// <summary>
        /// 从model实例中获取字段名和字段值字典
        /// </summary>
        /// <param name="syncOrderExecuteView">给药医嘱执行model实例</param>
        /// <returns></returns>
        private static Dictionary<string, string> GetFieldValueDict(SyncMedicationOrderExecuteView syncOrderExecuteView)
        {
            var result = new Dictionary<string, string>();
            foreach (var prop in CachedProperties)
            {
                var value = prop.GetValue(syncOrderExecuteView);
                var type = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
                bool isSimpleType = type.IsPrimitive ||
                                    type.IsEnum ||
                                    type == typeof(string) ||
                                    type == typeof(decimal) ||
                                    type == typeof(DateTime);
                if (!isSimpleType)
                { 
                    continue;
                }                
                if (value == null)
                {
                    result[prop.Name] = string.Empty;
                    continue;
                }
                if (type == typeof(DateTime))
                {
                    result[prop.Name] = ((DateTime)value).ToString("yyyyMMddHHmmss");
                    continue;
                }
                result[prop.Name] = value.ToString();                    
            }

            return result;
        }

    }
}
