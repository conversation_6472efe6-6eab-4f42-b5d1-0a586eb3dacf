﻿namespace XianYangInterconnect.Data.Interface
{
    public interface IRedisService
    {
        /// <summary>
        /// 取得緩存數據
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        Task<T> GetAsync<T>(string key);

        /// <summary>
        /// 根据Key，获取缓存，返回String
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        Task<string> GetAsync(string key);
        /// <summary>
        /// 获取或者新增缓存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="expirationSecondTime">多少秒后到期</param>
        /// <param name="datas"></param>
        /// <returns></returns>
        Task<T> GetOrCreateAsync<T>(string key, int expirationSecondTime, Func<Task<T>> datas);

        /// <summary>
        /// 获取或者写入缓存，传参Language
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="expirationSecondTime">多少秒后到期</param>
        /// <param name="language"></param>
        /// <param name="datas"></param>
        /// <returns></returns>
        Task<object> GetOrCreateAsync(string key, int expirationSecondTime, int language, Func<int, Task<object>> datas);

        /// <summary>
        /// 获取或者写入缓存，传参hospitalID
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="expirationSecondTime">多少秒后到期/param>
        /// <param name="hospitalID"></param>
        /// <param name="datas"></param>
        /// <returns></returns>
        Task<T> GetOrCreateAsync<T>(string key, int expirationSecondTime, string hospitalID, Func<string, Task<T>> datas);

        /// <summary>
        ///  获取或者写入缓存，传参Language,传参hospitalID
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="expirationSecondTime">多少秒后到期</param>
        /// <param name="hospitalID"></param>
        /// <param name="language"></param>
        /// <param name="datas"></param>
        /// <returns></returns>
        Task<T> GetOrCreateAsync<T>(string key, int expirationSecondTime, string hospitalID, int language, Func<string, int, Task<T>> datas);
        /// <summary>
        /// 增加緩存
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="key"></param>
        /// <param name="expirationSecondTimetalID">多少秒后到期</param>
        /// <param name="value"></param>
        /// <returns></returns>
        Task<bool> Add<T>(string key, int expirationSecondTimetalID, T value);

        /// <summary>
        /// 移除緩存
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        Task<bool> Remove(string key);

        /// <summary>
        /// 模糊查找Key,并且清除,all 清除所有缓存
        /// </summary>
        /// <param name="keyStr"></param>
        /// <returns></returns>

        Task<List<string>> RemoveCacheByFuzzyKey(string keyStr, string hospitalID);

        /// <summary>
        /// 更新緩存
        /// </summary>
        /// <param name="key"></param>
        /// <param name="expirationSecondTimeitalID">多少秒后到期</param>
        /// <param name="value"></param>
        /// <returns></returns>
        Task<bool> Update<T>(string key, int expirationSecondTimeitalID, T value);

        /// <summary>
        /// 验证是否存在
        /// </summary>
        /// <param name="key"></param>
        /// <returns></returns>
        Task<bool> Exists(string key);

        /// <summary>
        /// 获取所有缓存清单
        /// </summary>
        /// <returns></returns>
        List<string> GetDateBaseKeys();
    }
}
