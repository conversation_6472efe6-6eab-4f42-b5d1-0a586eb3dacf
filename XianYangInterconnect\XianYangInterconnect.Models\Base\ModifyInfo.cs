﻿using System.ComponentModel.DataAnnotations.Schema;

namespace XianYangInterconnect.Models.Base
{
    public class ModifyInfo : BaseInfo
    {
        /// <summary>
        /// 修改人员
        /// </summary>
        [Column(TypeName = "varchar(20)")]
        public string ModifyPersonID { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifyDate { get; set; }

        /// <summary>
        /// 删除标志 *表示删除
        /// </summary>
        [Column(TypeName = "varchar(1)")]
        public string DeleteFlag { get; set; } = "";

        public ModifyInfo Modify(string modifyEmployeeID)
        {
            ModifyDate = DateTime.Now;
            ModifyPersonID = modifyEmployeeID;
            return this;
        }

        /// <summary>
        /// 设置删除
        /// </summary>
        public void Delete(string modifyEmployeeID)
        {
            Modify(modifyEmployeeID);
            DeleteFlag = "*";
        }
    }
}