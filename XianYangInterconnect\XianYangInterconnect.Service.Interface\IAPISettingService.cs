﻿using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Services.Interface
{
    public interface IAPISettingService
    {
        /// <summary>
        /// 获取API地址
        /// </summary>
        /// <param name="settingCode"></param>
        /// <returns></returns>
        Task<ApiUrlView> GetAPIAddressByCode(string settingCode);

        /// <summary>
        /// 获取服务器地址
        /// </summary>
        /// <param name="serverCode">服务器码</param>
        /// <returns></returns>
        Task<string> GetServerURL(string serverCode);
    }
}
