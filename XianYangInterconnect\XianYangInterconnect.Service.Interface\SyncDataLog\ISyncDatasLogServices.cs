﻿
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Data.Interface
{
    public interface ISyncDatasLogServices
    {
        /// <summary>
        /// 新增同步数据记录
        /// </summary>
        /// <param name="syncDataLogView"></param>
        /// <returns></returns>
        Task<bool> SetSyncDataLog(SyncDataLogView syncDataLogView);
        /// <summary>
        /// 更新同步记录
        /// </summary>
        /// <param name="id"></param>
        /// <param name="syncFlag">同步结果</param>
        /// <returns></returns>
        Task<bool> ModifySyncDataLog(int id, bool syncResult);

        /// <summary>
        /// 打印接口数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<string> PrintSyncDataLog(int id);

    }
}
