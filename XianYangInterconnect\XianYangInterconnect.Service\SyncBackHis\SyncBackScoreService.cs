﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using System.Xml.Serialization;
using XianYangInterconnect.Common;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.Service.SyncBackHis;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.Service
{
    /// <summary>
    /// 回传注册患者风险评分数据
    /// </summary>
    public class SyncBackScoreService : ISyncBackScoreService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IUnitOfWork<DataOutContext> _unitOfWorkOut;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ISynchronizeLogService _synchronizeLogService;
        public SyncBackScoreService(
            IUnitOfWork<DataOutContext> unitOfWorkOut,
            IAppConfigSettingRepository appConfigSettingRepository,
            ISynchronizeLogService synchronizeLogService)
        {
            _unitOfWorkOut = unitOfWorkOut;
            _appConfigSettingRepository = appConfigSettingRepository;
            _synchronizeLogService = synchronizeLogService;
        }

        /// <summary>
        /// 就诊类型 01、02、03、04、05、09
        /// 1门诊 2急诊 3住院 4体检 5互联网 9其他
        /// </summary>
        private const string VISIT_TYPE_CODE = "03";
        private const string VISIT_TYPE_NAME = "住院";
        /// <summary>
        /// 配置类别
        /// </summary>
        private const string APPCONFIGSETTING_SETTINGTYPE_CONFIGS = "Configs";
        /// <summary>
        /// webservice请求中的action
        /// </summary>
        private const string ACTION = "createRiskAssessmentMsg";
        /// <summary>
        /// 回传风险评估数据的url配置的查询码 （AppConfigSetting）
        /// </summary>
        private const string REQUEST_SETTING_CODE = "SyncBackRiskAssessment";

        public async Task<bool> SyncBackData(SyncPatientScoreView patientScoreView)
        {
            var syncBackView = new PatientRiskAssessmentView
            {
                HospitalCode = patientScoreView.HospitalCode,
                HospitalName = patientScoreView.HospitalName,
                PatientName = patientScoreView.PatientName,
                VisitNo = patientScoreView.ChartNo,
                VisitSqNo = patientScoreView.CaseNumber,
                VisitTypeCode = VISIT_TYPE_CODE,
                VisitTypeName = VISIT_TYPE_NAME,
                BedNo = patientScoreView.BedNumber,
                HpAreaCode = patientScoreView.StationCode,
                HpAreaName = patientScoreView.StationName,
                OperCode = patientScoreView.AddEmployeeID,
                OperName = patientScoreView.AddEmployeeName,
                OperDeptCode = patientScoreView.DepartmentCode,
                OperDeptName = patientScoreView.DepartmentName,
                OperDate = patientScoreView.AssessDate.Add(patientScoreView.AssessTime).ToString("yyyyMMddHHmmss"),
                Times = patientScoreView.NumberOfAdmissions.ToString(),
                Remark = "",
                ValidFlag = patientScoreView.DeleteFlag != "*" ? "1" : "0",
            };
            syncBackView = SetFlagAndScore(patientScoreView, syncBackView);

            #region 发送对HIS的请求
            return await SendRequest(syncBackView, patientScoreView.InpatientID, patientScoreView.CaseNumber);
            #endregion
        }
        /// <summary>
        /// 发送请求，回传数据给his
        /// </summary>
        /// <param name="syncBackView"></param>
        /// <param name="inpatientID"></param>
        /// <param name="caseNumber"></param>
        /// <returns></returns>
        private async Task<bool> SendRequest( PatientRiskAssessmentView syncBackView, string inpatientID, string caseNumber)
        {
            var url = await _appConfigSettingRepository.GetConfigSettingValue(APPCONFIGSETTING_SETTINGTYPE_CONFIGS, REQUEST_SETTING_CODE);
            if (url == null)
            {
                _logger.Error($"回传his数据失败，请求地址没有配置，SettingCode ={REQUEST_SETTING_CODE}");
                return false;
            }
            //新增调用日志
            var syncLog = await _synchronizeLogService.CreateSynchronizeLogInfo(url, ListToJson.ToJson(syncBackView), inpatientID, caseNumber, true);
            _unitOfWorkOut.SaveChanges();
            // 构造回传的xml信息
            var message = CreateWebServiceRequestBody(syncBackView);
            // 调用请求
            var result = await WebServiceClient.SendRequestMessageAsync(message, url, ACTION);
            if (result == null)
            {
                return false;
            }
            try
            {
                var responseXmlView = WebServiceClient.DeserializationXml<ResponseXmlView>(result);
                //responseXmlView序列化还有问题，暂时补充判断
                if (responseXmlView.Body.Result?.ReturnCode == "AA" 
                    || responseXmlView.Body.Result?.ReturnMessage == "SUCCESS" || result.Contains("SUCCESS"))
                {
                    syncLog.SuccessFlag = "*";
                    syncLog.ModifyDateTime = DateTime.Now;
                    syncLog.ModifyEmployeeID = "Interconnect";
                    syncLog.ResponseResult = result;
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"反序列化返回结果失败,返回结果{result}，异常为：{ex}");
                return false;
            }
            return _unitOfWorkOut.SaveChanges() > 0;
        }

        /// <summary>
        /// 根据不同的风险类型设置对应的字段
        /// </summary>
        /// <param name="patientScoreView">请求传递的参数</param>
        /// <param name="syncBackView">组装的view</param>
        private static PatientRiskAssessmentView SetFlagAndScore(SyncPatientScoreView patientScoreView, PatientRiskAssessmentView syncBackView)
        {
            switch (patientScoreView.RecordListID)
            {
                case 37:// 跌倒
                case 52:
                case 90:
                case 103:
                case 122:
                    (syncBackView.FallFlag, syncBackView.FallScore) = (patientScoreView.ScoreRangeContent, patientScoreView.ScorePoint.ToString());
                    break;
                case 30:
                case 31:// 压疮
                case 58:
                case 129:
                    (syncBackView.SoresFlag, syncBackView.SoresScore) = (patientScoreView.ScoreRangeContent, patientScoreView.ScorePoint.ToString());
                    break;
                case 47:
                case 132:// 导管
                    (syncBackView.CatheterFlag, syncBackView.CatheterScore) = (patientScoreView.ScoreRangeContent, patientScoreView.ScorePoint.ToString());
                    break;
                case 44:// 血栓
                case 45:
                case 170:
                    (_, syncBackView.ThrombusScore) = (patientScoreView.ScoreRangeContent, patientScoreView.ScorePoint.ToString());
                    break;
                case 34:// 营养
                case 95:
                case 127:
                case 128:
                    (syncBackView.NutritionFlag, _) = (patientScoreView.ScoreRangeContent, patientScoreView.ScorePoint.ToString());
                    break;
                case 110:
                    (syncBackView.PainFlag, _) = (patientScoreView.ScoreRangeContent, patientScoreView.ScorePoint.ToString());
                    break;
                case 82:
                case 83:
                case 97:
                case 98:
                case 99:
                case 100:
                case 150:
                    (syncBackView.WarningFlag, _) = (patientScoreView.ScoreRangeContent, patientScoreView.ScorePoint.ToString());
                    break;
                default:
                    break;
            };
            return syncBackView;
        }
        #region 构建回传的数据
        /// <summary>
        /// 创建回传的消息
        /// </summary>
        /// <param name="syncBackView">风险相关信息</param>
        /// <returns></returns>
        string CreateWebServiceRequestBody(PatientRiskAssessmentView syncBackView)
        {
            // 创建风险评估消息
            var message = new RiskAssessmentMessageView
            {
                ITSVersion = "XML_1.0",
                SchemaLocation = "urn:hl7-org:v3 ../-multicacheschemas-/REPC_IN004014UV.xsd",
                MessageId = new Id
                {
                    Root = "2.16.156.10011.2.5.1.1",
                    Extension = "HI0218"
                },
                CreationTime = new CreationTime
                {
                    Value = DateTime.Now.ToString("yyyyMMddHHmmss")
                },
                InteractionId = new InteractionId
                {
                    Root = "2.16.156.10011.2.5.1.2",
                    Extension = "REPC_IN004014UV"
                },
                ProcessingCode = new ProcessingCode
                {
                    Code = "P"
                },
                ProcessingModeCode = new ProcessingModeCode(),
                AcceptAckCode = new AcceptAckCode
                {
                    Code = "AL"
                },
                Receiver = new Receiver
                {
                    TypeCode = "RCV",
                    Device = new Device
                    {
                        ClassCode = "DEV",
                        DeterminerCode = "INSTANCE",
                        Id = new IdContainer
                        {
                            Item = new Id
                            {
                                Root = "2.16.156.10011.2.5.1.3",
                                Extension = "IIB"
                            }
                        }
                    }
                },
                Sender = new Sender
                {
                    TypeCode = "SND",
                    Device = new Device
                    {
                        ClassCode = "DEV",
                        DeterminerCode = "INSTANCE",
                        Id = new IdContainer
                        {
                            Item = new Id
                            {
                                Root = "2.16.156.10011.2.5.1.3",
                                Extension = "SHZHY_HLCCC"
                            }
                        }
                    }
                },
                ControlActProcess = new ControlActProcess
                {
                    ClassCode = "CACT",
                    MoodCode = "EVN",
                    AuthorOrPerformer = SetAuthorOrPerformer(syncBackView),
                    Subject = new Subject
                    {
                        TypeCode = "SUBJ",
                        RegistrationEvent = new RegistrationEvent
                        {
                            ClassCode = "REG",
                            MoodCode = "EVN",
                            StatusCode = new StatusCode { Code = syncBackView.ValidFlag },
                            Custodian = new Custodian
                            {
                                TypeCode = "CST",
                                AssignedEntity = new AssignedEntity
                                {
                                    ClassCode = "ASSIGNED",
                                    Id = new Id()
                                }
                            },
                            Subject2 = new Subject2
                            {
                                TypeCode = "SUBJ",
                                CareProvisionEvent = new CareProvisionEvent
                                {
                                    ClassCode = "PCPR",
                                    MoodCode = "EVN",
                                    Author = new Author
                                    {
                                        TypeCode = "AUT",
                                        NoteText = new NoteText { Value = syncBackView.Remark },
                                        Time = new Time { Value = DateTime.Now.ToString("yyyyMMddHHmmss") },
                                        AssignedParty = new AssignedParty
                                        {
                                            ClassCode = "ASSIGNED",
                                            Id = new IdContainer
                                            {
                                                Item = new Id
                                                {
                                                    Root = "2.16.156.10011.1.4",
                                                    Extension = syncBackView.OperCode
                                                }
                                            },
                                            AssignedPerson = new AssignedPerson
                                            {
                                                ClassCode = "PSN",
                                                DeterminerCode = "INSTANCE",
                                                Name = new Name
                                                {
                                                    Type = "DSET_EN",
                                                    Item = new NameItem
                                                    {
                                                        Part = new Part { Value = syncBackView.OperName }
                                                    }
                                                }
                                            },
                                            RepresentedOrganization = new RepresentedOrganization
                                            {
                                                ClassCode = "ORG",
                                                DeterminerCode = "INSTANCE",
                                                Id = new IdContainer
                                                {
                                                    Item = new Id
                                                    {
                                                        Root = "2.16.156.10011.1.26",
                                                        Extension = syncBackView.OperDeptCode
                                                    }
                                                },
                                                Name = new Name
                                                {
                                                    Type = "DSET_EN",
                                                    Item = new NameItem
                                                    {
                                                        Part = new Part { Value = syncBackView.OperDeptName }
                                                    }
                                                }
                                            }
                                        }
                                    },
                                    PertinentInformation2 = SetPatientInfo(syncBackView),
                                    Component3 = SetRiskFlagAndScore(syncBackView)
                                }
                            }
                        }
                    }
                }
            };
            var namespaces = new XmlSerializerNamespaces();
            namespaces.Add("xsi", "http://www.w3.org/2001/XMLSchema-instance");
            string soapRequest = WebServiceClient.XmlSerializerMessage(message, namespaces);
            return soapRequest;
        }

        /// <summary>
        /// 设置执行人
        /// </summary>
        /// <param name="syncBackView"></param>
        /// <returns></returns>
        static AuthorOrPerformer SetAuthorOrPerformer(PatientRiskAssessmentView syncBackView)
        {
            return new AuthorOrPerformer
            {
                TypeCode = "AUT",
                AssignedDevice = new AssignedDevice
                {
                    ClassCode = "ASSIGNED",
                    AssignedDeviceInner = new AssignedDeviceInner
                    {
                        ClassCode = "DEV",
                        DeterminerCode = "INSTANCE",
                        AsLocatedEntity = new AsLocatedEntity
                        {
                            ClassCode = "LOCE",
                            Id = new IdContainer
                            {
                                Item = new Id
                                {
                                    Root = "2.16.156.10011.0.9.1.64",
                                    //院区编码
                                    Extension = ""
                                }
                            },
                            Location = new Location
                            {
                                ClassCode = "PLC",
                                DeterminerCode = "INSTANCE",
                                Name = new Name
                                {
                                    Type = "DSET_EN",
                                    Item = new NameItem
                                    {
                                        //院区名称
                                        Part = new Part { Value = "" }
                                    }
                                }
                            }
                        }
                    },
                    RepresentedOrganization = new RepresentedOrganization
                    {
                        ClassCode = "ORG",
                        DeterminerCode = "INSTANCE",
                        Id = new IdContainer
                        {
                            Item = new Id
                            {
                                Root = "2.16.156.10011.1.5",
                                Extension = syncBackView.HospitalCode
                            }
                        },
                        Name = new Name
                        {
                            Type = "DSET_EN",
                            Item = new NameItem
                            {
                                Part = new Part { Value = syncBackView.HospitalName }
                            }
                        }
                    }
                }
            };
        }

        /// <summary>
        ///  病人基本住院信息
        /// </summary>
        /// <param name="syncBackView"></param>
        /// <returns></returns>
        static PertinentInformation2 SetPatientInfo(PatientRiskAssessmentView syncBackView)
        {
            return new PertinentInformation2
            {
                TypeCode = "REFR",
                Encounter = new Encounter
                {
                    ClassCode = "ENC",
                    MoodCode = "EVN",
                    Id = new IdContainer
                    {
                        //诊疗流水号
                        Item = new Id
                        {
                            Extension = syncBackView.VisitSqNo,
                            Root = "2.16.156.10011.2.5.1.9"
                        }
                    },
                    Code = new Code
                    {
                        CodeSystem = "2.16.156.10011.2.3.1.271",
                        CodeValue = syncBackView.VisitTypeCode,
                        DisplayName = new DisplayName { Value = syncBackView.VisitTypeName }
                    },
                    // 入院次数
                    LengthOfStayQuantity = new LengthOfStayQuantity
                    {
                        Unit = "次",
                        Value = syncBackView.Times
                    },
                    // 患者信息
                    RecordTarget = new RecordTarget
                    {
                        TypeCode = "RCT",
                        Patient = new Patient
                        {
                            ClassCode = "PAT",
                            Id = new IdContainer
                            {
                                Item = new Id
                                {
                                    Root = "2.16.156.10011.0.9.1.55",
                                    Extension = syncBackView.VisitNo
                                }
                            },
                            StatusCode = new StatusCode(),
                            PatientPerson = new PatientPerson
                            {
                                Name = new Name
                                {
                                    Type = "DSET_EN",
                                    Item = new NameItem
                                    {
                                        Part = new Part { Value = syncBackView.PatientName }
                                    }
                                }
                            },
                            ProviderOrganization = new ProviderOrganization
                            {
                                ClassCode = "ORG",
                                DeterminerCode = "INSTANCE",
                                Id = new Id()
                            }
                        }
                    },
                    // 床号
                    Location = new EncounterLocation
                    {
                        TypeCode = "LOC",
                        HealthCareFacility = new HealthCareFacility
                        {
                            ClassCode = "SDLOC",
                            Id = new Id
                            {
                                Extension = syncBackView.BedNo,
                                Root = "2.16.156.10011.1.22"
                            }
                        }
                    }
                }
            };
        }
        /// <summary>
        /// 设置风险标识和风险评分
        /// </summary>
        /// <param name="syncBackView"></param>
        /// <returns></returns>
        private Component3 SetRiskFlagAndScore(PatientRiskAssessmentView syncBackView)
        {
            List<Component> components = [];

            var component = CreateFlagComponent(syncBackView);
            if (component != null)
            {
                components.Add(component);
            }
            component = CreateScoreComponent(syncBackView);
            if (component != null)
            {
                components.Add(component);
            }
            return new Component3
            {
                StatementCollectorActList = new StatementCollectorActList
                {
                    Components = components
                }
            };
        }
        /// <summary>
        /// 风险评分
        /// </summary>
        /// <param name="syncBackView"></param>
        /// <returns></returns>
        Component CreateScoreComponent(PatientRiskAssessmentView syncBackView)
        {
            List<OrganizerComponent> components = [];
            if (syncBackView.FallScore != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.015.00", "跌倒评分", syncBackView.PainFlag));
            }
            if (syncBackView.SoresScore != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.015.01", "压疮评分", syncBackView.PainFlag));
            }
            if (syncBackView.CatheterScore != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.015.02", "拔管评分", syncBackView.PainFlag));
            }
            if (syncBackView.ThrombusScore != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.015.03", "血栓评分", syncBackView.PainFlag));
            }
            return components.Count > 0 ? new Component
            {
                Organizer = new Organizer
                {
                    ClassCode = "CONTAINER",
                    MoodCode = "EVN",
                    Components = components
                }
            } : null;
        }
        /// <summary>
        /// 风险标识
        /// </summary>
        /// <param name="syncBackView"></param>
        /// <returns></returns>
        Component CreateFlagComponent(PatientRiskAssessmentView syncBackView)
        {
            List<OrganizerComponent> components = [];
            if (syncBackView.PainFlag != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.010.00", "疼痛风险标识", syncBackView.PainFlag));
            }
            if (syncBackView.TemperatureFlag != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.010.01", "体温风险标识", syncBackView.TemperatureFlag));
            }
            if (syncBackView.SoresFlag != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.010.02", "压疮风险标识", syncBackView.SoresFlag));
            }
            if (syncBackView.FallFlag != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.010.03", "跌倒风险标识", syncBackView.FallFlag));
            }
            if (syncBackView.NutritionFlag != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.010.04", "营养风险标识", syncBackView.NutritionFlag));
            }
            if (syncBackView.CatheterFlag != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.010.05", "导管风险标识", syncBackView.CatheterFlag));
            }
            if (syncBackView.WarningFlag != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.010.06", "预警风险标识", syncBackView.WarningFlag));
            }
            if (syncBackView.QuarantineFlag != null)
            {
                components.Add(CreateOrganizerComponent("CDE01.93.010.07", "隔离风险标识", syncBackView.QuarantineFlag));
            }
            return components.Count > 0 ? new Component
            {
                Organizer = new Organizer
                {
                    ClassCode = "CONTAINER",
                    MoodCode = "EVN",
                    Components = components
                }
            } : null;
        }
        /// <summary>
        /// 护理信息
        /// </summary>
        /// <param name="codeValue">编码</param>
        /// <param name="displayName">名称</param>
        /// <param name="painFlag">标识</param>
        /// <returns></returns>
        private OrganizerComponent CreateOrganizerComponent(string codeValue, string displayName, string painFlag)
        {
            return new OrganizerComponent
            {
                TypeCode = "COMP",
                Observation = new Observation
                {
                    ClassCode = "OBS",
                    MoodCode = "EVN",
                    Code = new Code
                    {
                        CodeValue = codeValue,
                        CodeSystem = "2.16.156.10011.0.9.2.2.1",
                        CodeSystemName = "扩展卫生信息数据元目录",
                        DisplayName = new DisplayName { Value = displayName }
                    },
                    Value = new Value
                    {
                        Type = "ST",
                        ValueContent = painFlag
                    }
                }
            };
        }
        #endregion
    }

}
