﻿using Microsoft.AspNetCore.Mvc.Filters;

namespace XianYangInterconnect.API.Extensions
{
    /// <summary>
    /// 无需验证
    /// </summary>
    public class NoAuthorizationAttribute : ActionFilterAttribute
    {
        /// <summary>
        ///
        /// </summary>
        /// <param name="filterContext"></param>
        public override void OnActionExecuting(ActionExecutingContext filterContext)
        {
            base.OnActionExecuting(filterContext);
        }
    }
}