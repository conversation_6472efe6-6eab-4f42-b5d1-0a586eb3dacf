<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Com.Neusoft.Nhip.MqProxy</name>
    </assembly>
    <members>
        <member name="M:Com.Neusoft.Nhip.AckService.CallServer(System.String,IBM.XMS.IMessage)">
            <summary>
            服务器调用
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.CfgHelper.Instance">
            <summary>
            全局实例对象
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.CfgHelper.IbmHostName">
            <summary>
            接口地址
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MQParameter.hostName">
            <summary>
            Name of the host on which WMQ Queue manager is running 
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MQParameter.port">
            <summary>
            Port number on which WMQ Queue manager is listening
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MQParameter.channelName">
            <summary>
            Name of the channel
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MQParameter.queueManagerName">
            <summary>
            Name of the WMQ Queue manager to connect to
            </summary>
        </member>
        <member name="T:Com.Neusoft.Nhip.MQProxy">
            <summary>
            代理IBM MQ 接口
            需要IBMMQ的客户端软件驱动支持
            </summary>
        </member>
        <member name="M:Com.Neusoft.Nhip.MQProxy.RegisterQueueListener(System.String,Com.Neusoft.Nhip.CommonListener.OnMessage)">
            <summary>
              注册需要监听的队列名及回调函数
              样例：
              public void OnMessage(String message){...}

              MQProxy.RegisterQueueListener("SHZHY_HLCCC_createMedicalVisit", this.OnMessage);
              MQProxy.RegisterQueueListener("SHZHY_HLCCC_updateMedicalVisit", this.OnMessage);
              MQProxy.RegisterQueueListener("SHZHY_HLCCC_createAdmissions", this.OnMessage);
              MQProxy.RegisterQueueListener("SHZHY_HLCCC_createTransferDeptOut", this.OnMessage);
              MQProxy.RegisterQueueListener("SHZHY_HLCCC_deleteTransferDeptOut", this.OnMessage);
              MQProxy.RegisterQueueListener("SHZHY_HLCCC_createTransferDeptIn", this.OnMessage);
              MQProxy.RegisterQueueListener("SHZHY_HLCCC_createTransferBed", this.OnMessage);
              MQProxy.RegisterQueueListener("SHZHY_HLCCC_createTransferDoctor", this.OnMessage);
              MQProxy.RegisterQueueListener("SHZHY_HLCCC_createDischargedInfo", this.OnMessage);
              MQProxy.RegisterQueueListener("SHZHY_HLCCC_deleteDischargedInfo", this.OnMessage);
              MQProxy.RegisterQueueListener("SHZHY_HLCCC_createNoFeeDischargedInfo", this.OnMessage);
            </summary>
        </member>
        <member name="M:Com.Neusoft.Nhip.MQProxy.StartListener">
            <summary>
            监控启动
            备注：需要在所有监听注册后进行启动
            </summary>
        </member>
        <member name="M:Com.Neusoft.Nhip.MQProxy.StopListener">
            <summary>
            监控停止
            备注：系统关闭的时候调用，关闭监听线程等
            </summary>
        </member>
        <member name="T:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper">
            <summary>
            Http连接操作帮助类
            </summary>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.GetHtml(Com.Neusoft.Nhip.MqProxy.Utils.HttpItem)">
            <summary>
            根据相传入的数据，得到相应页面数据
            </summary>
            <param name="item">参数类对象</param>
            <returns>返回HttpResult类型</returns>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.GetData(Com.Neusoft.Nhip.MqProxy.Utils.HttpItem,Com.Neusoft.Nhip.MqProxy.Utils.HttpResult)">
            <summary>
            获取数据的并解析的方法
            </summary>
            <param name="item"></param>
            <param name="result"></param>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.SetEncoding(Com.Neusoft.Nhip.MqProxy.Utils.HttpItem,Com.Neusoft.Nhip.MqProxy.Utils.HttpResult,System.Byte[])">
            <summary>
            设置编码
            </summary>
            <param name="item">HttpItem</param>
            <param name="result">HttpResult</param>
            <param name="ResponseByte">byte[]</param>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.GetByte">
            <summary>
            提取网页Byte
            </summary>
            <returns></returns>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.GetMemoryStream(System.IO.Stream)">
            <summary>
            4.0以下.net版本取数据使用
            </summary>
            <param name="streamResponse">流</param>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.SetRequest(Com.Neusoft.Nhip.MqProxy.Utils.HttpItem)">
             <summary>
             为请求准备参数
             </summary>
            <param name="item">参数列表</param>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.SetCer(Com.Neusoft.Nhip.MqProxy.Utils.HttpItem)">
            <summary>
            设置证书
            </summary>
            <param name="item"></param>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.SetCerList(Com.Neusoft.Nhip.MqProxy.Utils.HttpItem)">
            <summary>
            设置多个证书
            </summary>
            <param name="item"></param>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.SetCookie(Com.Neusoft.Nhip.MqProxy.Utils.HttpItem)">
            <summary>
            设置Cookie
            </summary>
            <param name="item">Http参数</param>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.SetPostData(Com.Neusoft.Nhip.MqProxy.Utils.HttpItem)">
            <summary>
            设置Post数据
            </summary>
            <param name="item">Http参数</param>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.SetProxy(Com.Neusoft.Nhip.MqProxy.Utils.HttpItem)">
            <summary>
            设置代理
            </summary>
            <param name="item">参数对象</param>
        </member>
        <member name="M:Com.Neusoft.Nhip.MqProxy.Utils.HttpHelper.CheckValidationResult(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            回调验证证书问题
            </summary>
            <param name="sender">流对象</param>
            <param name="certificate">证书</param>
            <param name="chain">X509Chain</param>
            <param name="errors">SslPolicyErrors</param>
            <returns>bool</returns>
        </member>
        <member name="T:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem">
            <summary>
            Http请求参考类
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem._MaximumAutomaticRedirections">
            <summary>
            设置请求将跟随的重定向的最大数目
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.URL">
            <summary>
            请求URL必须填写
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.Method">
            <summary>
            请求方式默认为GET方式,当为POST方式时必须设置Postdata的值
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.Timeout">
            <summary>
            默认请求超时时间
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.ReadWriteTimeout">
            <summary>
            默认写入Post数据超时间
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.KeepAlive">
            <summary>
             获取或设置一个值，该值指示是否与 Internet 资源建立持久性连接默认为true。
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.Accept">
            <summary>
            请求标头值 默认为text/html, application/xhtml+xml, */*
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.ContentType">
            <summary>
            请求返回类型默认 text/html
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.UserAgent">
            <summary>
            客户端访问信息默认Mozilla/5.0 (compatible; MSIE 9.0; Windows NT 6.1; Trident/5.0)
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.Encoding">
            <summary>
            返回数据编码默认为NUll,可以自动识别,一般为utf-8,gbk,gb2312
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.PostDataType">
            <summary>
            Post的数据类型
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.Postdata">
            <summary>
            Post请求时要发送的字符串Post数据
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.PostdataByte">
            <summary>
            Post请求时要发送的Byte类型的Post数据
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.WebProxy">
            <summary>
            设置代理对象，不想使用IE默认配置就设置为Null，而且不要设置ProxyIp
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.CookieCollection">
            <summary>
            Cookie对象集合
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.Cookie">
            <summary>
            请求时的Cookie
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.Referer">
            <summary>
            来源地址，上次访问地址
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.CerPath">
            <summary>
            证书绝对路径
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.IsToLower">
            <summary>
            是否设置为全文小写，默认为不转化
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.Allowautoredirect">
            <summary>
            支持跳转页面，查询结果将是跳转后的页面，默认是不跳转
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.Connectionlimit">
            <summary>
            最大连接数
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.ProxyUserName">
            <summary>
            代理Proxy 服务器用户名
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.ProxyPwd">
            <summary>
            代理 服务器密码
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.ProxyIp">
            <summary>
            代理 服务IP ,如果要使用IE代理就设置为ieproxy
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.ResultType">
            <summary>
            设置返回类型String和Byte
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.Header">
            <summary>
            header对象
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.ProtocolVersion">
            <summary>
            获取或设置用于请求的 HTTP 版本。返回结果:用于请求的 HTTP 版本。默认为 System.Net.HttpVersion.Version11。
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.Expect100Continue">
            <summary>
             获取或设置一个 System.Boolean 值，该值确定是否使用 100-Continue 行为。如果 POST 请求需要 100-Continue 响应，则为 true；否则为 false。默认值为 true。
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.ClentCertificates">
            <summary>
            设置509证书集合
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.PostEncoding">
            <summary>
            设置或获取Post参数编码,默认的为Default编码
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.ResultCookieType">
            <summary>
            Cookie返回类型,默认的是只返回字符串类型
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.ICredentials">
            <summary>
            获取或设置请求的身份验证信息。
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.MaximumAutomaticRedirections">
            <summary>
            设置请求将跟随的重定向的最大数目
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpItem.IfModifiedSince">
            <summary>
            获取和设置IfModifiedSince，默认为当前日期和时间
            </summary>
        </member>
        <member name="T:Com.Neusoft.Nhip.MqProxy.Utils.HttpResult">
            <summary>
            Http返回参数类
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpResult.Cookie">
            <summary>
            Http请求返回的Cookie
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpResult.CookieCollection">
            <summary>
            Cookie对象集合
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpResult.Html">
            <summary>
            返回的String类型数据 只有ResultType.String时才返回数据，其它情况为空
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpResult.ResultByte">
            <summary>
            返回的Byte数组 只有ResultType.Byte时才返回数据，其它情况为空
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpResult.Header">
            <summary>
            header对象
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpResult.StatusDescription">
            <summary>
            返回状态说明
            </summary>
        </member>
        <member name="P:Com.Neusoft.Nhip.MqProxy.Utils.HttpResult.StatusCode">
            <summary>
            返回状态码,默认为OK
            </summary>
        </member>
        <member name="T:Com.Neusoft.Nhip.MqProxy.Utils.ResultType">
            <summary>
            返回类型
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MqProxy.Utils.ResultType.String">
            <summary>
            表示只返回字符串 只有Html有数据
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MqProxy.Utils.ResultType.Byte">
            <summary>
            表示返回字符串和字节流 ResultByte和Html都有数据返回
            </summary>
        </member>
        <member name="T:Com.Neusoft.Nhip.MqProxy.Utils.PostDataType">
            <summary>
            Post的数据格式默认为string
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MqProxy.Utils.PostDataType.String">
            <summary>
            字符串类型，这时编码Encoding可不设置
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MqProxy.Utils.PostDataType.Byte">
            <summary>
            Byte类型，需要设置PostdataByte参数的值编码Encoding可设置为空
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MqProxy.Utils.PostDataType.FilePath">
            <summary>
            传文件，Postdata必须设置为文件的绝对路径，必须设置Encoding的值
            </summary>
        </member>
        <member name="T:Com.Neusoft.Nhip.MqProxy.Utils.ResultCookieType">
            <summary>
            Cookie返回类型
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MqProxy.Utils.ResultCookieType.String">
            <summary>
            只返回字符串类型的Cookie
            </summary>
        </member>
        <member name="F:Com.Neusoft.Nhip.MqProxy.Utils.ResultCookieType.CookieCollection">
            <summary>
            CookieCollection格式的Cookie集合同时也返回String类型的cookie
            </summary>
        </member>
    </members>
</doc>
