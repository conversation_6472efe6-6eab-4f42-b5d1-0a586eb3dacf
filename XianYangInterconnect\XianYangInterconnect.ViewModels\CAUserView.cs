﻿namespace XianYangInterconnect.ViewModels
{
    public class CAUserView
    {
        /// <summary>
        /// 用户表业务主键
        /// </summary>
        public string id { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        public string username { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public string department { get; set; }

        /// <summary>
        /// 证件类型
        /// </summary>
        public string idType { get; set; }

        /// <summary>
        /// 证件号
        /// </summary>
        public string idCard { get; set; }

        /// <summary>
        /// 手机
        /// </summary>
        public string mobilePhone { get; set; }

        /// <summary>
        /// 固定电话
        /// </summary>
        public string telephone { get; set; }

        /// <summary>
        /// 邮件
        /// </summary>
        public string email { get; set; }

        /// <summary>
        /// 工号
        /// </summary>
        public string jobNumber { get; set; }

        /// <summary>
        /// 人脸 id
        /// </summary>
        public string faceId { get; set; }

        /// <summary>
        /// 签章 id
        /// </summary>
        public string signpicId { get; set; }

        /// <summary>
        /// 用户 id
        /// </summary>
        public string msspId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string createTime { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public string updateTime { get; set; }

        /// <summary>
        /// 是否有证书（有 Y，无 N）
        /// </summary>
        public string requireCert { get; set; }

        /// <summary>
        /// 冗余字段作为无证书用户的登录密码
        /// </summary>
        public string perserve1 { get; set; }

        /// <summary>
        /// 冗余字段作为默认签章标识（默认为 false,非默 认为 true）
        /// </summary>
        public string perserve2 { get; set; }
    }
}