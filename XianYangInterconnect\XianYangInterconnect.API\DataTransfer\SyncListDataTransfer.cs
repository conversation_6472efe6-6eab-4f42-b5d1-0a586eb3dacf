﻿using NLog;
using XianYangInterconnect.Common;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.API
{
    public static class SyncListDataTransfer
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();

        /// <summary>
        /// 数据转换
        /// </summary>
        /// <param name="dataJson"></param>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public static SyncDataLogView CreateSyncDataLogView(object dataJson, string hospitalID)
        {
            var syncDataLogView = new SyncDataLogView()
            {
                HospitalID = hospitalID,
                SyncData = dataJson
            };
            var messageView = new ViewModels.Message.MessageMainView();
            //尝试解析Json1
            try
            {
                messageView = ListToJson.ToList<ViewModels.Message.MessageMainView>(dataJson.ToString());
            }
            catch (Exception ex)
            {
                _logger.Error("Json 解析失败" + ex.ToString());
                return syncDataLogView;
            }
            syncDataLogView.SyncDataType = messageView.MessageType;
            syncDataLogView.EventName = messageView.EventName;
            if (messageView.ViewType == "2")
            {
                syncDataLogView.CaseNumber = messageView.CaseNumber;
            }
            else
            {
                syncDataLogView.HospitalID = messageView.InpatientView.HospitalId ?? hospitalID;
                syncDataLogView.CaseNumber = messageView.InpatientView.CaseNumber;
            }
          
            return syncDataLogView;
        }
    }
}
