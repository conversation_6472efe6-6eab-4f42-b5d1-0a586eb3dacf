# 血液检查数据同步功能说明

## 概述

本功能实现了从HIS系统获取血液检查数据，并将其转换为InterconnectCore系统所需的格式进行保存的完整流程。

## 新增文件

### 1. 视图模型 (ViewModels)

#### HisBloodInspectView.cs
- **HisBloodInspectView**: HIS血液检查数据的视图模型
- **HisBloodInspectListView**: 血液检查数据列表响应模型
- **HisBloodInspectResponse**: HIS API响应根对象

主要字段：
- `CaseNumber`: 病案号
- `BloodNumber`: 血袋号
- `SourceUniqueID`: 输血监测数据唯一ID
- `PerformEmployeeID`: 执行人ID
- `PerformDateTime`: 执行时间
- `Temperature`: 体温
- `HeartRate`: 心率
- `Breathing`: 呼吸
- `BloodPressure`: 血压
- `NursingObservation`: 护理观察

#### InterconnectCoreBloodInspectInputView.cs
- **InterconnectCoreBloodInspectInputView**: 输血监测数据输入视图模型

主要功能：
- 包含动态键值对字段 `KeyValueItems`
- 提供 `SetKeyValue` 方法进行字段映射
- 提供 `FromHisData` 和 `FromHisDataList` 静态方法进行数据转换

字段映射规则：
```csharp
"EMRSource_72" => 72   // 体温
"EMRSource_74" => 74   // 心率
"EMRSource_163" => 163 // 呼吸
"EMRSource_164" => 164 // 血压
"EMRSource_168" => 168 // 执行输血巡视时记录护理观察
```

### 2. 服务实现 (Service)

#### SyncBloodInspectService.cs
实现了 `ISyncBloodInspectService` 接口，提供血液检查数据同步功能。

主要方法：
- `SyncBloodInspectRecordDetail`: 主要同步方法
- `GetHisBloodInspectData`: 获取HIS血液检查数据
- `ProcessHisDataToInputData`: 处理数据转换
- `SaveBloodInspectData`: 保存数据到InterconnectCore

#### SyncBloodInspectServiceTest.cs
测试类，用于验证数据转换和处理逻辑。

测试方法：
- `TestDataConversion`: 测试HIS数据转换
- `TestFieldMapping`: 测试字段映射

## 配置要求

### API配置
需要在 `ApiSetting` 表中配置以下接口：

1. **GetHisBloodInspectData**: 获取HIS血液检查数据的接口
   - 用于从HIS系统获取血液检查数据
   - 参数格式：JSON，包含 StartDateTime, EndDateTime, PageIndex, PageSize

2. **SaveBloodInspectData**: 保存血液检查数据到InterconnectCore的接口
   - 用于将转换后的数据保存到InterconnectCore系统
   - 参数格式：JSON数组，包含 InterconnectCoreBloodInspectInputView 对象列表

### 依赖注入
服务已通过 Autofac 自动注册，无需额外配置。

## 使用方法

### 1. 手动调用API
```http
POST /api/SyncData/SyncBloodInspectRecordDetail
```

### 2. 程序调用
```csharp
// 注入服务
private readonly ISyncBloodInspectService _syncBloodInspectService;

// 调用同步方法
var result = await _syncBloodInspectService.SyncBloodInspectRecordDetail(startDateTime, endDateTime);
```

### 3. 测试数据转换
```csharp
// 运行测试
SyncBloodInspectServiceTest.TestDataConversion();
SyncBloodInspectServiceTest.TestFieldMapping();
```

## 数据流程

1. **获取数据**: 从HIS系统获取血液检查数据
2. **数据验证**: 验证必要字段（病案号、执行人ID等）
3. **数据转换**: 将HIS数据转换为InterconnectCore格式
4. **字段映射**: 将具体字段映射到动态键值对
5. **保存数据**: 调用InterconnectCore接口保存数据

## 日志记录

服务使用 NLog 进行日志记录，包括：
- 同步开始和结束信息
- 数据获取和转换过程
- 错误和异常信息
- 调试信息

## 错误处理

- 网络异常：自动重试机制（由HttpHelper处理）
- 数据验证失败：记录警告日志，跳过无效数据
- 转换异常：记录错误日志，返回失败状态
- 保存异常：记录错误日志，返回失败状态

## 注意事项

1. **API配置**: 确保在 ApiSetting 表中正确配置了相关接口地址
2. **字段映射**: 如需添加新的字段映射，请修改 `MapEmrKey` 方法
3. **数据验证**: 可根据业务需求调整数据验证规则
4. **性能优化**: 大量数据时可考虑分批处理
5. **错误监控**: 建议配置日志监控，及时发现同步异常

## 扩展说明

如需扩展功能，可以：
1. 在 `HisBloodInspectView` 中添加新字段
2. 在 `MapEmrKey` 方法中添加新的字段映射
3. 在 `ProcessHisDataToInputData` 方法中添加新的数据处理逻辑
4. 添加新的验证规则和业务逻辑
