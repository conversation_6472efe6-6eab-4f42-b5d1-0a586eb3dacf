﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using XianYangInterconnect.Common;
using XianYangInterconnect.Common.SessionCommon;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface;
using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Repository
{
    public class StationToDeptInfoRepository : IStationToDeptInfoRepository
    {
        private readonly MedicalContext _medicalContext;
        private readonly IMemoryCache _memoryCache;
        private readonly SessionCommonServer _sessionCommonServer;

        public StationToDeptInfoRepository(
            MedicalContext medicalContext
            , IMemoryCache memoryCache
            , SessionCommonServer sessionCommonServer
            )
        {
            _medicalContext = medicalContext;
            _memoryCache = memoryCache;
            _sessionCommonServer = sessionCommonServer;
        }
        /// <summary>
        /// 获取病区科室数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<StationToDepartmentInfo>> GetAsync()
        {
            return (List<StationToDepartmentInfo>)await GetCacheAsync();
        }
        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await this._memoryCache.GetOrCreateAsync(key, async entry =>
            {
                entry.SetAbsoluteExpiration(TimeSpan.FromSeconds(36000));
                return await _medicalContext.StationToDepartmentInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
            });
            return datas;
        }

        public void UpdateAsync()
        {
            string key = GetCacheType();
            _memoryCache.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.StationToDepartment.GetKey(_sessionCommonServer);
        }
    }
}
