﻿using Microsoft.EntityFrameworkCore;
using XianYangInterconnect.Data.Context;
using XianYangInterconnect.Data.Interface.Medical;
using XianYangInterconnect.Models;

namespace XianYangInterconnect.Data.Repository.Medical
{
    public class DepartmentListRepository : IDepartmentListRepository
    {
        private readonly MedicalContext _medicalContext = null;
        public DepartmentListRepository(
            MedicalContext medicalContext
            )
        {
            _medicalContext = medicalContext;
        }
        /// <summary>
        /// 最大ID
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<int>> GetDeptListMaxID(string hospitalID)
        {
            return await _medicalContext.DepartmentListInfos.Where(m => m.HospitalID == hospitalID).Select(m => m.ID).ToListAsync();
        }
        /// <summary>
        /// 获取所有科室
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<List<DepartmentListInfo>> GetDepartmentList(string hospitalID)
        {
            return await _medicalContext.DepartmentListInfos.Where(m => m.HospitalID == hospitalID&&m.DeleteFlag!="*").ToListAsync();
        }
    }

}
