﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using XianYangInterconnect.API.Extensions;
using XianYangInterconnect.Common;
using XianYangInterconnect.Service.Interface;
using XianYangInterconnect.ViewModels;

namespace XianYangInterconnect.API.Controllers
{
    /// <summary>
    /// 数据回传接口清单
    /// </summary>
    [Produces("application/json")]
    [Route("api/SyncBackHIS")]
    [EnableCors("any")]
    public class SyncBackHisController : Controller
    {
        private readonly ISyncBackScoreService _syncBackScoreService;
        private readonly ISyncBackBodySurfaceService _syncBackBodySurfaceService;
        private readonly ISyncBackSkinTestService _syncBackSkinTestService;
        private readonly ISyncBackMedicationOrderExecuteService _syncBackMedicationOrderExecuteService;
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="syncBackScoreService">回传风险service</param>
        /// <param name="syncBackBodySurfaceService">生命体征数据</param>
        /// <param name="syncBackSkinTestService">回传皮试service</param>
        /// <param name="syncBackMedicationOrderExecuteService">回传医嘱执行闭环状态service</param>
        public SyncBackHisController(ISyncBackScoreService syncBackScoreService
            , ISyncBackBodySurfaceService syncBackBodySurfaceService
            , ISyncBackSkinTestService syncBackSkinTestService,
            ISyncBackMedicationOrderExecuteService syncBackMedicationOrderExecuteService)
        {
            _syncBackScoreService = syncBackScoreService;
            _syncBackBodySurfaceService = syncBackBodySurfaceService;
            _syncBackSkinTestService = syncBackSkinTestService;
            _syncBackMedicationOrderExecuteService = syncBackMedicationOrderExecuteService;
        }

        /// <summary>
        ///  同步回传风险调用参数
        /// </summary>
        /// <param name="syncPatientScoreView">风险相关数据view</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncBackPatientScore")]
        [NoAuthorization]
        public async Task<IActionResult> SyncBackPatientScore([FromBody] SyncPatientScoreView syncPatientScoreView)
        {
            var result = new ResponseResult
            {
                Data = await _syncBackScoreService.SyncBackData(syncPatientScoreView)
            };
            return result.ToJson();
        }
        /// <summary>
        /// 同步回传声明体征数据
        /// </summary>
        /// <param name="vitalSignsView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncBackBodySurface")]
        [NoAuthorization]
        public async Task<IActionResult> SyncBackBodySurface([FromBody] VitalSignsView vitalSignsView)
        {
            var result = new ResponseResult
            {
                Data = await _syncBackBodySurfaceService.SyncBackData(vitalSignsView)
            };
            return result.ToJson();
        }
        /// <summary>
        ///  同步回传皮试调用参数
        /// </summary>
        /// <param name="dataView">皮试相关数据view</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncBackPatientMedicineScheduleSkinTest")]
        [NoAuthorization]
        public async Task<IActionResult> SyncBackPatientMedicineScheduleSkinTest([FromBody] SyncPatientMedicineScheduleSkinTestView dataView)
        {
            var result = new ResponseResult
            {
                Data = await _syncBackSkinTestService.SyncBackPatientMedicineScheduleSkinTestData(dataView)
            };
            return result.ToJson();
        }

        /// <summary>
        ///  同步回传 注册医嘱执行事件
        /// </summary>
        /// <param name="dataView">给药医嘱执行记录信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SyncBackMedicationOrderExecute")]
        [NoAuthorization]
        public async Task<IActionResult> SyncBackMedicationOrderExecute([FromBody] List<MedicationOrderExecuteView> dataView)
        {
            var result = new ResponseResult
            {
                Data = await _syncBackMedicationOrderExecuteService.SyncBackData(dataView)
            };
            return result.ToJson();
        }
    }
}
